package main

import (
	"flag"
	"fmt"
	"gateway/internal/cache"
	"gateway/internal/clash"
	"gateway/internal/config"
	"gateway/internal/db"
	_interface "gateway/internal/interface"
	"gateway/internal/jumpChange"
	"gateway/internal/log"
	"gateway/internal/platformRequest"
	"gateway/internal/status"
	"gateway/internal/sub"
	"gateway/internal/utils"
	"gateway/web"
	"os"
	"os/signal"
	"path/filepath"
	"runtime"
	"syscall"
	"time"

	"github.com/metacubex/mihomo/hub/executor"
)

var (
	version    bool
	configPath string
)

func init() {
	defaultConfigPath := filepath.Join(utils.GetEtcDir(), "config-local.json")
	flag.BoolVar(&version, "v", false, "show current version")
	flag.StringVar(&configPath, "c", defaultConfigPath, "config file path")
	flag.Parse()
}
func main() {
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 配置文件不存在，进入激活模式
		fmt.Println("Configuration file not found, entering activation mode...")
		startActivationMode()
	} else {
		// 配置文件存在，进入完整模式
		fmt.Println("Configuration file found, entering full mode...")
		startFullMode()
	}
}

func startFullMode() {
	if version {
		fmt.Printf(
			"WuZhen Gateway %s %s %s with %s %s\n",
			config.Version,
			runtime.GOOS,
			runtime.GOARCH,
			runtime.Version(),
			config.BuildTime,
		)
		return
	}
	now := time.Now()
	cache.Init(time.Hour)
	//加载配置
	config.SetConfigPath(configPath)
	err := config.LoadConfig()
	if err != nil {
		fmt.Printf("Load config error: %s\n", err.Error())
		return
	}
	fmt.Println("start loadConfig", time.Since(now))

	log.Init(config.Config().LogLevel)

	err = db.InitDB()
	if err != nil {
		log.GetLogger().Errorf("Init db error: %s", err.Error())
		return
	}
	defer db.Close()
	fmt.Println("start db", time.Since(now))
	for {
		err = platformRequest.Init()
		if err != nil {
			log.GetLogger().Errorf("Init token fail: %s, retry after 30 seconds", err.Error())
		} else {
			break
		}
		time.Sleep(time.Second * 30)
	}
	fmt.Println("start reg", time.Since(now))
	//状态统计服务
	status.Init()
	fmt.Println("start status", time.Since(now))

	//订阅
	if config.Config().CloudGateway != "" {
		sub.StartSub()
		fmt.Println("start sub", time.Since(now))
	}

	//跳变服务
	if !config.HasFlag(config.FlagJumpChangeDisable) { //启用跳变
		jumpChange.Init()
		fmt.Println("start jump", time.Since(now))
	}

	//加载 clash - 在启动 web 服务前先加载 clash
	for {
		err = clash.Load()
		if err != nil && config.Config().CloudGateway != "" {
			log.GetLogger().Errorf("Load proxy error: %s, retry after 30 seconds", err.Error())
		} else {
			_interface.StatusApi.SetLoad(true)
			break
		}
		time.Sleep(time.Second * 30)
	}
	fmt.Println("start load", time.Since(now))

	//启动代理服务
	err = clash.Start()
	if err != nil {
		log.GetLogger().Errorf("Start proxy error: %s", err.Error())
	}
	fmt.Println("start proxy", time.Since(now))

	//启动 web 服务 - 在 clash 加载完成后再启动 web 服务
	s := web.NewServer()
	go func() {
		err = s.Serve()
		if err != nil {
			log.GetLogger().Fatalln(err)
			return
		}
	}()
	fmt.Println("start web", time.Since(now))
	log.GetLogger().
		Infof("os:%s/%s runtime:%s version:%s flags:%v", runtime.GOOS, runtime.GOARCH, runtime.Version(), config.Version, config.Config().Flags)
	defer executor.Shutdown()
	termSign := make(chan os.Signal, 1)
	hupSign := make(chan os.Signal, 1)
	signal.Notify(termSign, syscall.SIGINT, syscall.SIGTERM)
	signal.Notify(hupSign, syscall.SIGHUP)
	for {
		select {
		case <-termSign:
			return
		case <-hupSign:
			err = config.LoadConfig()
			if err != nil {
				log.GetLogger().Errorf("Load config error: %s", err.Error())
			}
			err = clash.Load()
			if err != nil {
				log.GetLogger().Errorf("Load proxy error: %s", err.Error())
			}
			err = clash.HotUpdate()
			if err != nil {
				log.GetLogger().Errorf("Hot update proxy error: %s", err.Error())
			}
			// TODO：这里可能还要重新加载其他服务
			// 重载日志等级
			log.SetLogLevel(config.Config().LogLevel)
			log.GetLogger().Infof("reload config success")

			s.SetPprof(config.Config().Pprof)
		}
	}
}
