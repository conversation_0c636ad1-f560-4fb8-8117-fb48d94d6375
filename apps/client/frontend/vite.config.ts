import { TanStackRouterVite } from '@tanstack/router-vite-plugin'
import react from '@vitejs/plugin-react-swc'
import { CodeInspectorPlugin } from 'code-inspector-plugin'
import path from 'path'
import { defineConfig, loadEnv } from 'vite'
import svgr from 'vite-plugin-svgr'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const VITE_DEV_BACKEND_URL = `${env.VITE_DEV_BACKEND_URL ?? 'http://localhost:80'}`

  return {
    build: {
      outDir: 'web',
      // sourcemap: true
    },
    plugins: [
      react(),
      TanStackRouterVite(),
      svgr({ include: '**/*.svg?react' }),
      // minipic({
      // 	convert: [
      // 		{ from: "png", to: "webp" },
      // 		{ from: "gif", to: "webp" },
      // 	],
      // }),
      CodeInspectorPlugin({
        bundler: 'vite',
      }),
    ],
    server: {
      host: true,
      strictPort: true,
      open: true,
      port: 3000,
      proxy: {
        '/api': {
          target: VITE_DEV_BACKEND_URL,
          changeOrigin: true,
          // rewrite: (path) => path.replace(/^\/api/, ""),
        },
      },
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        'package.json': path.resolve(__dirname, './package.json'),
      },
    },
  }
})
