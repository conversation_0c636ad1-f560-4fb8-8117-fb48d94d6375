/* prettier-ignore-start */

/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file is auto-generated by TanStack Router

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as VersionUpdateImport } from './routes/versionUpdate'
import { Route as UserManageImport } from './routes/userManage'
import { Route as ScreenImport } from './routes/screen'
import { Route as QuickStrikeImport } from './routes/quickStrike'
import { Route as ProxiesImport } from './routes/proxies'
import { Route as PerformanceImport } from './routes/performance'
import { Route as NetworkErrorImport } from './routes/networkError'
import { Route as NetExaminationImport } from './routes/netExamination'
import { Route as NetClientImport } from './routes/netClient'
import { Route as LinkImport } from './routes/link'
import { Route as EventLogImport } from './routes/eventLog'
import { Route as ErrorImport } from './routes/error'
import { Route as DashboardImport } from './routes/dashboard'
import { Route as NotFoundImport } from './routes/$not-found'
import { Route as IndexImport } from './routes/index'

// Create/Update Routes

const VersionUpdateRoute = VersionUpdateImport.update({
  path: '/versionUpdate',
  getParentRoute: () => rootRoute,
} as any)

const UserManageRoute = UserManageImport.update({
  path: '/userManage',
  getParentRoute: () => rootRoute,
} as any)

const ScreenRoute = ScreenImport.update({
  path: '/screen',
  getParentRoute: () => rootRoute,
} as any)

const QuickStrikeRoute = QuickStrikeImport.update({
  path: '/quickStrike',
  getParentRoute: () => rootRoute,
} as any)

const ProxiesRoute = ProxiesImport.update({
  path: '/proxies',
  getParentRoute: () => rootRoute,
} as any)

const PerformanceRoute = PerformanceImport.update({
  path: '/performance',
  getParentRoute: () => rootRoute,
} as any)

const NetworkErrorRoute = NetworkErrorImport.update({
  path: '/networkError',
  getParentRoute: () => rootRoute,
} as any)

const NetExaminationRoute = NetExaminationImport.update({
  path: '/netExamination',
  getParentRoute: () => rootRoute,
} as any)

const NetClientRoute = NetClientImport.update({
  path: '/netClient',
  getParentRoute: () => rootRoute,
} as any)

const LinkRoute = LinkImport.update({
  path: '/link',
  getParentRoute: () => rootRoute,
} as any)

const EventLogRoute = EventLogImport.update({
  path: '/eventLog',
  getParentRoute: () => rootRoute,
} as any)

const ErrorRoute = ErrorImport.update({
  path: '/error',
  getParentRoute: () => rootRoute,
} as any)

const DashboardRoute = DashboardImport.update({
  path: '/dashboard',
  getParentRoute: () => rootRoute,
} as any)

const NotFoundRoute = NotFoundImport.update({
  path: '/$not-found',
  getParentRoute: () => rootRoute,
} as any)

const IndexRoute = IndexImport.update({
  path: '/',
  getParentRoute: () => rootRoute,
} as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexImport
      parentRoute: typeof rootRoute
    }
    '/$not-found': {
      id: '/$not-found'
      path: '/$not-found'
      fullPath: '/$not-found'
      preLoaderRoute: typeof NotFoundImport
      parentRoute: typeof rootRoute
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardImport
      parentRoute: typeof rootRoute
    }
    '/error': {
      id: '/error'
      path: '/error'
      fullPath: '/error'
      preLoaderRoute: typeof ErrorImport
      parentRoute: typeof rootRoute
    }
    '/eventLog': {
      id: '/eventLog'
      path: '/eventLog'
      fullPath: '/eventLog'
      preLoaderRoute: typeof EventLogImport
      parentRoute: typeof rootRoute
    }
    '/link': {
      id: '/link'
      path: '/link'
      fullPath: '/link'
      preLoaderRoute: typeof LinkImport
      parentRoute: typeof rootRoute
    }
    '/netClient': {
      id: '/netClient'
      path: '/netClient'
      fullPath: '/netClient'
      preLoaderRoute: typeof NetClientImport
      parentRoute: typeof rootRoute
    }
    '/netExamination': {
      id: '/netExamination'
      path: '/netExamination'
      fullPath: '/netExamination'
      preLoaderRoute: typeof NetExaminationImport
      parentRoute: typeof rootRoute
    }
    '/networkError': {
      id: '/networkError'
      path: '/networkError'
      fullPath: '/networkError'
      preLoaderRoute: typeof NetworkErrorImport
      parentRoute: typeof rootRoute
    }
    '/performance': {
      id: '/performance'
      path: '/performance'
      fullPath: '/performance'
      preLoaderRoute: typeof PerformanceImport
      parentRoute: typeof rootRoute
    }
    '/proxies': {
      id: '/proxies'
      path: '/proxies'
      fullPath: '/proxies'
      preLoaderRoute: typeof ProxiesImport
      parentRoute: typeof rootRoute
    }
    '/quickStrike': {
      id: '/quickStrike'
      path: '/quickStrike'
      fullPath: '/quickStrike'
      preLoaderRoute: typeof QuickStrikeImport
      parentRoute: typeof rootRoute
    }
    '/screen': {
      id: '/screen'
      path: '/screen'
      fullPath: '/screen'
      preLoaderRoute: typeof ScreenImport
      parentRoute: typeof rootRoute
    }
    '/userManage': {
      id: '/userManage'
      path: '/userManage'
      fullPath: '/userManage'
      preLoaderRoute: typeof UserManageImport
      parentRoute: typeof rootRoute
    }
    '/versionUpdate': {
      id: '/versionUpdate'
      path: '/versionUpdate'
      fullPath: '/versionUpdate'
      preLoaderRoute: typeof VersionUpdateImport
      parentRoute: typeof rootRoute
    }
  }
}

// Create and export the route tree

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/$not-found': typeof NotFoundRoute
  '/dashboard': typeof DashboardRoute
  '/error': typeof ErrorRoute
  '/eventLog': typeof EventLogRoute
  '/link': typeof LinkRoute
  '/netClient': typeof NetClientRoute
  '/netExamination': typeof NetExaminationRoute
  '/networkError': typeof NetworkErrorRoute
  '/performance': typeof PerformanceRoute
  '/proxies': typeof ProxiesRoute
  '/quickStrike': typeof QuickStrikeRoute
  '/screen': typeof ScreenRoute
  '/userManage': typeof UserManageRoute
  '/versionUpdate': typeof VersionUpdateRoute
}

export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/$not-found': typeof NotFoundRoute
  '/dashboard': typeof DashboardRoute
  '/error': typeof ErrorRoute
  '/eventLog': typeof EventLogRoute
  '/link': typeof LinkRoute
  '/netClient': typeof NetClientRoute
  '/netExamination': typeof NetExaminationRoute
  '/networkError': typeof NetworkErrorRoute
  '/performance': typeof PerformanceRoute
  '/proxies': typeof ProxiesRoute
  '/quickStrike': typeof QuickStrikeRoute
  '/screen': typeof ScreenRoute
  '/userManage': typeof UserManageRoute
  '/versionUpdate': typeof VersionUpdateRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/': typeof IndexRoute
  '/$not-found': typeof NotFoundRoute
  '/dashboard': typeof DashboardRoute
  '/error': typeof ErrorRoute
  '/eventLog': typeof EventLogRoute
  '/link': typeof LinkRoute
  '/netClient': typeof NetClientRoute
  '/netExamination': typeof NetExaminationRoute
  '/networkError': typeof NetworkErrorRoute
  '/performance': typeof PerformanceRoute
  '/proxies': typeof ProxiesRoute
  '/quickStrike': typeof QuickStrikeRoute
  '/screen': typeof ScreenRoute
  '/userManage': typeof UserManageRoute
  '/versionUpdate': typeof VersionUpdateRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/$not-found'
    | '/dashboard'
    | '/error'
    | '/eventLog'
    | '/link'
    | '/netClient'
    | '/netExamination'
    | '/networkError'
    | '/performance'
    | '/proxies'
    | '/quickStrike'
    | '/screen'
    | '/userManage'
    | '/versionUpdate'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/$not-found'
    | '/dashboard'
    | '/error'
    | '/eventLog'
    | '/link'
    | '/netClient'
    | '/netExamination'
    | '/networkError'
    | '/performance'
    | '/proxies'
    | '/quickStrike'
    | '/screen'
    | '/userManage'
    | '/versionUpdate'
  id:
    | '__root__'
    | '/'
    | '/$not-found'
    | '/dashboard'
    | '/error'
    | '/eventLog'
    | '/link'
    | '/netClient'
    | '/netExamination'
    | '/networkError'
    | '/performance'
    | '/proxies'
    | '/quickStrike'
    | '/screen'
    | '/userManage'
    | '/versionUpdate'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  NotFoundRoute: typeof NotFoundRoute
  DashboardRoute: typeof DashboardRoute
  ErrorRoute: typeof ErrorRoute
  EventLogRoute: typeof EventLogRoute
  LinkRoute: typeof LinkRoute
  NetClientRoute: typeof NetClientRoute
  NetExaminationRoute: typeof NetExaminationRoute
  NetworkErrorRoute: typeof NetworkErrorRoute
  PerformanceRoute: typeof PerformanceRoute
  ProxiesRoute: typeof ProxiesRoute
  QuickStrikeRoute: typeof QuickStrikeRoute
  ScreenRoute: typeof ScreenRoute
  UserManageRoute: typeof UserManageRoute
  VersionUpdateRoute: typeof VersionUpdateRoute
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  NotFoundRoute: NotFoundRoute,
  DashboardRoute: DashboardRoute,
  ErrorRoute: ErrorRoute,
  EventLogRoute: EventLogRoute,
  LinkRoute: LinkRoute,
  NetClientRoute: NetClientRoute,
  NetExaminationRoute: NetExaminationRoute,
  NetworkErrorRoute: NetworkErrorRoute,
  PerformanceRoute: PerformanceRoute,
  ProxiesRoute: ProxiesRoute,
  QuickStrikeRoute: QuickStrikeRoute,
  ScreenRoute: ScreenRoute,
  UserManageRoute: UserManageRoute,
  VersionUpdateRoute: VersionUpdateRoute,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* prettier-ignore-end */

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/",
        "/$not-found",
        "/dashboard",
        "/error",
        "/eventLog",
        "/link",
        "/netClient",
        "/netExamination",
        "/networkError",
        "/performance",
        "/proxies",
        "/quickStrike",
        "/screen",
        "/userManage",
        "/versionUpdate"
      ]
    },
    "/": {
      "filePath": "index.tsx"
    },
    "/$not-found": {
      "filePath": "$not-found.tsx"
    },
    "/dashboard": {
      "filePath": "dashboard.tsx"
    },
    "/error": {
      "filePath": "error.tsx"
    },
    "/eventLog": {
      "filePath": "eventLog.tsx"
    },
    "/link": {
      "filePath": "link.tsx"
    },
    "/netClient": {
      "filePath": "netClient.tsx"
    },
    "/netExamination": {
      "filePath": "netExamination.tsx"
    },
    "/networkError": {
      "filePath": "networkError.tsx"
    },
    "/performance": {
      "filePath": "performance.tsx"
    },
    "/proxies": {
      "filePath": "proxies.tsx"
    },
    "/quickStrike": {
      "filePath": "quickStrike.tsx"
    },
    "/screen": {
      "filePath": "screen.tsx"
    },
    "/userManage": {
      "filePath": "userManage.tsx"
    },
    "/versionUpdate": {
      "filePath": "versionUpdate.tsx"
    }
  }
}
ROUTE_MANIFEST_END */
