import { isEnv } from '@/lib/utils'

const proto = window.location.protocol === 'http:' ? 'ws:' : 'wss:'
const socketUrl = isEnv()
  ? `${proto}//${import.meta.env.VITE_WS_URL}/api`
  : `${proto}//${window.location.host}/api`

export class WebSocketService {
  private url: string
  private socket: WebSocket | null = null
  private isConnected: boolean = false
  private messageQueue: string[] = []
  private reConnectTimer: NodeJS.Timeout | null = null
  private onMessageCallback: ((message: string) => void) | null = null

  public constructor(url: string) {
    this.url = `${socketUrl}${url}`
  }

  public connect(): void {
    this.socket = new WebSocket(this.url)

    this.socket.onopen = () => {
      this.isConnected = true
      this.flushMessageQueue()
    }

    this.socket.onmessage = (event: MessageEvent) => {
      if (this.onMessageCallback) {
        this.onMessageCallback(event.data)
      }
    }

    this.socket.onclose = () => {
      console.log('WebSocket连接已关闭')
      this.isConnected = false
      // 可在此处实现重连机制
      this.reConnectTimer && clearTimeout(this.reConnectTimer)
      this.reConnectTimer = setTimeout(() => {
        this.connect()
      }, 5000)
    }

    this.socket.onerror = (error: Event) => {
      console.error('WebSocket错误:', error)
    }
  }

  public send(message: string): void {
    if (this.isConnected) {
      this.socket?.send(message)
    } else {
      console.warn('WebSocket未连接，消息已加入队列')
      this.messageQueue.push(message)
    }
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message) {
        this.send(message)
      }
    }
  }

  public setOnMessageCallback(callback: (message: string) => void): void {
    this.onMessageCallback = callback
  }

  public close(): void {
    this.socket?.close()
  }
}
