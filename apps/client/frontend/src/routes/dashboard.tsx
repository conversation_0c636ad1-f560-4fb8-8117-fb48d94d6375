import { isAdmin } from "@/auth.tsx";
import { DashboardLoader, Dashboard as DashboardPage } from "@/pages/dashboard";
import "@/pages/dashboard/index.scss";
import { createFileRoute, notFound } from "@tanstack/react-router";
import { ErrorBoundary, ErrorFallback } from "@/components/ErrorBoundary";

export const routeConfig = (
	component: () => React.ReactElement,
	role?: string
) => {
	return {
		component: () => (
			<ErrorBoundary fallback={ErrorFallback}>{component()}</ErrorBoundary>
		),
		beforeLoad: async () => {
			// if (!isAuthenticated() || !getUserActive()) {
			//   throw redirect({
			//     to: '/login',
			//   })
			// }
		},
		notFoundMode: "root",
		loader: () => {
			if (!isAdmin() && role) {
				throw notFound();
			}
		},
		pendingComponent: () => <DashboardLoader />,
	};
};

export const Route = createFileRoute("/dashboard")(routeConfig(Dashboard));

function Dashboard() {
	return <DashboardPage />;
}
