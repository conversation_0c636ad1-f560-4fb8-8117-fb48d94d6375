// import { isAuthenticated, getUserActive } from '@/auth'
import { LayoutPage } from '@/components/LayoutPage'
import { Dialog, type DialogExport } from '@/components/Dialog'
import { ReactQueryDevelopmentTools } from '@/components/utils/development-tools/ReactQueryDevelopmentTools.tsx'
import type { QueryClient } from '@tanstack/react-query'
import {
  createRootRouteWithContext,
  Outlet,
  useLocation,
  // useNavigate,
} from '@tanstack/react-router'
import {
  createContext,
  Suspense,
  useContext,
  useMemo,
  useRef,
} from 'react'
import { Toaster } from '@/components/ui/toaster'
import { ErrorBoundary, ErrorFallback } from '@/components/ErrorBoundary'
// import { useGlobalInfoContext } from '@/provider/GlobalInfoContextProvider'
// import { layoutData } from '@/components/LayoutPage/layoutData'
// import { isAdmin } from '@/auth'

import packageJson from 'package.json'

export interface MyRouterContext {
  queryClient: QueryClient
}

const RootContext = createContext<{
  dialog: DialogExport['open']
}>({
  dialog() {},
})

export const useRootContext = () => {
  return useContext(RootContext)
}

export const Route = createRootRouteWithContext<MyRouterContext>()({
  component: RootComponent,
})

function RootComponent() {
  const dialogRef = useRef<DialogExport>(null)
  // const navigate = useNavigate()
  const { pathname } = useLocation()
  // const context = useRouteContext({ from: '/screen' })
  // const { globalInfo,refetchGlobalInfo } = useGlobalInfoContext()
  const noAside = useMemo(() => {
    const noAsideRoutes = ['/screen', '/networkError', '/error']
    return noAsideRoutes.includes(pathname)
  }, [pathname])
  // const flags = useMemo(() => {
  //   return globalInfo?.data.flags || []
  // }, [globalInfo])

  // useEffect(() => {
  //   // const curLayoutData = layoutData.find((item) => pathname.includes(item.id))
  //   // if (curLayoutData?.role === 'admin' && !isAdmin()) {
  //   //   navigate({ to: '/dashboard' })
  //   // }
  //   // if (curLayoutData?.flag) {
  //   //   if (!flags.includes(curLayoutData.flag)) {
  //   //     navigate({ to: '/dashboard' })
  //   //   }
  //   // }
  //   refetchGlobalInfo()
  // }, [flags, pathname])

  return (
    <>
      <ErrorBoundary fallback={ErrorFallback}>
        <Dialog ref={dialogRef} hasCancelButton={false} />
        <Toaster />
        <RootContext.Provider
          value={{
            dialog(...args) {
              dialogRef.current!.open(...args)
            },
          }}
        >
          <div
            className="layout-content flex-1 flex w-full h-screen min-w-[1440px]"
            data-version={packageJson.version}
          >
            {!noAside && <LayoutPage />}
            <div className="flex-1">
              <Outlet />
            </div>
          </div>
          <Suspense>
            <ReactQueryDevelopmentTools initialIsOpen={false} />
          </Suspense>
        </RootContext.Provider>
      </ErrorBoundary>
    </>
  )
}
