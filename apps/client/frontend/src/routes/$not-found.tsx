import { createFileRoute } from "@tanstack/react-router";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "@tanstack/react-router";

function NotFindErrorBoundary() {
	const navigator = useNavigate();

	const goBack = async () => {
		navigator({ to: `/dashboard` });
	};
	return (
		<div className="flex flex-col items-center justify-center h-screen">
			<h1>暂无该页面</h1>
			<Button
				className="rounded-md bg-[#1E3A8A] text-[#fff] hover:bg-[#1E3A8A] flex font-medium"
				onClick={() => goBack()}
			>
				返回系统
			</Button>
		</div>
	);
}

export const Route = createFileRoute("/$not-found")({
	component: () => <NotFindErrorBoundary />,
});
