import * as echarts from 'echarts'
import { useEffect } from 'react'
import { EChartsType } from 'echarts'

export const usePreformChart = (
  elementId: string,
  options = {},
  ref: React.MutableRefObject<EChartsType | null>,
  domRef: React.MutableRefObject<HTMLDivElement | null>,
) => {
  useEffect(() => {
    const chartDom = document.getElementById(elementId)
    if (!chartDom) return
    ref.current = echarts.init(chartDom)

    ref.current.setOption(options)

    const handleResize = () => {
      ref.current?.resize()
    }

    const resizeObserver = new ResizeObserver(handleResize)
    domRef.current && resizeObserver.observe(domRef.current)

    return () => {
      resizeObserver?.disconnect()
      ref.current?.dispose()
    }
  }, [])
}
