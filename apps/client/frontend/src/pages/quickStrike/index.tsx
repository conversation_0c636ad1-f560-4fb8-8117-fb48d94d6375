import { useState, useMemo, useEffect } from 'react'

import { ToastAction } from '@/components/ui/toast'
import { toast, useToast } from '@/components/ui/use-toast'
import { BaseContent } from '@/components/BaseContent'
import { errorToast } from '@/components/GlobalToast'
import { QuickStrikeList } from '@/pages/quickStrike/components/quickStrikeList'
import { X } from 'lucide-react'

import ProxiesRemind from '@/assets/svg/proxies/remind.svg'
import ProxiesEmpty from '@/assets/svg/proxies/ProxiesEmpty.svg?react'

import { Route } from '@/routes'

import { useHitDeployCountry, useQuickStrikeMutation } from '@/api/quickStrike'

import type { CountryItem } from '@/pages/quickStrike/types/quick-strike-type'
import type { CountryCodeType } from '../dashboard/data'

export const QuickStrike = () => {
  const navigate = Route.useNavigate()

  const { clear: clearToast } = useToast()

  const [showTip, setShowTip] = useState(true)
  const [currentCountryCode, setCurrentCountryCode] =
    useState<CountryCodeType>('')
  const [buttonLoading, setButtonLoading] = useState(false)

  const { data: allHitDeployCountry } = useHitDeployCountry()

  const quickStrikeMutation = useQuickStrikeMutation()

  const hitDeployCountryList = useMemo<CountryItem[]>(() => {
    if (allHitDeployCountry && allHitDeployCountry.data) {
      return allHitDeployCountry.data.country.sort((a, b) =>
        b.code.localeCompare(a.code),
      ) as CountryItem[]
    }
    return [] as CountryItem[]
  }, [allHitDeployCountry])

  const onSuccess = (countryCode: CountryCodeType) => {
    toast({
      variant: 'succsess',
      title: '目标打击成功！',
      duration: 5000,
      titleClassName:
        "text-white text-lg font-medium font-['PingFang SC'] leading-relaxed",
      descClassName:
        'w-[238px] text-justify text-neutral-50 text-sm font-normal leading-tight',
      description: '已自动部署节点，在节点池查看节点！',
      action: (
        <ToastAction
          className="h-10 px-4 py-2 rounded-md border border-zinc-200 justify-center items-center gap-2.5 inline-flex hover:bg-transparent mt-1 !ml-[0px] !mr-1"
          altText="查看节点"
          onClick={() => onViewNodeHandle(countryCode)}
        >
          查看节点
        </ToastAction>
      ),
    })
  }
  const onError = () => {
    errorToast({ title: '打击失败，请切换目标或重试!', duration: 2000 }, toast)
  }
  const quickStrikeHanle = async (countryCode: CountryCodeType) => {
    setButtonLoading(true)
    setCurrentCountryCode(countryCode)
    await quickStrikeMutation.mutate(countryCode, {
      onSuccess: () => {
        const randomSeconds = Math.floor(Math.random() * 7) + 4
        // 因为打击成功后有5s-10s的延迟，所以前端这里需要手动延迟5s
        setTimeout(() => {
          onSuccess(countryCode)
          setButtonLoading(false)
          setCurrentCountryCode('')
        }, randomSeconds * 1000)
      },
      onError: () => {
        setButtonLoading(false)
        setCurrentCountryCode('')
        onError()
      },
    })
  }

  const onViewNodeHandle = async (countryCode: string) => {
    await navigate({ to: '/proxies', search: { country_code: countryCode } })
  }

  useEffect(() => {
    return () => {
      clearToast()
    }
  }, [])

  return (
    <BaseContent title="快速打击">
      {hitDeployCountryList.length ? (
        <div className="flex-1 flex flex-col space-y-5">
          {showTip && (
            <div className="relative p-5 bg-blue-50 rounded-md shadow-xs border border-blue-200 justify-start items-start gap-4 inline-flex">
              <img src={ProxiesRemind} alt="" className="w-8 h-8" />
              <div className="text-zinc-700 text-sm font-normal leading-snug">
                <p>1. 执行打击后会有5-10s的时间，才能获取打击结果。</p>
                <p>
                  2.
                  以下为测绘获取的可打击国家列表，目标包含NAS、IOT设备等。由于测绘的时效问题，若漏洞已修复，则存在一定打击失败的概率。
                </p>
              </div>
              <X
                className=" absolute right-3 top-3 w-4 h-4 text-[#18181B] cursor-pointer"
                onClick={() => setShowTip(false)}
              />
            </div>
          )}
          <hgroup>
            <div className="text-gray-900 text-lg font-semibold leading-relaxed">
              国家
            </div>
          </hgroup>
          <QuickStrikeList
            data={hitDeployCountryList}
            btnLoading={buttonLoading}
            currentCountryCode={currentCountryCode}
            quickStrikeHanle={quickStrikeHanle}
          />
        </div>
      ) : (
        <div className="flex-1 flex flex-col justify-center items-center">
          <ProxiesEmpty />
          <div className="text-zinc-950 text-lg font-medium leading-relaxed">
            暂无可打击节点
          </div>
        </div>
      )}
    </BaseContent>
  )
}
