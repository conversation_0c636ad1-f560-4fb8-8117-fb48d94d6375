import { useMemo } from 'react'

import { Loader } from 'lucide-react'
import { EllipsisTooltip } from '@/components/encapsulation'
import { Button } from '@/components/ui/button'

import { cn, getUrl } from '@/lib/utils'

import { CountryCodeType } from '@/pages/dashboard/data'
import type { CountryItem } from '@/pages/quickStrike/types/quick-strike-type'

import './index.scss'

interface IQuickStrikeItemProps {
  btnLoading: boolean
  countryName: string
  countryCode: CountryCodeType
  currentCountryCode: CountryCodeType
  quickStrikeHanle: (countryCode: CountryCodeType) => void
}

const QuickStrikeItem: React.FC<IQuickStrikeItemProps> = (props) => {
  const {
    btnLoading,
    countryName,
    countryCode,
    currentCountryCode,
    quickStrikeHanle,
  } = props
  const url = getUrl()
  const isCurrentItem = useMemo(() => {
    return countryCode === currentCountryCode
  }, [currentCountryCode, countryCode])

  return (
    <div
      className={cn(
        'w-[minmax(160px,464px)] h-[60px] flex items-center p-3 hover:bg-blue-50 rounded-lg group text-[#111322] ',
        btnLoading && isCurrentItem ? 'bg-blue-50' : '',
      )}
    >
      <div className="flex justify-end items-center w-full h-7 gap-3">
        <div className="w-[27px] h-[20px] quick-strike-item-flag  rounded-sm overflow-hidden">
          <img
            className={cn('w-full h-full object-cover rounded-sm')}
            src={`${url}/res/flag3/${countryCode.toLowerCase()}.svg`}
          />
        </div>

        <EllipsisTooltip
          className="text-lg flex-1 font-semibold"
          text={countryName}
        />

        {!(btnLoading && !isCurrentItem) && (
          <Button
            className={cn(
              'hidden group-hover:flex h-9 px-4 py-2 bg-red-600 rounded-md justify-end items-center gap-2 !opacity-100',
              btnLoading && isCurrentItem ? 'flex' : 'hidden',
            )}
            loading={btnLoading}
            loaderIcon={
              <Loader className="h-4 w-4 animate-spin" color="#ffffff" />
            }
            onClick={() => quickStrikeHanle(countryCode)}
          >
            快速打击
          </Button>
        )}
      </div>
    </div>
  )
}

type IProps = {
  data: CountryItem[]
  currentCountryCode: CountryCodeType // 正在快速打击的code
  btnLoading: boolean
  quickStrikeHanle: (countryCode: CountryCodeType) => void
}

export const QuickStrikeList = ({
  data,
  currentCountryCode,
  btnLoading,
  quickStrikeHanle,
}: IProps) => {
  return (
    <main className="p-4 bg-white rounded-lg border border-[#dcdfea] grid grid-cols-[repeat(6,_minmax(160px,_464px))] gap-4 justify-between">
      {data.map((country: CountryItem) => {
        return (
          <QuickStrikeItem
            key={country.code}
            btnLoading={btnLoading}
            countryName={country.name}
            countryCode={country.code as CountryCodeType}
            currentCountryCode={currentCountryCode}
            quickStrikeHanle={quickStrikeHanle}
          />
        )
      })}
    </main>
  )
}
