import { ColumnDef } from '@tanstack/react-table'

import { EllipsisTooltip } from '@/components/encapsulation'
import { DataTableColumnHeader } from '@/components/LqTable/components/data-table-column-header'
import { StateTag } from '@/components/StateTag'
import WhiteDot from '@/assets/svg/WhiteDot.svg?react'
import { ClientList } from '../types/proxy-bridge-type'
export function columns(): ColumnDef<ClientList>[] {
  return [
    {
      accessorKey: 'Hostname',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="主机名" />
      ),
      cell: ({ row }) => {
        return (
          <div className="min-w-[120px] flex space-x-2">
            <EllipsisTooltip text={row.getValue('Hostname')} />
          </div>
        )
      },
      enableSorting: false,
    },
    {
      accessorKey: 'IP',
      header: 'ip地址',
      cell: ({ row }) => {
        return (
          <div className="min-w-[80px] flex space-x-2">
            {row.getValue('IP')}
          </div>
        )
      },
      enableSorting: false,
    },
    {
      accessorKey: 'Mac',
      header: 'MAC地址',
      cell: ({ row }) => {
        return (
          <div className="min-w-[60px] flex space-x-2 ">
            {row.getValue('Mac')}
          </div>
        )
      },
      enableSorting: false,
    },
    {
      accessorKey: 'proxy',
      header: '链路名',
      cell: ({ row }) => {
        return (
          <div className="min-w-[60px] flex space-x-2 ">
            {row.getValue('proxy')}
          </div>
        )
      },
      enableSorting: false,
    },
    {
      accessorKey: 'active',
      header: '状态',
      cell: ({ row }) => {
        return (
          <div className="min-w-[80px]">
            {row.getValue('active') ? (
              <StateTag
                active={{
                  className: ' text-[#fff]  bg-[#1E3A8A]',
                  desc: '活跃',
                  icon: <WhiteDot className="mr-1" />,
                }}
              />
            ) : (
              <StateTag
                active={{
                  className: ' text-[#fff]  bg-[#A1A1AA]',
                  desc: '闲置',
                  icon: <WhiteDot className="mr-1" />,
                }}
              />
            )}
          </div>
        )
      },
      enableSorting: false,
    },
  ]
}
