import { z } from 'zod'

import {
  Combobox,
  type optionsType,
  type ComboboxProps,
} from '@/components/Combobox'
import { IFormSchemaItem } from '@/components/LqTable/components/SearchForm'
import { Input, InputProps } from '@/components/ui/input.tsx'
import { SearchOptionText } from '@/pages/link'

export const linkSchema = z.object({
  status: z.string(),
  username: z.string(),
  password: z.string(),
  role: z.string(),
})

export type LinkForm = z.infer<typeof linkSchema>

interface IOptions {
  projectOptions: optionsType[]
}

export const getSearchFormSchema = (options: IOptions): IFormSchemaItem[] => {
  const { projectOptions } = options

  return [
    {
      key: 'username',
      defaultValue: '',
      formItemRender: Input,
      className: 'w-[321px]',
      formItemProps: {
        placeholder: '输入关键词,设备名、链路名、节点名..',
      } as InputProps,
    },
    {
      key: 'projectIds',
      defaultValue: [],
      formItemRender: Combobox,
      className: 'w-[260px] h-[40px]',
      formItemProps: {
        placeholder: '用户',
        title: '项目名称',
        className: 'border-black text-[#18181B] gap-0 px-3 py-2 ',
        contentClass: ' w-[260px]',
        options: projectOptions,
        showSearch: false,
        radioText: SearchOptionText,
        showRadio: false,
      } as ComboboxProps,
    },
  ]
}
