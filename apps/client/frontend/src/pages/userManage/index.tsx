import { useCallback, useMemo, useRef, useState } from 'react'
import {
  DataTableMemo,
  DataTableRef,
} from '@/components/LqTable/data-table-scroll'
import { columns } from './components/columns'
import { DataTableViewOptions } from '@/components/LqTable/components/data-table-view-options'
import { UserManageEnum } from './enums'
import { BaseContent } from '@/components/BaseContent'
import { UserList } from './types/proxy-bridge-type'
import { SearchLocal } from './components/search-local'
import { useUserDeleteMutation, useUserList } from '@/api/user'
import { successToast } from '@/components/GlobalToast'
import { toast } from '@/components/ui/use-toast'
import DeleteDialog from '@/components/DeleteDialog'
export const UserManage = () => {
  const [pagination] = useState({
    currentPage: 1,
    pageSize: 999,
    totalCount: 0,
  })
  const [delVisible, setDelVisible] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState(false)
  const [deleteData, setDeleteData] = useState<UserList | null>()
  const { data: userList } = useUserList()
  const usersDeleteMutation = useUserDeleteMutation()

  const tableRef = useRef<DataTableRef<UserList>>(null)
  const selectRow =
    tableRef.current?.getTable().getFilteredSelectedRowModel().rows ?? []
  const userListLength = userList?.data.length ? userList?.data.length - 1 : 0

  const deleteHandle = useCallback(async () => {
    setDeleteLoading(true)

    const currentUser = deleteData
      ? [deleteData.id]
      : (selectRow
          ?.map((item: { original: UserList }) => {
            return !item.original.is_admin ? item.original.id : ''
          })
          ?.filter((item) => item) ?? [])

    usersDeleteMutation.mutate(currentUser)

    successToast('删除成功', toast)
    tableRef.current?.getTable().resetRowSelection()
    setDeleteData(null)
    setDeleteLoading(false)
    setDelVisible(false)
  }, [deleteData, selectRow])

  const columnsData = useMemo(() => {
    return columns({ setDeleteData, setDelVisible })
  }, [])

  return (
    <BaseContent title="用户管理">
      <div className="flex rounded-md flex-1">
        <DataTableMemo
          ref={tableRef}
          className=" px-4 p-1 pb-0"
          pagination={pagination}
          data={userList?.data ?? []}
          columns={columnsData}
          // loadMore={loadMore}
        >
          {{
            toolbar: (table) => (
              <SearchLocal
                table={table}
                toolbar={
                  <DataTableViewOptions
                    withSelectedRowModel
                    table={table}
                    columnName={UserManageEnum}
                    setOpen={setDelVisible}
                  />
                }
              />
            ),
          }}
        </DataTableMemo>
      </div>
      <DeleteDialog
        open={delVisible}
        title={`确定删除 ${deleteData ? '1' : selectRow?.length > userListLength ? userListLength : selectRow?.length} 个用户?`}
        desc="用户删除后将无法登录系统，且不能使用相关节点和链路"
        loading={deleteLoading}
        openChange={setDelVisible}
        deleteHandle={deleteHandle}
      />
    </BaseContent>
  )
}
