import type { UserList } from '@/pages/link/types/proxy-bridge-type'
import { Button } from '@/components/ui/button'
import { PasswordInput } from '@/components/PasswordInput'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Form } from 'antd'
import { useEffect, useState } from 'react'
import {
  PasVerification,
  validatePassword,
} from '@/pages/login/components/passVerification'
import { useRePassMutation } from '@/api/user'
import { successToast } from '@/components/GlobalToast'
import { toast } from '@/components/ui/use-toast'
import { passCrypto } from '@/lib/utils'

export const UserPass = ({
  rowData,
}: {
  rowData: UserList
}) => {
  const [open, setOpen] = useState(false)
  const [pass, setPass] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(true)
  const [form] = Form.useForm()

  const rePassValue = Form.useWatch('rePassword', form)
  const passwordValue = Form.useWatch('password', form)

  const editPassMutation = useRePassMutation()

  const handleSubmit = () => {
    // setIsSubmitting(true);
    form.validateFields()
    if (isSubmitting) return
    try {
      const addvalue = form.getFieldsValue()
      editPassMutation.mutate({
        uuid: rowData.id,
        password: passCrypto(addvalue.password),
      })
      successToast('密码修改成功', toast)
      form.resetFields()
      setOpen(false)
    } finally {
      // setIsSubmitting(false);
    }
  }
  useEffect(() => {
    setIsSubmitting(
      !validatePassword(passwordValue as string, rePassValue as string, pass),
    )
  }, [rePassValue, passwordValue])

  return (
    <div className="min-w-[120px] flex space-x-2">
      <Popover
        open={open}
        onOpenChange={(type) => {
          setOpen(type)
          if (open) form.resetFields()
        }}
      >
        <PopoverTrigger asChild>
          <Button variant="ghost" className="hover:bg-transparent">
            <span className="underline">****</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80">
          <Form
            className="projectform"
            form={form}
            name={'form_' + rowData.account}
            autoComplete="off"
            layout="vertical"
          >
            <PasVerification
              className="change_password_input"
              setSatisfiedConditions={setPass}
              des="新密码"
            />
            <Form.Item
              name="rePassword"
              label="确认密码"
              rules={[
                { required: true, message: '请输入确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'))
                  },
                }),
              ]}
              className="confirm_password_input"
            >
              <PasswordInput
                className="focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 focus-within:border-[#D1D5DB] confirm_password_input"
                placeholder="请输入密码"
              />
            </Form.Item>
          </Form>
          <Button
            className="mt-5 w-full h-10 bg-[#1E3A8A]"
            disabled={isSubmitting}
            onClick={handleSubmit}
          >
            确认
          </Button>
        </PopoverContent>
      </Popover>
    </div>
  )
}
