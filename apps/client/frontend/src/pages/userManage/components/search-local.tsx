import { Input } from '@/components/ui/input'
import { Table } from '@tanstack/react-table'
interface IProps<TData> {
  table: Table<TData>
  toolbar?: React.ReactNode
}

export function SearchLocal<TData>({ table, toolbar }: IProps<TData>) {
  return (
    <>
      <div className="flex items-center justify-between space-x-3">
        <div className="flex flex-1 items-center space-x-2">
          <Input
            placeholder="输入用户名..."
            value={
              (table.getColumn('account')?.getFilterValue() as string) ?? ''
            }
            onChange={(event) =>
              table.getColumn('account')?.setFilterValue(event.target.value)
            }
            className="w-[321px]"
          />
        </div>
        <div className="min-w-[80px] h-10 flex justify-end">{toolbar}</div>
      </div>
    </>
  )
}
