import { ColumnDef } from '@tanstack/react-table'
import { DataTableColumnHeader } from '@/components/LqTable/components/data-table-column-header'
import { StateTag } from '@/components/StateTag'
import DelWhite from '@/assets/svg/delWhite.svg?react'
import { Tooltip } from 'antd'
import Delete from '@/assets/svg/Delete.svg?react'
import { UserList } from '../types/proxy-bridge-type'
import { Checkbox } from '@/components/ui/checkbox'
import { timeContrast } from '@/lib/utils'
import { UserPass } from './userPass'

export function columns({
  setDeleteData,
  setDelVisible,
}: {
  setDeleteData: (data: UserList) => void
  setDelVisible: (data: boolean) => void
}): ColumnDef<UserList>[] {
  return [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table
              .getFilteredRowModel()
              .rows.filter((row) => !row.original.is_admin)
              .every((row) => row.getIsSelected()) ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={() => {
            const rows = table
              .getRowModel()
              .rows.filter((row) => !row.original.is_admin)
            const notAllSelected = rows.some((row) => !row.getIsSelected())
            rows.forEach((row) => {
              row.toggleSelected(notAllSelected)
            })
          }}
          aria-label="Select all"
          className="translate-y-[2px]"
        />
      ),
      cell: ({ row }) => {
        return (
          <Checkbox
            disabled={row.original.is_admin}
            checked={!row.original.is_admin && row.getIsSelected()}
            onCheckedChange={(value: boolean) => row.toggleSelected(!!value)}
            aria-label="Select row"
            className="translate-y-[2px]"
          />
        )
      },
      meta: {
        className: 'w-1 max-w-8',
      },
    },
    {
      accessorKey: 'account',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="用户名" />
      ),
      cell: ({ row }) => {
        const account = row.getValue('account') as string
        return (
          <div className="min-w-[120px] flex items-center space-x-2">
            {account}
            {row.original.is_admin && (
              <StateTag
                active={{
                  className:
                    ' text-[#fff] rounded bg-[#1E3A8A] text-[10px] mx-1 p-1 w-[18px] h-[18px] box-border',
                  desc: '系',
                }}
              />
            )}
          </div>
        )
      },
      enableSorting: false,
      filterFn: (row, id, value) => {
        const searchValue = row.getValue(id) as string
        return searchValue.includes(value)
      },
    },
    {
      accessorKey: 'pass',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="用户密码" />
      ),
      cell: ({ row }) => {
        return <UserPass rowData={row.original} />
      },
      enableSorting: false,
    },
    {
      accessorKey: 'last_login_at',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="最近登录时间" autoOrder />
      ),
      cell: ({ row }) => {
        return (
          <div className="min-w-[80px]">
            {timeContrast(row.getValue('last_login_at'))}
          </div>
        )
      },
    },
    {
      accessorKey: 'created_at',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="用户创建时间" autoOrder />
      ),
      cell: ({ row }) => {
        return (
          <div className="min-w-[80px]">
            {timeContrast(row.getValue('created_at'))}
          </div>
        )
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        return (
          <div className="flex items-center">
            {row.original.is_admin ? (
              <div className="p-2.5 rounded-md  ">
                <DelWhite id="user_delete_disabled_icon" className="w-5" />
              </div>
            ) : (
              <Tooltip title="删除">
                <div
                  className="p-2.5 rounded-md hover:bg-[#E5E7EB] cursor-pointer"
                  id={`user-action-delete-account-${row.id}`}
                  onClick={() => {
                    setDeleteData(row.original)
                    setDelVisible(true)
                  }}
                >
                  <Delete id="user_delete_icon" className="w-5" />
                </div>
              </Tooltip>
            )}
          </div>
        )
      },
      meta: {
        className: 'w-[60px]',
        isFixed: true,
      },
    },
  ]
}
