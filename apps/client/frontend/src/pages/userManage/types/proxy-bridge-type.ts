import { ApiResponseSchema } from '@/api/commonProxy'
import { z } from 'zod'

export const PathSchema = z.object({
  name: z.string(),
  account_is_admin: z.boolean(),
  account: z.string(),
  exclusive: z.string(),
  use: z.boolean(),
  proxies: z.array(z.string()),
  proxies_code: z.array(z.string()),
})

export const PathListSchema = ApiResponseSchema(z.array(PathSchema))

export type PathList = z.infer<typeof PathSchema>

export const UserSchema = z.object({
  account: z.string(),
  created_at: z.string(),
  last_login_at: z.string(),
  id: z.string(),
  is_active: z.boolean(),
  is_admin: z.boolean(),
})

export const UserListSchema = ApiResponseSchema(z.array(UserSchema))

export type UserList = z.infer<typeof UserSchema>

export const ProxiesSchema = z.object({
  city_name: z.string(),
  country_code: z.string(),
  country_name: z.string(),
  delay: z.number(),
  name: z.string(),
  port: z.number(),
  protocol: z.string(),
  server: z.string(),
  type: z.string(),
  use_status: z.string(),
})

export const ProxiesListSchema = ApiResponseSchema(z.array(ProxiesSchema))

export type ProxiesList = z.infer<typeof ProxiesSchema>
