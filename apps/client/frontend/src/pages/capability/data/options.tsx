import { hexToRgba } from '@/lib/utils'

export const PerformanceTabsMap = {
  OVERVIEW: 'overview',
  CPU: 'cpu',
  MEMORY: 'memory',
}

export const usageOption = (title: string, themeColor: string) => {
  return {
    title: {
      text: title + '（%）',
      textStyle: {
        fontWeight: 'normal',
        fontSize: 12,
        color: '#374151',
      },
      left: 35,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        lineStyle: {
          color: '#57617B',
        },
      },
      formatter: function (parameters: Record<string, any>) {
        if (!parameters[0].value) return ''
        return ` <div style="padding:2px;width:236px">
                            <div style="padding-bottom:6px;border-bottom:1px solid #E5E7EB;margin-bottom:10px;color:#000;font-weight:600">2024-08-06 ${parameters[0].name}</div>
                            <div style="display:flex;align-items:center;justify-content:space-between;">
                                <div style="display:flex;align-items:center;">
                                    <div style="width:8px;height:8px;border-radius:50%;background-color:${themeColor};margin-right:8px"></div>
                                    <span style="font-weight:400;color:#374151">${title}</span>
                                </div>
                                <span style="color:${themeColor}">${parameters[0].value}%</span>
                            </div>
                    </div>
                `
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: '40',
      right: '0',
      top: '40',
      bottom: '20',
      containLabel: false,
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#F3F4F6',
          },
        },
        axisLabel: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        splitArea: {
          show: true,
          areaStyle: {
            color: ['#FAFAFA', '#fff'],
            opacity: 0.5,
          },
        },
        data: new Array(20).fill(null),
      },
    ],
    yAxis: [
      {
        type: 'value',
        min: 0,
        max: 100,
        interval: 20,
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          margin: 10,
          textStyle: {
            fontSize: 14,
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#F3F4F6',
          },
        },
        nameTextStyle: {
          fontSize: 12,
        },
      },
    ],
    series: [
      {
        name: '移动',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        lineStyle: {
          width: 3,
        },
        areaStyle: {
          color: hexToRgba(themeColor, 0.1),
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 10,
        },
        itemStyle: {
          width: 4,
          color: themeColor,
          borderColor: '#fff',
          borderWidth: 2,

          // emphasis: {
          //     symbolSize: 10,
          //     color: themeColor,
          //     borderColor: '#fff',
          //     borderWidth: 2
          // }
        },

        data: new Array(20).fill(null),
      },
    ],
  }
}
