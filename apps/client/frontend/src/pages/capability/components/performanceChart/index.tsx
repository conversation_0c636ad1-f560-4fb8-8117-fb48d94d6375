import React, { useEffect } from 'react'
import { EChartsType } from 'echarts'
import { usageOption, PerformanceTabsMap } from '../../data/options'
import { usePreformChart } from '@/hooks/useCharts'
import { cn } from '@/lib/utils'

import { useWsTrafficContext } from '@/provider/WsTrafficContextProvider'
export const PerformanceChart = ({
  activeType,
}: {
  activeType: string
}) => {
  const { wsTrafficData } = useWsTrafficContext()

  const cpuDomRef = React.useRef<HTMLDivElement | null>(null)
  const memoryDomRef = React.useRef<HTMLDivElement | null>(null)
  const lineChart = React.useRef<EChartsType | null>(null)
  const memoryChart = React.useRef<EChartsType | null>(null)

  const lineType = activeType !== PerformanceTabsMap.MEMORY
  const memoryType = activeType !== PerformanceTabsMap.CPU

  const lineActiveOption =
    (lineType && usageOption('CPU使用率', '#2563EB')) || undefined
  const memoryActiveOption =
    (memoryType && usageOption('内存利用率', '#00F700')) || undefined

  const updatelineData = (
    chartRef: React.MutableRefObject<EChartsType | null>,
    message: number,
  ) => {
    if (!chartRef.current) return

    const axisData = new Date().toLocaleTimeString().replace(/^\D*/, '')
    const outData = message.toFixed(2)

    const option = chartRef.current.getOption() as typeof lineActiveOption
    if (!option) return

    const data = option.series[0]?.data ?? []
    const categories = option.xAxis[0]?.data ?? []
    if (data.length <= 20) {
      data.push(outData)
      categories.push(axisData)
    } else {
      data.shift()
      data.push(outData)
      option.xAxis[0] && option.xAxis[0].data.shift()
      categories.push(axisData)
    }
    if (chartRef.current) {
      chartRef.current.setOption({
        series: [{ data }],
        xAxis: { data: categories },
      })
    }
  }

  useEffect(() => {
    if (!wsTrafficData.length) return
    const newData = wsTrafficData[wsTrafficData.length - 1]
    updatelineData(lineChart, newData?.cpu ?? 0)
    updatelineData(memoryChart, newData?.memory ?? 0)
  }, [wsTrafficData])

  useEffect(() => {
    lineChart.current?.resize()
    memoryChart.current?.resize()
  }, [activeType])

  usePreformChart('usage', lineActiveOption, lineChart, cpuDomRef)
  usePreformChart('memoryUsage', memoryActiveOption, memoryChart, memoryDomRef)

  return (
    <div className="w-full h-full flex flex-col justify-start">
      {
        <div
          ref={cpuDomRef}
          className={`flex-1 ${lineType ? 'flex' : 'hidden'}`}
          id="usage"
        />
      }
      {
        <div
          ref={memoryDomRef}
          className={cn(
            ' flex-1',
            activeType === 'account' && 'mt-5',
            memoryType ? 'flex' : 'hidden',
          )}
          id="memoryUsage"
        />
      }
    </div>
  )
}
