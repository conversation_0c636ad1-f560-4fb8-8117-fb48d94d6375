import { ColumnDef } from '@tanstack/react-table'
import { DataTableColumnHeader } from '@/components/LqTable/components/data-table-column-header'
import SpecialLinks from '@/pages/link/components/multipleLinks/special-links'
import { StateTag } from '@/components/StateTag'

import Dot from '@/assets/svg/Dot.svg?react'

// import { PathList } from "../../types/proxy-bridge-type"
import { SpecialLinkValueType } from '@/api/kv'
import { OrdersType } from '@/components/LqTable/@types/common'
import type { ExitNodeType } from '@/pages/link/data/type'

export function columns({
  isSpecial = false,
  exitNode,
  orders,
  setOrders,
}: {
  isSpecial?: boolean
  exitNode: ExitNodeType
  orders: OrdersType | undefined
  setOrders: React.Dispatch<React.SetStateAction<OrdersType | undefined>>
}): ColumnDef<SpecialLinkValueType>[] {
  return [
    {
      accessorKey: 'account',
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="用户名" />
      ),
      cell: ({ row }) => {
        const account = row.getValue('account') as string
        return (
          <div className="min-w-[120px] flex space-x-2">
            {account}
            {row.original.account_is_admin && (
              <StateTag
                active={{
                  className:
                    ' text-[#fff] leading-[18px] bg-[#065F46] text-xs ml-3',
                  desc: '管',
                }}
              />
            )}
          </div>
        )
      },
      enableSorting: false,
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id))
      },
    },
    {
      accessorKey: 'proxies',
      enableHiding: false,
      header: '链路',
      cell: ({ row }) => {
        // todo 正在使用中且不是当前设备使用中禁用链路切换
        return (
          <div className="min-w-[1147px] flex space-x-2 ">
            <SpecialLinks hopCount={row.original.hop} exitNode={exitNode} />
          </div>
        )
      },
      enableSorting: false,
      meta: {
        className: 'max-w-[1147px]',
      },
    },
    {
      accessorKey: 'use',
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title="状态"
          singleOrder
          orders={orders}
          setOrders={setOrders}
        />
      ),
      cell: ({ row }) => {
        const noUse = (
          <StateTag
            active={{
              className: 'border-[#D4D4D8] text-[#A1A1AA] border bg-[#F4F4F5]',
              desc: '未使用',
              icon: <Dot className="mr-1" fill="#A1A1AA" />,
            }}
          />
        )

        if (row.original.current_ip_use && !isSpecial) {
          return <div className="min-w-[80px]">{noUse}</div>
        }

        return (
          <div className="min-w-[80px]">
            {
              // todo 当前设备使用
              row.original.current_ip_use ? (
                <StateTag
                  active={{
                    className:
                      'border-[#3B82F6] text-[#1D4ED8] border bg-[#EFF6FF]',
                    desc: '当前使用中',
                    icon: <Dot className="mr-1" fill="#2563EB" />,
                  }}
                />
              ) : (
                <StateTag
                  active={{
                    className:
                      'border-[#14B8A6] text-[#0F766E] border bg-[#CCFBF1]',
                    desc: '使用中',
                    icon: <Dot className="mr-1" />,
                  }}
                />
              )
            }
          </div>
        )
      },
    },
  ]
}
