import { useEffect, useMemo, useState } from 'react'
import { useGlobalInfoContext } from '@/provider/GlobalInfoContextProvider'
import _ from 'lodash'
import { DataTableMemo } from '@/components/LqTable/data-table-scroll'
import { DataTableViewOptions } from '@/components/LqTable/components/data-table-view-options'
import { columns } from './columns'
import { UserManageEnum } from '../../enums'
import { ExitNodeType } from '@/pages/link/data/type'
// import { useLink } from "@/api/link"
import { KV_KEYS } from '@/data'
import {
  useStorageQuery,
  SpecialLinksType,
  SpecialLinkValueType,
} from '@/api/kv'
import { PathList } from '../../types/proxy-bridge-type'
import { SearchLocal } from './search-local'
import { useUserList } from '@/api/user'
import { useUserOutProxy } from '@/api/dashboard'
import { StateTag } from '@/components/StateTag'
import { isAdmin } from '@/auth'
import { countryCodeMap } from '@/pages/dashboard/data'
import { OrdersType } from '@/components/LqTable/@types/common'

export interface LinkType {
  contry: string
  flag: string
  exclusive?: string
  disabled?: boolean
  hideExclusiveStyle?: boolean
}

export interface DialogProps extends PathList {
  name: string
  nodeGuard: string
  nodeExit: string
  nodeDefault: string
  use: boolean
  exclusive: string | boolean
}

export const SpecialModeTable = () => {
  const { globalInfo } = useGlobalInfoContext()

  const { data: ipData, refetch: refetchIp } = useUserOutProxy()

  const [pagination] = useState({
    currentPage: 1,
    pageSize: 999,
    totalCount: 0,
  })
  const [orders, setOrders] = useState<OrdersType>()

  const { data: specialLinks } = useStorageQuery(KV_KEYS.SPECIAL_LINKS) as {
    data: { data: SpecialLinksType } | null
    refetch: () => void
  }

  // const { data: pathLink } = useLink()
  const { data: userList } = (isAdmin() && useUserList()) || {}

  const isWg = useMemo(() => false, [globalInfo]) // WG functionality removed

  const sortBy = (data: SpecialLinkValueType[], orders: OrdersType) => {
    const key = Object.keys(orders)[0]
    function getUse(v: SpecialLinkValueType) {
      return v.current_ip_use ? 1 : 2
    }
    return data.sort((a, b) => {
      const useComparison = getUse(a) - getUse(b)
      return orders[key]?.desc ? -useComparison : useComparison
    })
  }

  const exitNode = useMemo<ExitNodeType>(() => {
    return {
      name: countryCodeMap[ipData?.country_code?.toUpperCase() ?? ''],
      country_code: ipData?.country_code ?? '',
    }
  }, [ipData])

  const tableData = useMemo(() => {
    const specialLinksDt = JSON.parse(JSON.stringify(specialLinks)) as {
      data: SpecialLinksType
    } | null
    const you_ip = globalInfo?.data.device_information.you_ip
    if (you_ip && specialLinksDt && specialLinksDt?.data) {
      let currentData = specialLinksDt.data[you_ip]
      if (currentData) {
        currentData = { ...currentData, current_ip_use: true }
        specialLinksDt.data[you_ip] = currentData
      }
    }

    const list = (Object.values(specialLinksDt?.data ?? {}) ?? []).filter(
      (v) => v,
    )
    const data = sortBy(
      list,
      orders ? orders : { use: { desc: false, field: 'use' } },
    )
    return data || []
  }, [specialLinks, globalInfo, orders])

  const userFilter = useMemo(() => {
    const accountList = tableData?.map((item) => {
      return {
        account: item.account,
        is_admin: item.account_is_admin,
      }
    })
    const uniqueData = _.uniqBy(accountList, 'account')
    return uniqueData?.map((item) => ({
      label: item.account,
      value: item.account,
      icon: item.is_admin && (
        <StateTag
          active={{
            className:
              ' text-[#fff] leading-[14px] bg-[#065F46] text-xs mr-[2px]',
            desc: '管',
          }}
        />
      ),
    }))
  }, [userList, specialLinks])

  const columnsData = useMemo(() => {
    const columnList = columns({ isSpecial: isWg, exitNode, orders, setOrders })
    return columnList
  }, [orders, exitNode, isWg])

  useEffect(() => {
    refetchIp()
  }, [])

  return (
    <>
      <div className="flex rounded-md flex-1">
        <DataTableMemo
          className=" px-4 p-1 pb-0"
          pagination={pagination}
          data={tableData}
          columns={columnsData}
        >
          {{
            toolbar: (table) => (
              <SearchLocal
                table={table}
                options={userFilter}
                toolbar={
                  <DataTableViewOptions
                    table={table}
                    columnName={UserManageEnum}
                  />
                }
              />
            ),
          }}
        </DataTableMemo>
      </div>
    </>
  )
}
