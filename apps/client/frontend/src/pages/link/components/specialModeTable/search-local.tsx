import { Button } from '@/components/ui/button'
import { Table } from '@tanstack/react-table'
import IconHourglass from '@/assets/svg/hourglass.svg?react'
import {
  DataTableFacetedFilter,
  FacetedFilterExport,
} from '@/components/LqTable/components/data-table-faceted-filter'
import { isAdmin } from '@/auth'
import { useRef } from 'react'
interface IProps<TData> {
  table: Table<TData>
  toolbar?: React.ReactNode
  options?: {
    label: string
    value: string
    statistics?: number | undefined
    icon?: React.ReactNode
  }[]
}

export function SearchLocal<TData>({ table, toolbar, options }: IProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0
  const ref = useRef<FacetedFilterExport>(null)
  return (
    <>
      <div className={'flex items-center justify-between space-x-3'}>
        <div className="flex flex-1 items-center space-x-2">
          {isAdmin() && options && (
            <>
              <DataTableFacetedFilter
                ref={ref}
                column={table.getColumn('account')}
                placeholder="用户"
                options={options}
                value={['']}
                popButtonClass="w-[300px] "
                isLocal={true}
              />
              {isFiltered && (
                <Button
                  variant="ghost"
                  onClick={() => {
                    table.resetColumnFilters()
                    ref.current?.clear()
                  }}
                  className="h-10 px-4 py-2 ml-[10px] font-medium hover:bg-[#F4F4F5]"
                >
                  <IconHourglass />
                  重置过滤
                </Button>
              )}
            </>
          )}
        </div>
        <div className="min-w-[80px] h-10 flex justify-end">{toolbar}</div>
      </div>
    </>
  )
}
