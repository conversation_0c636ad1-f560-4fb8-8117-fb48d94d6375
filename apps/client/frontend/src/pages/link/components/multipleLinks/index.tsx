import { useEffect, useMemo, useState } from "react";
import { Combobox, IComboboxValue, optionsType } from "@/components/Combobox";
import { ButtonWithFlag } from "../buttonWithFlag";
import LinkButton from "@/assets/svg/linkButton.svg?react";
import DownWhite from "@/assets/svg/downWhite.svg?react";
import Down from "@/assets/svg/down.svg?react";
import { cn, getUrl } from "@/lib/utils";
import { LinkType } from "../..";
import "./index.scss";
// import { ButtonWithFlag } from "../buttonWithFlag";
import { PathList, ProxiesList } from "../../types/proxy-bridge-type";
import {
	usePathListMutation,
	useProxiesExit,
	useProxiesGuard,
} from "@/api/link";
import { ComboxTitle } from "../formAlertDialog/pathChoose";
import { errorToast } from "@/components/GlobalToast";
import { toast } from "@/components/ui/use-toast";
import { isWretchError } from "@/auth";
import { countryCodeMap } from "@/pages/dashboard/data";
import { startCountry } from "@/pages/data";
import { disableNode } from "@/pages/link/data/link-state";
import { useQueryClient } from "@tanstack/react-query";

export const SearchOptionText = (number_: number, option?: optionsType) => {
	const delayStyle = option?.delayStyle;
	return (
		<span
			style={{
				color: delayStyle || "",
			}}
			className={cn(
				" text-sm font-normal mr-2",
				number_ > 0 ? "text-[#71717A]" : "text-[#DC2626]",
			)}
		>
			{number_ > 0 ? number_ + "ms" : "超时"}
		</span>
	);
};

const comboboxProps = {
	showSearch: true,
	className:
		"h-7 fit-content data-[state=open]:bg-[#fff] ring-offset-background data-[state=open]:outline-none data-[state=open]:ring-2 data-[state=open]:ring-[#1E3A8A] data-[state=open]:ring-offset-2 data-[state=open]:border-[#D1D5DB] ",
	contentClass: "comboboxContent border border-[#1E3A8A] my-[6px] p-[5px]",
	itemClass: "px-2 py-2.5 ",
	radioText: SearchOptionText,
	showRadio: true,
	title: "输入节点名称...",
	inputClassName: "dashboard_node_search",
};

export const DefaultLink = ({
	withLink = true,
	className,
	noSelect = false,
	onlyIcon = false,
	disabled,
}: {
	withLink?: boolean;
	className?: string;
	noSelect?: boolean;
	onlyIcon?: boolean;
	disabled?: boolean;
}) => {
	const url = getUrl();
	const entryName = countryCodeMap[startCountry.country_code];
	const [entryOptions, setEntryOptions] = useState<
		{
			label: string;
			value: string;
			icon: JSX.Element;
			num: number;
		}[]
	>([]);

	const getOptions = () => {
		const delay = parseInt((Math.random() * 150 + 50).toFixed(0));
		setEntryOptions([
			{
				label: entryName ?? "",
				value: entryName ?? "",
				icon: (
					<div
						className={cn(
							"w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] shrink-0",
						)}
					>
						<img
							src={`${url}/res/flag3/${startCountry.country_code.toLowerCase()}.svg`}
							alt=""
							className="w-4 h-4 "
						/>
					</div>
				),
				num: delay,
			},
		]);
	};

	useEffect(() => {
		getOptions();
	}, []);

	return (
		<div className={cn("flex items-center renderButtonStyle", className)}>
			{noSelect
				? (
					<ButtonWithFlag
						index={0}
						contry={entryName}
						flag={startCountry.country_code || ""}
						onlyIcon={onlyIcon}
					/>
				)
				: (
					<Combobox
						value={entryName}
						{
							// onChange={handleComboxChange}
							...comboboxProps
						}
						typeBorder
						triggerClass={cn(
							"px-4 py-0  flex justify-center items-center text-xs",
						)}
						downIcon={<Down />}
						options={entryOptions}
						onComboxOpen={getOptions}
						muiltip={false}
						disabled={disabled}
						comboxTitle={<ComboxTitle type="entry" />}
					/>
				)}
			{withLink && <LinkButton className="linkbutton w-7 h-7" />}
		</div>
	);
};

const renderCombobox = (
	item: LinkType,
	index: number,
	length: number,
	linkList: PathList | null,
	onlyIcon?: boolean,
	noSelect?: boolean,
	onLinkChange?: () => void, // 新增：链路变化回调
) => {
	if (!item) return;

	const url = getUrl();
	const [_, setFlagText] = useState<ProxiesList>();
	const [selectValue, setSelectValue] = useState(item.contry);
	// const [ingressName, setIngressName] = useState('')

	const comboxType = index === length - 1 &&
		item.exclusive !== "none" &&
		!item.hideExclusiveStyle;

	const { data: proxiesList, refetch: refetchGuard } = useProxiesGuard();
	const { data: proxiesExit, refetch: refetchExit } = useProxiesExit();

	const pathListMutation = usePathListMutation();
	const queryClient = useQueryClient();

	const currentList = useMemo(() => {
		return index ? proxiesExit?.data : proxiesList?.data;
	}, [proxiesList, proxiesExit, index]);

	const handleComboxChange = (value: IComboboxValue) => {
		if (!linkList) return;
		setSelectValue(value as string);
		pathListMutation.mutate(
			{
				...linkList,
				use: linkList.use,
				proxies: linkList.proxies?.map((proxie, index_) => {
					if (index_ === index) {
						return value as string;
					}
					return proxie;
				}),
			},
			{
				onError: (error) => {
					setSelectValue(item.contry);
					errorTosatShow(error);
				},
				onSuccess: () => {
					setFlagText(
						currentList?.find((index_) => index_.name === value),
					);

					// 链路切换成功后，触发数据重新获取
					console.log(
						"MultipleLinks 节点切换成功，开始重新获取数据...",
					);

					// 延迟执行，避免与正在进行的 API 请求冲突
					setTimeout(() => {
						// 清除相关查询缓存，强制重新获取
						queryClient.invalidateQueries({
							queryKey: ["userOutProxy"],
						});
						queryClient.invalidateQueries({
							queryKey: ["useUserDnsProxy"],
						});
						queryClient.invalidateQueries({
							queryKey: ["proxyDelay"],
						});

						// 触发链路变化回调
						onLinkChange?.();

						console.log("已触发数据重新获取");
					}, 200); // 延迟 200ms 执行
				},
			},
		);
	};

	const errorTosatShow = (error: unknown, errorType?: string) => {
		if (isWretchError(error)) {
			const res = error.json;

			const errorMessages = {
				"already use": () =>
					errorToast("当前链路出口节点已被使用，无法私有", toast),
				default: () => errorToast(errorType ?? "修改失败", toast),
			};

			for (const [pattern, handler] of Object.entries(errorMessages)) {
				if (res.msg.includes(pattern)) {
					handler();
					break;
				}
			}
		} else {
			errorToast("修改失败", toast);
		}
	};

	const getOptions = (value: ProxiesList[]) => {
		const options = value
			.filter(
				(item) =>
					!disableNode.includes(item.use_status) ||
					item.name === selectValue,
			)
			.map((item) => {
				return {
					label: item.name,
					value: item.name,
					icon: (
						<div
							className={cn(
								"w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] shrink-0",
							)}
						>
							<img
								src={`${url}/res/flag3/${item.country_code.toLowerCase()}.svg`}
								alt=""
								className="w-4 h-4 "
							/>
						</div>
					),
					num: item.delay,
				};
			});
		return options;
	};

	const currentOption = useMemo(() => {
		if (!currentList) return [];
		setFlagText(currentList.find((index_) => index_.name === item.contry));
		return getOptions(currentList);
	}, [currentList, item, selectValue]);

	// const ingressOptions = useMemo(() => {
	//   if (!flagText) return []
	//   const ingressCode = flagText?.ingress_country_code.toUpperCase() ?? ''
	//   setIngressName(countryCodeMap[ingressCode])
	//   return [
	//     {
	//       label: countryCodeMap[ingressCode] ?? '',
	//       value: countryCodeMap[ingressCode] ?? '',
	//       icon: (
	//         <div
	//           className={cn(
	//             'w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] shrink-0',
	//           )}
	//         >
	//           <img
	//             src={`${url}/res/flag/${ingressCode}.png`}
	//             alt=""
	//             className="w-4 h-4 "
	//           />
	//         </div>
	//       ),
	//       num: flagText?.delay,
	//     },
	//   ]
	// }, [flagText])

	useEffect(() => {
		setSelectValue(item.contry);
	}, [item]);

	return (
		<div className="flex items-center" key={index}>
			<div className="flex items-center" key={index}>
				<div className="flex items-center renderButtonStyle text-xs">
					{
						/* <ButtonWithFlag
						index={index}
						contry={
							countryCodeMap[flagText?.ingress_country_code.toUpperCase() || ""]
						}
						flag={flagText?.ingress_country_code.toUpperCase() || ""}
						onlyIcon={onlyIcon}
					/> */
					}
					<LinkButton className="linkbutton w-7 h-8" />
				</div>
				{noSelect
					? (
						<ButtonWithFlag
							index={0}
							contry={selectValue}
							flag={item.flag.toUpperCase() || ""}
							onlyIcon={onlyIcon}
						/>
					)
					: (
						<Combobox
							value={selectValue}
							onChange={handleComboxChange}
							{...comboboxProps}
							typeBorder
							onlyIcon={onlyIcon}
							triggerClass={cn(
								"px-4 py-0  flex justify-center items-center text-xs",
								index > 0 ? "jump5-btn" : "jump3-btn",
								comboxType
									? "bg-[#1E3A8A] text-[#fff] hover:bg-[#1E3A8A] hover:text-[#fff] data-[state=open]:bg-[#1E3A8A]  data-[state=open]:text-[#fff]"
									: "bg-[#F9FAFB]",
							)}
							downIcon={comboxType
								? <DownWhite className="linkbutton " />
								: <Down />}
							options={currentOption}
							onComboxOpen={index ? refetchExit : refetchGuard}
							muiltip={false}
							disabled={item.disabled}
							comboxTitle={
								<ComboxTitle
									type={index === length - 1 ? "" : "guard"}
								/>
							}
						/>
					)}
				{index !== length - 1 && (
					<div className="w-7 h-8">
						<LinkButton className="linkbutton" />
					</div>
				)}
			</div>
		</div>
	);
};

export const MultipleLinks = ({
	linkList,
	onlyIcon,
	className,
	disabled,
	hideExclusiveStyle,
	noSelect,
	onLinkChange, // 新增：链路变化回调
}: {
	linkList: PathList | null;
	onlyIcon?: boolean;
	className?: string;
	disabled?: boolean;
	hideExclusiveStyle?: boolean;
	noSelect?: boolean;
	onLinkChange?: () => void; // 新增：链路变化回调
}) => {
	const pathList = useMemo(() => {
		let newList = new Array(2).fill(null);
		newList = newList.map((_, index) => {
			return {
				contry: linkList?.proxies?.[index] ?? "",
				flag: linkList?.proxies_code?.[index] ?? "",
				exclusive: false, // Exclusive functionality removed
				hideExclusiveStyle,
				disabled,
			};
		});

		return newList;
	}, [linkList, disabled, hideExclusiveStyle]);

	return (
		<div className={cn("flex items-center ", className)}>
			<DefaultLink
				noSelect={true}
				onlyIcon={onlyIcon}
				disabled={disabled}
			/>
			{pathList.map((item, index) => {
				return renderCombobox(
					item,
					index,
					pathList.length,
					linkList,
					onlyIcon,
					noSelect,
					onLinkChange, // 传递链路变化回调
				);
			})}
		</div>
	);
};
