import React, { useEffect, useRef, useState } from 'react'
import LinkButton from '@/assets/svg/linkButton.svg?react'
import InletNodeSvg from '@/assets/svg/link/InletNode.svg?react'
import HiddenNodeSvg from '@/assets/svg/link/HiddenNode.svg?react'
import GuardNodeSvg from '@/assets/svg/link/GuardNode.svg?react'

import { cn, getUrl } from '@/lib/utils'

interface HiddenLinkProps {
  text: string
  slot: React.ReactNode
  withLink?: boolean
  className?: string
  onlyIcon?: boolean
}
export const HiddenLink = ({
  text,
  slot,
  withLink = true,
  className,
  onlyIcon,
}: HiddenLinkProps) => {
  return (
    <div
      className={cn('flex items-center flex-nowrap overflow-hidden', className)}
    >
      <div
        className={cn(
          'py-2 px-4 rounded-md h-7 bg-[#F9FAFB] text-xs font-medium leading-5 flex flex-nowrap items-center justify-start space-x-2 border border-[#D4D4D8',
        )}
      >
        {slot}
        {!onlyIcon && (
          <span className="text-[#18181B] flex flex-nowrap whitespace-nowrap">
            {text}
          </span>
        )}
      </div>
      {withLink && <LinkButton className="linkbutton w-7 h-7" />}
    </div>
  )
}

type NodeInfo = { text: string; icon: React.ReactNode }
type NodeType = 'inlet' | 'hidden' | 'guard'

const nodeCategoryMap: Record<NodeType, NodeInfo> = {
  inlet: {
    text: '入口节点',
    icon: <InletNodeSvg className="w-4 h-4" fill="#18181B" />,
  },
  hidden: {
    text: '隐匿节点',
    icon: <HiddenNodeSvg className="w-4 h-4" fill="#18181B" />,
  },
  guard: {
    text: '中继节点',
    icon: <GuardNodeSvg className="w-4 h-4" fill="#18181B" />,
  },
}

const nodeInfoMap: Record<string, NodeInfo[]> = {
  '3': [nodeCategoryMap.inlet, nodeCategoryMap.hidden],
  '4': [nodeCategoryMap.inlet, nodeCategoryMap.hidden, nodeCategoryMap.guard],
  '5': [
    nodeCategoryMap.inlet,
    nodeCategoryMap.hidden,
    nodeCategoryMap.guard,
    nodeCategoryMap.hidden,
  ],
}

interface SpecialLinksProps {
  hopCount: string
  exitNode: {
    name: string
    country_code: string
  } | null
  onlyIcon?: boolean
}

const SpecialLinks = ({ hopCount, exitNode, onlyIcon }: SpecialLinksProps) => {
  const url = getUrl()
  const [count, setCount] = useState('3')

  const getNodeInfo = (hopCount: string, index: number) => {
    const nodes = nodeInfoMap[hopCount] || []
    return (
      nodes[index] || {
        text: exitNode?.name ?? '',
        icon: (
          <img
            src={`${url}/res/flag3/${exitNode?.country_code.toLowerCase()}.svg`}
            alt=""
            className="w-4 h-4"
          />
        ),
      }
    ) // 返回对应节点或默认值
  }

  const timer = useRef<NodeJS.Timeout | null>(null)
  useEffect(() => {
    timer.current && clearTimeout(timer.current)
    timer.current = setTimeout(() => {
      setCount(hopCount)
      timer.current && clearTimeout(timer.current)
    }, 300)
  }, [hopCount])

  return (
    <div className="flex items-center">
      {Array.from({ length: Number(count) }).map((_, index) => {
        const info = getNodeInfo(count, index)
        return (
          <HiddenLink
            key={index}
            text={info.text}
            slot={info.icon}
            withLink={index !== Number(count) - 1}
            onlyIcon={onlyIcon}
          />
        )
      })}
    </div>
  )
}

export default SpecialLinks
