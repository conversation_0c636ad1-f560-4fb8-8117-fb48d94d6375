import { Combobox, IComboboxValue, optionsType } from "@/components/Combobox";
import LinkButton from "@/assets/svg/linkButton.svg?react";
import DownWhite from "@/assets/svg/downWhite.svg?react";
import Down from "@/assets/svg/down.svg?react";
import EntryNode from "@/assets/svg/link/EntryNode.svg?react";
import GuardNode from "@/assets/svg/link/GuardNode.svg?react";
import ExitNode from "@/assets/svg/link/ExitNode.svg?react";
import { cn, getUrl } from "@/lib/utils";
import { LinkType } from "../..";
import "./index.scss";
// import { ButtonWithFlag } from '../buttonWithFlag'
import { PathList, ProxiesList } from "../../types/proxy-bridge-type";
import { Button } from "@/components/ui/button";
import { useEffect, useMemo, useState } from "react";
import {
	PROXIES_TYPE,
	usePathListMutation,
	useProxiesExit,
	useProxiesGuard,
	useProxiesIngress,
} from "@/api/link";
import { ComboxTitle, getMedianIndex } from "../formAlertDialog/pathChoose";
import { errorToast } from "@/components/GlobalToast";
import { toast } from "@/components/ui/use-toast";
import { isWretchError } from "@/auth";
import { DELAY_TYPE_STYLE } from "../../types/config";
import { useQueryClient } from "@tanstack/react-query";

// import { useGlobalInfoContext } from '@/provider/GlobalInfoContextProvider'

export const SearchOptionText = (num: number, option?: optionsType) => {
	const delayStyle = option?.delayStyle;
	return (
		<span
			style={{
				color: delayStyle || "",
			}}
			className={cn(
				" text-sm font-normal mr-2",
				num > 0 ? "text-[#71717A]" : "text-[#DC2626]",
			)}
		>
			{num > 0 ? num + "ms" : "超时"}
		</span>
	);
};

const comboboxProps = {
	showSearch: true,
	className:
		"h-7 fit-content data-[state=open]:bg-[#fff] ring-offset-background data-[state=open]:outline-none data-[state=open]:ring-2 data-[state=open]:ring-[#1E3A8A] data-[state=open]:ring-offset-2 data-[state=open]:border-[#D1D5DB] ",
	contentClass: "comboboxContent border border-[#1E3A8A] my-[6px] p-[5px]",
	itemClass: "px-2 py-2.5 ",
	radioText: SearchOptionText,
	showRadio: true,
	title: "输入节点名称...",
};

// 通用的默认节点按钮组件
export const DefaultNodeButton = ({
	nodeType,
	withLink = true,
	className,
}: {
	nodeType: "entry" | "guard" | "exit";
	withLink?: boolean;
	className?: string;
}) => {
	const nodeConfig = {
		entry: {
			icon: EntryNode,
			label: "入口节点",
		},
		guard: {
			icon: GuardNode,
			label: "中继节点",
		},
		exit: {
			icon: ExitNode,
			label: "出口节点",
		},
	};

	const config = nodeConfig[nodeType];
	const IconComponent = config.icon;

	return (
		<div className={cn("flex items-center renderButtonStyle", className)}>
			<Button
				className={cn(
					"h-7 bg-[#E5E7EB] text-xs justify-start border border-[#D4D4D8] pr-[17px]",
				)}
				disabled
			>
				<IconComponent className="w-4 h-4 mr-2 fill-[#18181B]" />
				<span className="text-[#18181B]">{config.label}</span>
			</Button>
			{withLink && <LinkButton className="linkbutton w-7 h-7" />}
		</div>
	);
};

// 保持向后兼容性的 DefaultLink 组件
export const DefaultLink = ({
	withLink = true,
	className,
}: {
	withLink?: boolean;
	className?: string;
}) => {
	return (
		<DefaultNodeButton
			nodeType="entry"
			withLink={withLink}
			className={className}
		/>
	);
};

const renderCombobox = (
	item: LinkType,
	index: number,
	length: number,
	linkList: PathList,
	onlyIcon?: boolean,
	onLinkChange?: () => void, // 新增：链路变化回调
) => {
	if (!item) return;
	// const { globalInfo } = useGlobalInfoContext()
	const [_, setFlagText] = useState<ProxiesList>();
	const [selectValue, setSelectValue] = useState(item.contry);
	const comboxType = index < 2 && item.exclusive !== "none" &&
		!item.hideExclusiveStyle;

	const {
		data: proxiesList,
		refetch: refetchGuard,
		isLoading: isLoadingGuard,
		error: errorGuard,
	} = useProxiesGuard();
	const {
		data: proxiesExit,
		refetch: refetchExit,
		isLoading: isLoadingExit,
		error: errorExit,
	} = useProxiesExit();
	const {
		data: proxiesPassGfw,
		refetch: refetchPassGfw,
		isLoading: isLoadingIngress,
		error: errorIngress,
	} = useProxiesIngress();

	const pathListMutation = usePathListMutation();
	const queryClient = useQueryClient();

	const currentList = useMemo(() => {
		console.log(length, "length");
		let newType = index || 0;
		switch (newType) {
			case 0:
				return proxiesPassGfw?.data || [];
			case 1:
				return proxiesList?.data || [];
			case 2:
				return proxiesExit?.data || [];
			default:
				return proxiesList?.data || [];
		}
	}, [proxiesList, proxiesExit, proxiesPassGfw, index]);

	const isLoading = useMemo(() => {
		let newType = index || 0;
		switch (newType) {
			case 0:
				return isLoadingIngress;
			case 1:
				return isLoadingGuard;
			case 2:
				return isLoadingExit;
			default:
				return isLoadingGuard;
		}
	}, [isLoadingIngress, isLoadingGuard, isLoadingExit, index]);

	const currentError = useMemo(() => {
		let newType = index || 0;
		switch (newType) {
			case 0:
				return errorIngress;
			case 1:
				return errorGuard;
			case 2:
				return errorExit;
			default:
				return errorGuard;
		}
	}, [errorIngress, errorGuard, errorExit, index]);
	const currentListFn = useMemo(() => {
		let newType = index || 0;
		switch (newType) {
			case 0:
				return refetchPassGfw;
			case 1:
				return refetchGuard;
			case 2:
				return refetchExit;
			default:
				return refetchGuard;
		}
	}, [proxiesList, proxiesExit, proxiesPassGfw]);

	const handleComboxChange = (value: IComboboxValue) => {
		setSelectValue(value as string);
		console.log(
			{
				...linkList,
				proxies: linkList.proxies?.map((proxie, i) => {
					if (i === index) {
						return value as string;
					}
					return proxie;
				}),
			},
			"linkListlinkList",
		);
		pathListMutation.mutate(
			{
				...linkList,
				use: linkList.use,
				proxies: linkList.proxies?.map((proxie, i) => {
					if (i === index) {
						return value as string;
					}
					return proxie;
				}),
			},
			{
				onError: (error) => {
					setSelectValue(item.contry);
					errorTosatShow(error);
				},
				onSuccess: () => {
					setFlagText(
						currentList?.find((i: any) => i.name === value),
					);

					// 链路切换成功后，触发数据重新获取
					console.log("多跳链路节点切换成功，开始重新获取数据...");

					// 延迟执行，避免与正在进行的 API 请求冲突
					setTimeout(() => {
						// 清除相关查询缓存，强制重新获取
						queryClient.invalidateQueries({
							queryKey: ["userOutProxy"],
						});
						queryClient.invalidateQueries({
							queryKey: ["useUserDnsProxy"],
						});
						queryClient.invalidateQueries({
							queryKey: ["proxyDelay"],
						});

						// 触发链路变化回调
						onLinkChange?.();

						console.log("已触发数据重新获取");
					}, 200); // 延迟 200ms 执行
				},
			},
		);
	};

	const errorTosatShow = (err: unknown, errType?: string) => {
		if (isWretchError(err)) {
			const res = err.json;

			const errorMessages = {
				"already use": () =>
					errorToast("当前链路出口节点已被使用，无法独占", toast),
				default: () => errorToast(errType ?? "修改失败", toast),
			};

			for (const [pattern, handler] of Object.entries(errorMessages)) {
				if (res.msg.includes(pattern)) {
					handler();
					break;
				}
			}
		} else {
			errorToast("修改失败", toast);
		}
	};

	const getOptions = (value_: ProxiesList[]) => {
		const url = getUrl();
		value_?.forEach((item, index) => {
			item.i = index;
		});
		const optionsFilter = value_.filter(
			(item) =>
				item.use_status !== "exclusive" || item.name === selectValue,
		);
		// 超时了的数组
		const delayListTimeout = [];
		// 未超时的数组
		const okList = [];
		for (let i = 0; i < optionsFilter.length; i++) {
			const item = optionsFilter[i];
			if (item.delay > 0) {
				okList.push(item);
			} else {
				item.delayStyle = DELAY_TYPE_STYLE.HIGH;
				delayListTimeout.push(item);
			}
		}
		// okList 通过 delay 排序从小到大
		okList.sort((a, b) => a.delay - b.delay);
		const midianIndex = getMedianIndex(okList);
		okList.forEach((item, index) => {
			if (index <= midianIndex) {
				item.delayStyle = DELAY_TYPE_STYLE.LOW;
			} else {
				item.delayStyle = DELAY_TYPE_STYLE.MID;
			}
		});

		const options = [...okList, ...delayListTimeout]
			.sort((a, b) => (a.i ?? 0) - (b.i ?? 0))
			.map((item) => {
				return {
					label: item.name,
					value: item.name,
					icon: (
						<div
							className={cn(
								"w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] shrink-0",
							)}
						>
							{item.country_code &&
									item.country_code.trim() !== ""
								? (
									<>
										<img
											src={`${url}/res/flag3/${item.country_code.toLowerCase()}.svg`}
											alt={item.country_code}
											className="w-4 h-4"
											onError={(e) => {
												// 图标加载失败时显示默认图标
												const target = e
													.target as HTMLImageElement;
												target.style.display = "none";
												// 显示默认图标
												const parent =
													target.parentElement;
												if (
													parent &&
													!parent.querySelector(
														".default-flag-icon",
													)
												) {
													const defaultIcon = document
														.createElement("div");
													defaultIcon.className =
														"default-flag-icon w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center";
													defaultIcon.innerHTML =
														'<span class="text-xs text-gray-600">?</span>';
													parent.appendChild(
														defaultIcon,
													);
												}
											}}
										/>
									</>
								)
								: (
									// 当没有 country_code 时显示默认图标
									<div className="w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center">
										<span className="text-xs text-gray-600">
											?
										</span>
									</div>
								)}
						</div>
					),
					num: item.delay,
					delayStyle: item.delayStyle,
				};
			});
		return options;
	};

	// const flags = useMemo(() => {
	//   return globalInfo?.data.flags || []
	// }, [globalInfo])

	const currentOption = useMemo(() => {
		if (!currentList || currentList.length === 0) return [];
		setFlagText(currentList.find((i: any) => i.name === item.contry));
		return getOptions(currentList);
	}, [currentList, item, selectValue]);

	useEffect(() => {
		setSelectValue(item.contry);
	}, [item]);

	// Show loading state
	if (isLoading) {
		return (
			<div className="flex items-center" key={index}>
				<div className="flex items-center renderButtonStyle text-xs">
				</div>
				<Button
					className={cn(
						"h-7 px-4 py-0 flex justify-center items-center text-xs",
						comboxType
							? "bg-[#1E3A8A] text-[#fff]"
							: "bg-[#F9FAFB]",
					)}
					disabled
				>
					<span className="text-xs">加载中...</span>
				</Button>
				{index < 2 && (
					<div className="w-7 h-8">
						<LinkButton className="linkbutton" />
					</div>
				)}
			</div>
		);
	}

	// Show error state
	if (currentError) {
		return (
			<div className="flex items-center" key={index}>
				<div className="flex items-center renderButtonStyle text-xs">
				</div>
				<Button
					className={cn(
						"h-7 px-4 py-0 flex justify-center items-center text-xs",
						"bg-[#FEE2E2] text-[#DC2626] border-[#FECACA]",
					)}
					disabled
				>
					<span className="text-xs">加载失败</span>
				</Button>
				{index < 2 && (
					<div className="w-7 h-8">
						<LinkButton className="linkbutton" />
					</div>
				)}
			</div>
		);
	}

	return (
		<div className="flex items-center" key={index}>
			<div className="flex items-center renderButtonStyle text-xs">
			</div>
			<Combobox
				value={selectValue}
				onChange={handleComboxChange}
				{...comboboxProps}
				typeBorder
				onlyIcon={onlyIcon}
				triggerClass={cn(
					"px-4 py-0  flex justify-center items-center text-xs",
					comboxType
						? "bg-[#1E3A8A] text-[#fff] hover:bg-[#1E3A8A] hover:text-[#fff] data-[state=open]:bg-[#1E3A8A]  data-[state=open]:text-[#fff]"
						: "bg-[#F9FAFB]",
				)}
				downIcon={comboxType
					? <DownWhite className="linkbutton " />
					: <Down />}
				options={currentOption}
				onComboxOpen={currentListFn}
				muiltip={false}
				disabled={item.disabled}
				comboxTitle={
					<ComboxTitle
						type={(PROXIES_TYPE[index] as string) || "guard"}
					/>
				}
			/>
			{index < 2 && (
				<div className="w-7 h-8">
					<LinkButton className="linkbutton" />
				</div>
			)}
		</div>
	);
};

export const MultipleLinksJump3 = ({
	linkList,
	onlyIcon,
	className,
	disabled,
	hideExclusiveStyle,
	onLinkChange, // 新增：链路变化回调
}: {
	linkList: PathList;
	onlyIcon?: boolean;
	className?: string;
	disabled?: boolean;
	hideExclusiveStyle?: boolean;
	onLinkChange?: () => void; // 新增：链路变化回调
}) => {
	const pathList = useMemo(() => {
		let newList = new Array(3).fill(null);
		newList = newList.map((_, index) => {
			const contry = linkList.proxies?.[index];
			// 修复：为所有三个位置创建节点对象，即使 contry 为空
			// 这样可以确保三跳链路控件都能正确显示
			return {
				contry: contry || "",
				flag: linkList.proxies_code?.[index] || "",
				exclusive: false,
				hideExclusiveStyle,
				disabled,
			};
		});
		return newList;
	}, [linkList, disabled, hideExclusiveStyle]);

	return (
		<div className={cn("flex items-center ", className)}>
			{pathList.map((item, index) => {
				// 确保所有三个位置都有控件显示
				if (!item.contry || item.contry === "") {
					// 根据索引显示对应的默认节点
					const nodeTypes: Array<"entry" | "guard" | "exit"> = [
						"entry",
						"guard",
						"exit",
					];
					const nodeType = nodeTypes[index] || "guard";
					return (
						<DefaultNodeButton
							key={`default-${index}`}
							nodeType={nodeType}
							withLink={index < 2}
						/>
					);
				}
				// 有数据时渲染正常的 combobox
				return renderCombobox(
					item,
					index,
					pathList.length,
					linkList,
					onlyIcon,
					onLinkChange, // 传递链路变化回调
				);
			})}
		</div>
	);
};
