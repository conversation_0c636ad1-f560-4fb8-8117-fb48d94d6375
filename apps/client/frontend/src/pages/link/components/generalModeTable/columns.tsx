import { ColumnDef } from "@tanstack/react-table";
import { EllipsisTooltip } from "@/components/encapsulation";
import { DurationDropDown } from "../durationDropDown";
import { DataTableColumnHeader } from "@/components/LqTable/components/data-table-column-header";
import { MultipleLinks } from "../multipleLinks";
import { MultipleLinksJump3 } from "../multipleLinksJump3";
import { StateTag } from "@/components/StateTag";
import Edit from "@/assets/svg/Edit.svg?react";
import DelWhite from "@/assets/svg/delWhite.svg?react";
import UseWhite from "@/assets/svg/UseWhite.svg?react";
import Use from "@/assets/svg/Use.svg?react";
import { Tooltip } from "antd";
import Dot from "@/assets/svg/Dot.svg?react";
import Delete from "@/assets/svg/Delete.svg?react";
import HelpCircle from "@/assets/svg/HelpCircle.svg?react";
import { Switch } from "@/components/ui/switch";
import { PathList } from "../../types/proxy-bridge-type";
// import { currentUser } from "@/auth"; // Removed - single user mode
import { OrdersType } from "@/components/LqTable/@types/common";
export function columns({
	flags,
	exclusive_num,
	isSpecial = false,
	editHandle,
	onSwitchHandle,
	setUseData,
	setUseVisible,
	setDeleteData,
	setDelVisible,
	orders,
	setOrders,
}: {
	exclusive_num: number;
	flags?: string[];
	isSpecial?: boolean;
	setUseData: (data: PathList) => void;
	setDeleteData: (data: PathList) => void;
	editHandle: (data: PathList) => void;
	onSwitchHandle: (row: PathList) => void;
	onStateHandle: (row: PathList) => void;
	setDelVisible: (data: boolean) => void;
	setUseVisible: (data: boolean) => void;
	orders: OrdersType | undefined;
	setOrders: React.Dispatch<React.SetStateAction<OrdersType | undefined>>;
}): ColumnDef<PathList>[] {
	const columns: ColumnDef<PathList>[] = [
		{
			accessorKey: "name",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="链路名" />
			),
			cell: ({ row }) => {
				return (
					<div className="min-w-[120px] flex space-x-2">
						<EllipsisTooltip text={row.getValue("name")} />
					</div>
				);
			},
			enableSorting: false,
		},
		{
			accessorKey: "proxies",
			enableHiding: false,
			header: ({ column }) => (
				<DataTableColumnHeader
					showHide={false}
					orders={orders}
					singleOrder
					setOrders={setOrders}
					column={column}
					title={
						<div className="flex items-center">
							<span className="mr-1">链路</span>
							{flags?.includes("jump3_disable") && (
								<Tooltip
									placement="bottomLeft"
									title={
										<div className="text-sm my-[2px] mx-1">
											1、链路提供全流量代理传输功能，将流量转发至您的目标站点。
											<br />
											2、链路构建引入0day漏洞，提供更全面的隐私保护能力，增加网络活动被追踪或监控的难度。
											<br />
											3、部分节点为所控的物联网设备，故所选链路可能由2个及以上物联网设备组成，形成多层加密的传输链路以增强数据匿名性。
											<br />
											4、当链路组成节点异常时，系统将动态调整同区域节点作为替换，保障业务的连续性；若此区域节点全部异常，则开启断线保护，阻止由本地网络上网，避免真实身份信息泄露。
										</div>
									}
									overlayClassName="max-w-[968px] w-[968px]"
									arrow={{ pointAtCenter: true }}
									autoAdjustOverflow={true}
								>
									<div>
										<HelpCircle />
									</div>
								</Tooltip>
							)}
						</div>
					}
				/>
			),
			cell: ({ row }) => {
				// todo 正在使用中且不是当前设备使用中禁用链路切换
				// const disabled = row.original.account !== currentUser()?.username;
				const disabled = false;
				// const noSelect = !!row.original.change_time
				return (
					<div className="min-w-[60px] flex space-x-2 ">
						{flags?.includes("jump3_disable") ? (
							<MultipleLinks linkList={row.original} disabled={disabled} />
						) : (
							<MultipleLinksJump3 linkList={row.original} disabled={disabled} />
						)}
					</div>
				);
			},
			enableSorting: false,
			meta: {
				className: "max-w-[800px]",
			},
		},
		{
			accessorKey: "use",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title="状态"
					singleOrder
					orders={orders}
					setOrders={setOrders}
				/>
			),
			cell: ({ row }) => {
				const noUse = (
					<StateTag
						active={{
							className: "border-[#D4D4D8] text-[#A1A1AA] border bg-[#F4F4F5]",
							desc: "未使用",
							icon: <Dot className="mr-1" fill="#A1A1AA" />,
						}}
					/>
				);
				if (row.original.current_ip_use && isSpecial) {
					return (
						<div className="min-w-[80px]" data-link-use={row.original.name}>
							{noUse}
						</div>
					);
				}
				return (
					<div className="min-w-[80px]" data-link-use={row.original.name}>
						{
							// todo 当前设备使用
							row.original.current_ip_use ? (
								<StateTag
									active={{
										className:
											"border-[#3B82F6] text-[#1D4ED8] border bg-[#EFF6FF]",
										desc: "当前使用中",
										icon: <Dot className="mr-1" fill="#2563EB" />,
									}}
								/>
							) : row.getValue("use") ? (
								<StateTag
									active={{
										className:
											"border-[#14B8A6] text-[#0F766E] border bg-[#CCFBF1]",
										desc: "使用中",
										icon: <Dot className="mr-1" />,
									}}
								/>
							) : (
								noUse
							)
						}
					</div>
				);
			},
		},
		{
			id: "actions",
			header: "操作",
			cell: ({ row }) => {
				const disabledUse = row.original.use;
				// const disabled = row.original.account !== currentUser()?.username;
				return (
					<div
						className="flex items-center"
						data-link-actions={row.original.name}
					>
						{disabledUse ? (
							<div className="p-2.5 rounded-md  ">
								<DelWhite className="w-5" />
							</div>
						) : (
							<Tooltip title="删除">
								<div
									className="p-2.5 rounded-md hover:bg-[#E5E7EB] cursor-pointer"
									onClick={() => {
										setDeleteData(row.original);
										setDelVisible(true);
									}}
								>
									<Delete className="w-5" />
								</div>
							</Tooltip>
						)}
						<Tooltip title="编辑">
							<div
								className="p-2.5 rounded-md hover:bg-[#E5E7EB] cursor-pointer"
								onClick={() => editHandle(row.original)}
							>
								<Edit className="w-5" />
							</div>
						</Tooltip>
						{disabledUse || isSpecial ? (
							<div className="p-2.5 rounded-md  ">
								<UseWhite className="w-10" fill="#E4E4E7" />
							</div>
						) : (
							<Tooltip title="应用">
								<div
									className="p-2.5 rounded-md hover:bg-[#E5E7EB] cursor-pointer"
									onClick={() => {
										setUseData(row.original);
										setUseVisible(true);
									}}
								>
									<Use className="w-10" />
								</div>
							</Tooltip>
						)}
					</div>
				);
			},
			meta: {
				className: "w-[60px]",
				isFixed: true,
			},
		},
	];

	// 如果没有exclusive_disable标志，则添加私有出口列
	if (!flags?.includes("exclusive_disable")) {
		columns.splice(2, 0, {
			accessorKey: "exclusive",
			header: ({ column }) => (
				<DataTableColumnHeader
					column={column}
					title={
						<div className="flex items-center">
							<span className="mr-1">私有出口</span>
							<Tooltip
								title={`当前系统版本支持${exclusive_num}个私有节点`}
								overlayClassName="text-sm"
							>
								<div>
									<HelpCircle />
								</div>
							</Tooltip>
						</div>
					}
				/>
			),
			cell: ({ row }) => {
				const value = row.getValue("exclusive") === "none" ? false : true;
				const disabled =
					false || // Account functionality removed - single user mode
					!row.original.use ||
					row.original.change_time > 0;
				return (
					<div
						className="min-w-[120px]"
						data-link-exclusive={row.original.name}
					>
						<Switch
							className="data-[state=checked]:bg-[#1E3A8A] "
							checked={value}
							disabled={disabled}
							onCheckedChange={() => onSwitchHandle(row.original)}
						/>
					</div>
				);
			},
			enableSorting: false,
		});
	}

	if (!flags?.includes("jump_change_disable")) {
		columns.splice(flags?.includes("exclusive_disable") ? 3 : 4, 0, {
			accessorKey: "change_time",
			header: ({ column }) => (
				<DataTableColumnHeader column={column} title="跳变时长" />
			),
			cell: ({ row }) => {
				return (
					<div data-link-time={row.original.name}>
						<DurationDropDown data={row.original} />
					</div>
				);
			},
			meta: {
				className: "min-w-[150px]",
			},
		});
	}
	return columns;
}
