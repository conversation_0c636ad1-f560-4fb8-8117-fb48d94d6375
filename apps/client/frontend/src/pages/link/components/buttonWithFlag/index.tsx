import { Button } from '@/components/ui/button'
import { cn, getUrl } from '@/lib/utils'

export const ButtonWithFlag = ({
  index,
  contry,
  className,
  flag,
  onlyIcon,
  disabled,
}: {
  index: number
  contry: string
  flag: string
  className?: string
  onlyIcon?: boolean
  disabled?: boolean
}) => {
  const url = getUrl()
  return (
    <Button
      disabled={disabled}
      className={cn(
        'h-7 bg-[#F9FAFB] text-xs text-[#18181B] justify-start border border-[#D1D5DB]',
        className,
      )}
      key={index}
    >
      <div
        className={cn(
          'w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] mr-2 shrink-0',
          onlyIcon ? 'mr-0' : '',
        )}
      >
        <img
          src={`${url}/res/flag3/${flag.toLowerCase()}.svg`}
          alt=""
          className="w-4 h-4"
        />
      </div>
      {onlyIcon ? '' : contry}
    </Button>
  )
}
