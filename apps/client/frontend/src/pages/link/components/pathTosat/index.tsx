import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'

export const PathTosat = ({
  exclusive_num,
  open,
  openChange,
}: {
  exclusive_num: number
  open: boolean
  openChange: (open: boolean) => void
}) => {
  return (
    <Dialog open={open} onOpenChange={() => openChange(false)}>
      <DialogContent
        className="w-[600px]"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="text-[#18181B]">
            已私有系统版本允许的最大{exclusive_num}个出口节点
          </DialogTitle>
          <DialogDescription className="text-[#71717A] font-normal">
            当前系统版本支持私有{exclusive_num}个出口节点
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button
            type="submit"
            className="bg-[#1E3A8A] hover:bg-[#1E3A8A] font-medium text-sm "
            onClick={() => openChange(false)}
          >
            确认
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
