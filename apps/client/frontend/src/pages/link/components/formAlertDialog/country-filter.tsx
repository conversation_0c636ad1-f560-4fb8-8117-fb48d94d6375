import * as React from 'react'
import { CaretSortIcon } from '@radix-ui/react-icons'

import Down from '@/assets/svg/down.svg?react'
import { cn, debounce } from '@/lib/utils'
import { Badge } from '@/components/ui/badge'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

import Radio from '@/assets/svg/radio.svg?react'
import { X } from 'lucide-react'

interface CountryFilterProps {
  value?: string[]
  options: {
    label: string
    value: string
    statistics?: number | undefined
    icon?: React.ComponentType<{ className?: string }> | React.ReactElement
  }[]
  placeholder?: string
  onValueChange: (value: string[]) => void
  comboxTitle?: string
  title?: string
}

// 搜索栏下拉多选
export function CountryFilter({
  value,
  options,
  placeholder,
  onValueChange,
  comboxTitle,
  title,
}: CountryFilterProps) {
  const selectedValues = new Set(value)
  const selectedRef = React.useRef<HTMLDivElement | null>(null)
  const triggerRef = React.useRef<HTMLDivElement>(null!)

  const [hasOver, setHasOver] = React.useState(false)
  const [popoverContentWidth, setPopoverContentWidth] = React.useState(254)

  const [hiddenSelected, setHiddenSelected] = React.useState(true)
  const hideSelected = React.useRef(true)

  const getSelectedWidth = () => {
    const selectedWidth = selectedRef.current?.clientWidth ?? 0
    const selectedItemList = selectedRef.current?.querySelectorAll(
      '.selected_item',
    ) as NodeListOf<HTMLElement>
    if (!selectedItemList) return

    const array = Array.from(selectedItemList)
    array.forEach((item) => (item.style.display = `flex`))

    let totalWidth = 0
    const GAP = 6
    let overOneLine = false
    const isOver = (currentWidth: number) => {
      totalWidth = totalWidth + currentWidth + GAP
      overOneLine = totalWidth > selectedWidth
      return hideSelected.current ? totalWidth > selectedWidth : false
    }

    const res = array.filter((item, index) => {
      const w = item?.clientWidth || 0
      const flag = isOver(w)

      if (flag) {
        item.style.display = `none`
        const preWidth = totalWidth - w + 60
        if (preWidth > selectedWidth) {
          array[index - 1] && (array[index - 1].style.display = `none`)
        }
      } else {
        item.style.display = `flex`
      }
      return flag || item.style.display === `none`
    })
    // setHideSelectedCount(arr.filter(v => v.style.display === `none`).length)
    // 超出一行时，设置为true
    setHasOver((!!res.length && array.length > 1) || overOneLine)
  }

  const calcPopoverContentWidth = () => {
    const w = triggerRef.current?.clientWidth
    const width = w < 254 ? 254 : w
    setPopoverContentWidth(width)
  }

  React.useEffect(() => {
    getSelectedWidth()
  }, [value])

  React.useEffect(() => {
    getSelectedWidth()
    const obs = new ResizeObserver(getSelectedWidth)
    if (!selectedRef.current) return
    obs.observe(selectedRef.current)
    return () => {
      obs.disconnect()
    }
  }, [])

  React.useEffect(() => {
    const resizeObserver = new ResizeObserver(
      debounce(calcPopoverContentWidth, 100),
    )
    if (triggerRef.current) {
      resizeObserver.observe(triggerRef.current)
    }
    return () => {
      resizeObserver?.disconnect()
    }
  }, [])

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <div
            ref={triggerRef}
            className="flex items-center justify-between border border-1-[#D1D5DB] rounded-md shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] py-3 px-3 hover:bg-[#fff] border-solid w-full font-normal"
          >
            <div className="flex items-center flex-1 text-base h-auto">
              <span className="text-[#A1A1AA]">
                {selectedValues?.size === 0 && placeholder}
              </span>
              <>
                <div
                  ref={selectedRef}
                  className={cn(' gap-1 flex flex-1 flex-wrap')}
                >
                  {options
                    .filter((option) => selectedValues.has(option.value))
                    .map((option) => (
                      <Badge
                        key={option.value}
                        className="selected_item flex space-x-1 items-center bg-[#EFF6FF] text-[#1E3A8A] text-xs rounded-md px-2 py-[2px] font-normal hover:bg-[#EFF6FF]"
                      >
                        <span>{option.label}</span>
                        <X
                          className="w-3 h-3 text-[#1E3A8A] cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation()
                            onValueChange?.(
                              [...selectedValues].filter(
                                (v) => v !== option.value,
                              ),
                            )
                          }}
                        />
                      </Badge>
                    ))}
                  {hasOver ? (
                    <div
                      className="w-[60px] h-[22px] flex items-center justify-between cursor-pointer rounded-md px-2 py-[2px] bg-[#1D4ED8] text-[#fff] text-xs font-normal"
                      onClick={(e) => {
                        e.stopPropagation()
                        onValueChange?.([...selectedValues])
                        hideSelected.current = !hideSelected.current
                        setHiddenSelected(hideSelected.current)
                      }}
                    >
                      {/* {hideSelectedCount} <span className="hidden lg:flex">+</span> */}
                      <span>{hiddenSelected ? '全部' : '收起'}</span>
                      <CaretSortIcon className="h-4 w-4" />
                    </div>
                  ) : undefined}
                </div>
              </>
            </div>
            <Down />
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="w-[200px] p-2"
          align="start"
          style={{ width: popoverContentWidth + 'px' }}
          onWheel={(e) => e.stopPropagation()}
        >
          <Command
            filter={(value, search) => {
              if (value.includes(search)) return 1
              return 0
            }}
          >
            <div className=" leading-8 text-[16px] font-semibold text-[#18181B]">
              {comboxTitle}
            </div>
            <div className="my-2">
              <CommandInput placeholder={title} />
            </div>
            <CommandList className="relative">
              <CommandEmpty>无数据</CommandEmpty>
              {options.length > 0 && (
                <>
                  <div
                    onClick={() => {
                      if (options.every((v) => selectedValues.has(v.value))) {
                        selectedValues.clear()
                      } else {
                        options?.forEach((item) =>
                          selectedValues.add(item.value),
                        )
                      }
                      const filterValues = Array.from(selectedValues)
                      onValueChange(filterValues)
                    }}
                    className={cn(' flex flex-col p-0')}
                  >
                    <div className="flex justify-between cursor-pointer w-full px-2 py-2.5">
                      <div className="flex items-center accountSwitcherItem">
                        全选
                      </div>
                      <div className=" flex items-center justify-center">
                        {options.every((v) => selectedValues.has(v.value)) ? (
                          <div className="h-4 w-4 rounded-full border-[4px] border-[#1E3A8A]"></div>
                        ) : (
                          <Radio className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                  </div>
                  <div className={cn('border-t mt-1.5 pt-1.5 w-full')}></div>
                </>
              )}
              <CommandGroup className=" flex-1 p-0 flex flex-col">
                <div className="flex-1 flex flex-col overflow-y-auto">
                  {options?.map((option) => {
                    const isSelected = selectedValues.has(option.value)
                    return (
                      <React.Fragment key={option.value}>
                        <CommandItem
                          onSelect={() => {
                            if (isSelected) {
                              selectedValues.delete(option.value)
                            } else {
                              selectedValues.add(option.value)
                            }
                            options.every((v) => selectedValues.has(v.value))
                              ? selectedValues.add('ALL')
                              : selectedValues.delete('ALL')
                            const filterValues = Array.from(selectedValues)
                            onValueChange(filterValues)
                          }}
                          className=" flex flex-col p-0"
                        >
                          <div className="flex justify-between cursor-pointer w-full px-2 py-2.5">
                            <div className="flex items-center accountSwitcherItem">
                              {option.icon && <>{option.icon}</>}
                              <span>{option.label}</span>
                            </div>
                            <div className=" flex items-center justify-center">
                              {isSelected ? (
                                <div className="h-4 w-4 rounded-full border-[4px] border-[#1E3A8A]"></div>
                              ) : (
                                <Radio className="h-4 w-4" />
                              )}
                            </div>
                          </div>
                        </CommandItem>
                        <div
                          className={cn(
                            option.value === 'ALL' &&
                              'border-t mt-1.5 pt-1.5 w-full',
                          )}
                        ></div>
                      </React.Fragment>
                    )
                  })}
                </div>
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </>
  )
}
