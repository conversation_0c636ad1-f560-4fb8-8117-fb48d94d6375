import { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'

interface DurationSettingProps {
  value: number[]
  onChange: (value: number[]) => void
}

const DurationSetting = ({ value, onChange }: DurationSettingProps) => {
  const [hours, setHours] = useState<string>('')
  const [minute, setMinute] = useState<string>('')

  const onChangeHours = (e: React.ChangeEvent<HTMLInputElement>) => {
    // 只能输入数字
    const reg = /^[0-9]*$/
    if (!reg.test(e.target.value)) return
    const value = e.target.value
    setHours(value)
    // 确保空字符串转换为0而不是NaN
    const hoursValue = value === '' ? 0 : parseInt(value)
    const minuteValue = minute === '' ? 0 : parseInt(minute)
    onChange([hoursValue, minuteValue])
  }

  const onChangeMinute = (e: React.ChangeEvent<HTMLInputElement>) => {
    const reg = /^[0-9]*$/
    if (!reg.test(e.target.value)) return
    const value = e.target.value
    setMinute(value)
    // 确保空字符串转换为0而不是NaN
    const hoursValue = hours === '' ? 0 : parseInt(hours)
    const minuteValue = value === '' ? 0 : parseInt(value)
    onChange([hoursValue, minuteValue])
  }

  useEffect(() => {
    if (!value) return
    setHours(isNaN(value[0]) ? '' : value[0].toString())
    setMinute(isNaN(value[1]) ? '' : value[1].toString())
  }, [value])

  return (
    <div className="flex-1 flex gap-4">
      <div className="flex-1 w-full flex items-center space-x-2">
        <Input
          value={hours}
          placeholder="小时"
          onChange={onChangeHours}
          className="text-[16px]"
        />
        <span>h</span>
      </div>
      <div className="flex-1 w-full flex items-center space-x-2">
        <Input
          value={minute}
          placeholder="分钟"
          onChange={onChangeMinute}
          className="text-[16px]"
        />
        <span>m</span>
      </div>
    </div>
  )
}

export default DurationSetting
