import { useState, use<PERSON>emo, useCallback } from "react";
import { FormDialog } from "@/components/FormDialog";
import { Form, FormInstance, Tooltip } from "antd";
import { DialogConfig, DIALOGTYPE } from "../../data/type";
import InfoFill from "@/assets/svg/info-fill.svg?react";
import { Input } from "@/components/ui/input";
import { PathChoose } from "./pathChoose";
// import { DefaultLink, DefaultLinkJump3, PathChoose } from "./pathChoose";
import { StateTag } from "@/components/StateTag";
import Dot from "@/assets/svg/Dot.svg?react";
import WarnPath from "@/assets/svg/warnPath.svg?react";
import { Switch } from "@/components/ui/switch";
import { PathList } from "../../types/proxy-bridge-type";
import "./index.scss";
import { DialogProps } from "../..";
// import { startCountry } from "@/pages/data";
import DurationSetting from "./duration-setting";
import { CountryFilter } from "./country-filter";
import { IProxyItem } from "@/pages/proxies/types/proxy-bridge-type";
import { PROXY_TYPE } from "@/data";
import { countryCodeMap } from "@/pages/dashboard/data";
import { getUrl } from "@/lib/utils";
import { disableNode } from "@/pages/link/data/link-state";
import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";
import { useProxiesFastest } from "@/api/link";
import { IComboboxValue } from "@/components/Combobox";

const ExclusiveSwitch = ({
	value,
	onChange,
	title,
	tooltips,
	disabled,
}: {
	value?: boolean;
	onChange?: (data: boolean) => void;
	title: string;
	tooltips: string;
	disabled?: boolean;
}) => {
	const [checked, setChecked] = useState(value);
	return (
		<div className="flex items-center">
			<Switch
				className="data-[state=checked]:bg-[#1E3A8A] "
				checked={checked}
				disabled={disabled}
				onCheckedChange={(e) => {
					setChecked(e);
					onChange?.(e);
				}}
			/>
			<span className="ml-1.5 text-[#18181B] font-medium  text-[13px]">
				{title}
			</span>
			<Tooltip placement="bottom" title={tooltips}>
				<div className="ml-1.5 linkAdd_toolTip">
					<InfoFill className="w-3.5  " />
				</div>
			</Tooltip>
		</div>
	);
};

const TipTitle = ({ title, tooltips }: { title: string; tooltips: string }) => {
	return (
		<div className=" justify-start items-center inline-flex">
			<div className="font-medium">{title}</div>
			<Tooltip placement="bottom" title={tooltips}>
				<div className="ml-1.5 linkAdd_toolTip">
					<InfoFill className="w-3.5  " />
				</div>
			</Tooltip>
		</div>
	);
};

export const FormAlertDialog = ({
	open,
	setOpen,
	successHandle,
	dialogLoading,
	form,
	type,
	canSubmit,
	row,
	initialValues,
	proxiesList,
}: {
	open: boolean;
	setOpen: (open: boolean) => void;
	successHandle: () => void;
	dialogLoading: boolean;
	canSubmit: boolean;
	form: FormInstance;
	type: DialogConfig;
	row: PathList | undefined;
	initialValues: DialogProps;
	proxiesList: IProxyItem[] | undefined;
}) => {
	const { globalInfo } = useGlobalInfoContext();
	const { data: proxiesFastestList } = useProxiesFastest();
	const url = getUrl();
	const watchedValue = Form.useWatch("use", form);
	const watchedJump = Form.useWatch("jump", form);
	const watchedTime = Form.useWatch("time", form);
	const nodeGuard = Form.useWatch("nodeGuard", form);
	const nodeDefault = Form.useWatch("nodeDefault", form);
	const watchedCountry = Form.useWatch("change_country_array", form);

	const handleExitNodeChange = useCallback(async (value: IComboboxValue) => {
		if(value){
			if(!nodeGuard &&  proxiesFastestList?.data[1]){
				form.setFieldsValue({ nodeGuard:  proxiesFastestList?.data[1]});
			}
			if(!nodeDefault && proxiesFastestList?.data[0]){
				form.setFieldsValue({ nodeDefault:  proxiesFastestList?.data[0]});
			}
		}
	},[proxiesFastestList, nodeGuard, nodeDefault]);

	const showDialog = (open: boolean) => {
		setOpen(open);
	};

	const showStatus = useMemo(() => {
		const isUse = row?.current_ip_use || row?.use;
		return isUse && type === DIALOGTYPE.EDIT;
	}, [row, type]);

	const validateTime = (_rule: NonNullable<unknown>, value: string[]) => {
		const hoursValue = parseInt(value[0], 10);
		const minutesValue = parseInt(value[1], 10);

		if (!hoursValue && !minutesValue) {
			return Promise.reject("请输入跳变时间");
		}
		// 验证分钟范围
		if (minutesValue < 0 || minutesValue >= 60) {
			return Promise.reject("分钟必须在 0 到 59 之间");
		}

		// 验证小时范围
		if (hoursValue < 0 || hoursValue > 24) {
			return Promise.reject("小时必须在 0 到 24 之间");
		}

		// 逻辑验证
		if (
			(minutesValue < 10 && !hoursValue) ||
			(hoursValue === 24 && minutesValue > 0)
		) {
			return Promise.reject("时间范围选择10分钟-24小时");
		}

		return Promise.resolve();
	};

	const countrysGuard = useMemo(() => {
		const codes = new Set(
			proxiesList
				?.filter(
					(v) =>
						v.type === PROXY_TYPE.GUARD &&
						!disableNode.includes(v.use_status) &&
						v.country_code &&
						v.country_code.trim() !== ""
				)
				.map((v) => v.country_code) ?? []
		);
		return (
			Array.from(codes)
				.map((code) => {
					const upperCode = code?.toUpperCase();
					const label = countryCodeMap[upperCode];
					return {
						label,
						value: code,
						icon: (
							<img
								src={`${url}/res/flag3/${code.toLowerCase()}.svg`}
								alt={code}
								className="w-4 h-4 mr-2"
								onError={(e) => {
									// 图标加载失败时显示默认占位符
									const target = e.target as HTMLImageElement;
									target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04IDRDNi45IDQgNiA0LjkgNiA2VjEwQzYgMTEuMSA2LjkgMTIgOCAxMkM5LjEgMTIgMTAgMTEuMSAxMCAxMFY2QzEwIDQuOSA5LjEgNCA4IDRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo=';
									target.alt = '默认图标';
								}}
							/>
						),
					};
				})
				.filter((item) => item.label && typeof item.label === 'string' && item.label.trim() !== "" && item.value && item.value.trim() !== "") ?? []
		);
	}, [proxiesList]);

	const countrysPassGfw = useMemo(() => {
		const codes = new Set(
			proxiesList
				?.filter(
					(v) =>
						v.type === "ingress" &&
						!disableNode.includes(v.use_status) &&
						v.country_code &&
						v.country_code.trim() !== ""
				)
				.map((v) => v.country_code) ?? []
		);
		return (
			Array.from(codes)
				.map((code) => {
					const upperCode = code?.toUpperCase();
					const label = countryCodeMap[upperCode];
					return {
						label,
						value: code,
						icon: (
							<img
								src={`${url}/res/flag3/${code.toLowerCase()}.svg`}
								alt={code}
								className="w-4 h-4 mr-2"
								onError={(e) => {
									// 图标加载失败时显示默认占位符
									const target = e.target as HTMLImageElement;
									target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04IDRDNi45IDQgNiA0LjkgNiA2VjEwQzYgMTEuMSA2LjkgMTIgOCAxMkM5LjEgMTIgMTAgMTEuMSAxMCAxMFY2QzEwIDQuOSA5LjEgNCA4IDRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo=';
									target.alt = '默认图标';
								}}
							/>
						),
					};
				})
				.filter((item) => item.label && typeof item.label === 'string' && item.label.trim() !== "" && item.value && item.value.trim() !== "") ?? []
		);
	}, [proxiesList]);

	const countrysExit = useMemo(() => {
		const codes = new Set(
			proxiesList
				?.filter(
					(v) =>
						v.type === PROXY_TYPE.EXIT &&
						!disableNode.includes(v.use_status) &&
						v.country_code &&
						v.country_code.trim() !== ""
				)
				.map((v) => v.country_code) ?? []
		);
		return (
			Array.from(codes)
				.map((code) => {
					const upperCode = code?.toUpperCase();
					const label = countryCodeMap[upperCode];
					return {
						label,
						value: code,
						icon: (
							<img
								src={`${url}/res/flag3/${code.toLowerCase()}.svg`}
								alt={code}
								className="w-4 h-4 mr-2"
								onError={(e) => {
									// 图标加载失败时显示默认占位符
									const target = e.target as HTMLImageElement;
									target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04IDRDNi45IDQgNiA0LjkgNiA2VjEwQzYgMTEuMSA2LjkgMTIgOCAxMkM5LjEgMTIgMTAgMTEuMSAxMCAxMFY2QzEwIDQuOSA5LjEgNCA4IDRaIiBmaWxsPSIjOUI5QkEwIi8+Cjwvc3ZnPgo=';
									target.alt = '默认图标';
								}}
							/>
						),
					};
				})
				.filter((item) => item.label && typeof item.label === 'string' && item.label.trim() !== "" && item.value && item.value.trim() !== "") ?? []
		);
	}, [proxiesList]);

	const jumpChangeDisable = useMemo(() => {
		return !globalInfo?.data.flags.includes("jump_change_disable");
	}, [globalInfo]);

	const exclusive_disable = useMemo(() => {
		return !globalInfo?.data.flags.includes("exclusive_disable");
	}, [globalInfo?.data]);

	// const flags = useMemo(() => {
	// 	return globalInfo?.data.flags || [];
	// }, [globalInfo]);

	return (
		<FormDialog
			open={open}
			openChange={showDialog}
			title={type.title}
			describe={type.desc}
			successText={type.successText}
			successHandle={successHandle}
			submitLoading={dialogLoading}
			form={form}
			contentClass="w-[800px] flex flex-col max-h-[calc(100vh-100px)] overflow-y-hidden"
			successStyle={
				canSubmit
					? "bg-[#1E3A8A] hover:bg-[#1D4ED8] active:bg-[#1E40AF]"
					: "bg-[#1E3A8A] hover:bg-[#1E3A8A] opacity-50"
			}
		>
			{showStatus && (
				<div className="flex items-center mb-3">
					<span className="font-medium">状态：</span>
					<StateTag
						active={{
							className: row?.current_ip_use
								? "border-[#3B82F6] text-[#1D4ED8] border bg-[#EFF6FF]"
								: "border-[#14B8A6] text-[#0F766E] border bg-[#CCFBF1]",
							desc: row?.current_ip_use ? "当前使用中" : "使用中",
							icon: (
								<Dot
									className="mr-1 "
									fill={row?.current_ip_use ? "#2563EB" : "#0D9488"}
								/>
							),
						}}
					/>
				</div>
			)}
			<Form
				className="projectform"
				form={form}
				name="dynamic_form_nest_item"
				autoComplete="off"
				layout="vertical"
				initialValues={initialValues}
			>
				<Form.Item
					name="name"
					label="链路名"
					rules={[{ required: true, message: "请输入正确的链路名" }]}
				>
					<Input
						className="link_name_input placeholder:text-base text-[16px]"
						placeholder="输入链路名"
						height={40}
					/>
				</Form.Item>
				<Form.Item
					name="nodeExit"
					rules={[{ required: true, message: "请选择节点" }]}
				>
					<PathChoose
						pathText={{ guard: "出口节点", exit: "出口节点" }}
						type="exit"
						side="top"
						showDelayLabel
						onChange={(e)=>handleExitNodeChange(e)}
					/>
				</Form.Item>
				<Form.Item
					name="nodeGuard"
					rules={[{ required: true, message: "请选择节点" }]}
				>
					<PathChoose
						pathText={{ guard: "中继节点", exit: "中继节点" }}
						type="guard"
						showDelayLabel
					/>
				</Form.Item>

				<Form.Item
					name="nodeDefault"
					rules={[{ required: true, message: "请选择节点" }]}
				>
					<PathChoose
						pathText={{ guard: "入口节点", exit: "入口节点" }}
						type="ingress"
						showDelayLabel
					/>
				</Form.Item>
				{jumpChangeDisable && (
					<Form.Item name="jump" className="pt-3">
						<ExclusiveSwitch
							title="动态跳变"
							tooltips="开启动态跳变后，不支持修改链路节点和私有出口"
						/>
					</Form.Item>
				)}

				{jumpChangeDisable && watchedJump && (
					<div className="max-w-full">
						<div>
							<TipTitle
								title="跳变时间"
								tooltips="链路将按照所选的跳变时长进行刷新，跳变时间最少为10分钟，最多为24小时"
							/>
							<div className="flex items-center mt-[6px] gap-[11.5px]">
								<Form.Item
									name="time"
									rules={[{ validator: validateTime }]}
									className="flex-1"
								>
									<DurationSetting
										value={watchedTime}
										onChange={(e) => {
											form.setFieldValue("time", e);
										}}
									/>
								</Form.Item>
							</div>
						</div>
						<div className="max-w-full ">
							<TipTitle
								title="跳变国家"
								tooltips="链路国家将按照您的偏好进行分配"
							/>
							<div className="flex flex-col flex-1 mt-2.5">
								<Form.Item
									name={["change_country_array", 0]}
									className="flex-1 max-w-full"
									rules={[{ required: true, message: "请选择入口节点偏好" }]}
								>
									<CountryFilter
										value={watchedCountry?.[0]}
										comboxTitle="跳变国家"
										title="输入国家、地区名称"
										placeholder="入口节点偏好"
										options={countrysPassGfw}
										onValueChange={(e) => {
											form.setFieldValue("change_country_array", [
												e,
												watchedCountry[1] || [],
												watchedCountry[2] || [],
											]);
											form.validateFields([["change_country_array", 0]]);
										}}
									/>
								</Form.Item>
								<Form.Item
									name={["change_country_array", 1]}
									className="flex-1 max-w-full"
									rules={[{ required: true, message: "请选择中继节点偏好" }]}
								>
									<CountryFilter
										value={watchedCountry?.[1]}
										comboxTitle="跳变国家"
										title="输入国家、地区名称"
										placeholder="中继节点偏好"
										options={countrysGuard}
										onValueChange={(e) => {
											form.setFieldValue("change_country_array", [
												watchedCountry[0] || [],
												e,
												watchedCountry[2] || [],
											]);
											form.validateFields([["change_country_array", 1]]);
										}}
									/>
								</Form.Item>
								<Form.Item
									name={["change_country_array", 2]}
									className="flex-1 max-w-full"
									rules={[{ required: true, message: "请选择出口节点偏好" }]}
								>
									<CountryFilter
										value={watchedCountry?.[2]}
										comboxTitle="跳变国家"
										title="输入国家、地区名称"
										placeholder="出口节点偏好"
										options={countrysExit}
										onValueChange={(e) => {
											form.setFieldValue("change_country_array", [
												watchedCountry[0] || [],
												watchedCountry[1] || [],
												e,
											]);
											form.validateFields([["change_country_array", 2]]);
										}}
									/>
								</Form.Item>
							</div>
						</div>
					</div>
				)}
				{!row?.use && (
					<>
						<Form.Item name="use" className="pt-3">
							<ExclusiveSwitch
								disabled={row?.use}
								title="立即应用此到本设备"
								tooltips="应用后即刻生效，此链路将替代使用中链路进行全流量代理"
							/>
						</Form.Item>
						{watchedValue && (
							<div className="w-full p-3 border border-[#FED7AA] bg-[#FFF7ED] flex items-center rounded-md my-6">
								<WarnPath className="mr-4" />
								<span className="text-[#18181B] font-medium">
									应用后即刻生效，此链路将替代现使用中链路！
								</span>
							</div>
						)}
					</>
				)}
				{(row?.use || watchedValue) &&
					(!watchedJump || !jumpChangeDisable) &&
					exclusive_disable && (
						<Form.Item name="exclusive" className={`${row?.use && "pt-3"}`}>
							<ExclusiveSwitch
								title="私有出口节点"
								tooltips="开启私有后，此链路使用期间，该出口节点不可被其他设备使用"
							/>
						</Form.Item>
					)}
			</Form>
		</FormDialog>
	);
};
