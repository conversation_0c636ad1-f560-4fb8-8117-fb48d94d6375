import { useMemo, useState, useEffect } from "react";
import LinkButtonCol from "@/assets/svg/LinkButtonCol.svg?react";
// import Gurad from "@/assets/svg/Gurad.svg?react";
import Gurad from "@/assets/svg/proxies/ProxiesGuard.svg?react";
import Exit from "@/assets/svg/Exit.svg?react";
import Down from "@/assets/svg/down.svg?react";
import HiddenNode from "@/assets/svg/link/HiddenNode.svg?react";
import InletNodeSvg from "@/assets/svg/link/InletNode.svg?react";
import EntryNode from "@/assets/svg/link/EntryNode.svg?react";

import { cn, getUrl } from "@/lib/utils";
import { useProxiesExit, useProxiesGuard, useProxiesIngress } from "@/api/link";
import { Button } from "@/components/ui/button";
import { Combobox, IComboboxValue } from "@/components/Combobox";
import { ProxiesList } from "../../types/proxy-bridge-type";
import { SearchOptionText } from "../multipleLinks";
import { countryCodeMap } from "@/pages/dashboard/data";
import { disableNode } from "@/pages/link/data/link-state";
import { DELAY_TYPE_STYLE } from "../../types/config";

// import { useGlobalInfoContext } from '@/provider/GlobalInfoContextProvider'

export function getMedianIndex(arr: any[]) {
	const n = arr.length;
	if (n === 0) return -1; // 空数组，返回 -1 或你也可以选择返回 null/undefined
	if (n === 1) return 0; // 只有一个元素，下标就是 0
	return Math.ceil(n / 2) - 1;
}

export const DefaultLinkJump3 = ({
	flag = "",
	className,
	des,
	icon = "hidden",
}: {
	des: string;
	flag?: string;
	className?: string;
	icon?: string;
}) => {
	const url = getUrl();
	return (
		<div className={cn("flex items-center renderButtonStyle", className)}>
			<Button
				className={
					" bg-[#E5E7EB]  text-[#18181B] justify-start border border-[#D4D4D8] w-full px-3 h-10"
				}
				disabled
			>
				{flag ? (
					<div className="w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] mr-2 shrink-0">
						<img
							src={`${url}/res/flag3/${flag.toLowerCase()}.svg`}
							alt=""
							className="w-4 h-4"
						/>
					</div>
				) : icon === "hidden" ? (
					<HiddenNode className="mr-2" />
				) : (
					<EntryNode className="w-5 h-5 mr-2 fill-[#A1A1AA]" />
				)}
				<div className="text-[16px] font-normal">{des}</div>
			</Button>
		</div>
	);
};

export const DefaultLink = ({
	flag = "",
	className,
	des,
	type = "hidden",
	disabled,
	isChecked = true,
	onChange,
}: {
	des: string;
	flag?: string;
	className?: string;
	type?: string;
	disabled?: boolean;
	isChecked?: boolean;
	onChange?: (value: IComboboxValue) => void;
}) => {
	const url = getUrl();

	// const ingressName = countryCodeMap[flag.toUpperCase()]

	const [ingressName, setIngressName] = useState(des ?? "");
	const [ingressOptions, setIngressOptions] = useState<
		{
			label: string;
			value: string;
			icon: JSX.Element;
			num: number;
		}[]
	>([]);

	const getOptions = () => {
		const delay = parseInt((Math.random() * 150 + 50).toFixed(0));
		const name = countryCodeMap[flag.toUpperCase()];
		setIngressOptions([
			{
				label: name ?? des,
				value: name ?? des,
				icon: (
					<>
						{name ? (
							<div
								className={cn(
									"w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] shrink-0"
								)}
							>
								<img
									src={`${url}/res/flag3/${flag.toLowerCase()}.svg`}
									alt=""
									className="w-4 h-4 "
								/>
							</div>
						) : type === "hidden" ? (
							<HiddenNode className="" />
						) : undefined}
					</>
				),
				num: delay,
			},
		]);
	};

	useEffect(() => {
		// type === 'hidden' &&
		isChecked && setIngressName(countryCodeMap[flag.toUpperCase()] ?? des);
		getOptions();
		console.log(ingressName, "ingressName");
	}, [flag]);

	return (
		<div className={cn("flex items-center renderButtonStyle", className)}>
			{/* <Button className={" bg-[#E5E7EB]  text-[#18181B] justify-start border border-[#D4D4D8] w-full px-3 h-10"} disabled>
                {flag ?
                    <div className="w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] mr-2 shrink-0">
                        <img src={`${url}/res/flag3/${flag.toLowerCase()}.svg`} alt="" className="w-4 h-4" />
                    </div>
                    :
                    icon === 'hidden' ? <HiddenNode className="mr-2" />
                        : <EntryNode className="w-5 h-5 mr-2 fill-[#A1A1AA]" />
                }
                <span>{des}</span>
            </Button> */}
			<Combobox
				value={ingressName}
				typeBorder
				onChange={(e) => {
					onChange?.(e);
				}}
				radioText={SearchOptionText}
				downIcon={<Down />}
				options={ingressOptions}
				placeholder={type === "hidden" ? "隐匿节点" : "入口节点"}
				muiltip={false}
				contentClass="p-2 pt-0 linkAdd_ComboxContent my-[4px]"
				contentProps={{
					alignOffset: 0,
				}}
				disabled={disabled}
				comboxTitle={<ComboxTitle type={type} />}
			/>
		</div>
	);
};

export const ComboxTitle = ({ type }: { type: string }) => {
	const getNode = useMemo(() => {
		switch (type) {
			case "guard":
				return (
					<>
						<Gurad className="mr-2 w-6 h-6" fill="#059669" />
						<span className="text-[#18181B] text-base font-semibold">
							中继节点
						</span>
					</>
				);
			case "hidden":
				return (
					<>
						<HiddenNode className="mr-2 w-6 h-6" fill="#059669" />
						<span className="text-[#18181B] text-base font-semibold">
							隐匿节点
						</span>
					</>
				);
			case "entry":
				return (
					<>
						<InletNodeSvg className="mr-2 w-6 h-6" fill="#059669" />
						<span className="text-[#18181B] text-base font-semibold">
							入口节点
						</span>
					</>
				);
			case "pass_gfw":
				return (
					<>
						<InletNodeSvg className="mr-2 w-6 h-6" fill="#059669" />
						<span className="text-[#18181B] text-base font-semibold">
							入口节点
						</span>
					</>
				);
			default:
				return (
					<>
						<Exit className="mr-2" />
						<span className="text-[#18181B] text-base font-semibold">
							出口节点
						</span>
					</>
				);
		}
	}, [type]);

	return <div className="flex items-center mb-1 mt-2 p-1">{getNode}</div>;
};
export const PathChoose = ({
	value,
	onChange,
	pathText,
	type,
	showDelayLabel,
	side,
}: {
	value?: string;
	type: string;
	showDelayLabel?: boolean;
	side?: string;
	onChange?: (value: IComboboxValue) => void;
	pathText: {
		guard: string;
		exit: string;
	};
}) => {
	// const { globalInfo } = useGlobalInfoContext()
	const [_, setFlagText] = useState<ProxiesList>();
	const { data: proxiesList, refetch: refetchGuard } = useProxiesGuard();
	const { data: proxiesExit, refetch: refetchExit } = useProxiesExit();
	const { data: proxiesPassGfw, refetch: refetchPassGfw } = useProxiesIngress();
	const [maxHeightClass, setMaxHeightClass] = useState("max-h-[300px]");

	const currentList = useMemo(() => {
		let newType = type || "guard";
		switch (newType) {
			case "guard":
				return proxiesList?.data;
			case "exit":
				return proxiesExit?.data;
			case "pass_gfw":
			case "ingress":
				return proxiesPassGfw?.data;
			default:
				return proxiesList?.data;
		}
	}, [proxiesList, proxiesExit, proxiesPassGfw, type]);
	const currentListFn = useMemo(() => {
		let newType = type || "guard";
		switch (newType) {
			case "guard":
				return refetchGuard;
			case "exit":
				return refetchExit;
			case "pass_gfw":
			case "ingress":
				return refetchPassGfw;
			default:
				return refetchGuard;
		}
	}, [proxiesList, proxiesExit, proxiesPassGfw, type]);

	// const flags = useMemo(() => {
	//   return globalInfo?.data.flags || []
	// }, [globalInfo])

	const getOptions = (value_: ProxiesList[]) => {
		const url = getUrl();
		value_?.forEach((item, index) => {
			item.i = index;
		});
		const optionsFilter = value_.filter(
			(item) => !disableNode.includes(item.use_status) || item.name === value
		);
		// 超时了的数组
		const delayListTimeout = [];
		// 未超时的数组
		const okList = [];
		for (let i = 0; i < optionsFilter.length; i++) {
			const item = optionsFilter[i];
			if (item.delay > 0) {
				okList.push(item);
			} else {
				item.delayStyle = DELAY_TYPE_STYLE.HIGH;
				delayListTimeout.push(item);
			}
		}
		// okList 通过delay 排序从小到大
		okList.sort((a, b) => a.delay - b.delay);
		const midianIndex = getMedianIndex(okList);
		okList.forEach((item, index) => {
			if (index <= midianIndex) {
				item.delayStyle = DELAY_TYPE_STYLE.LOW;
			} else {
				item.delayStyle = DELAY_TYPE_STYLE.MID;
			}
		});

		const options = [...okList, ...delayListTimeout]
			.sort((a, b) => (a.i ?? 0) - (b.i ?? 0))
			.map((item) => {
				return {
					label: item.name,
					value: item.name,
					icon: (
						<div className="w-[16px] h-[16px] flex items-center justify-center rounded-full bg-[#FFF] mr-2 shrink-0">
							<img
								src={`${url}/res/flag/${item.country_code.toUpperCase()}.png`}
								alt=""
								className="w-4 h-4"
							/>
						</div>
					),
					delayStyle: item.delayStyle,
					num: item.delay,
				};
			});
		return options;
	};

	const onHandleChange = (e: IComboboxValue) => {
		const value_ = currentList?.find((index) => index.name === e);
		setFlagText(value_);
		onChange?.(e);
	};

	const currentOption = useMemo(() => {
		if (!currentList) return [];
		const value_ = currentList.find((index) => index.name === value);
		setFlagText(value_);
		return getOptions(currentList);
	}, [currentList]);

	useEffect(() => {
		const updateMaxHeight = () => {
			if (window.innerHeight < 1080) {
				setMaxHeightClass("max-h-[260px]");
			} else {
				setMaxHeightClass("max-h-[300px]");
			}
		};

		updateMaxHeight(); // 初始调用一次
		window.addEventListener("resize", updateMaxHeight); // 监听窗口大小变化
		return () => window.removeEventListener("resize", updateMaxHeight); // 清除监听器
	}, []);

	return (
		<>
			{type !== "exit" && <LinkButtonCol className="linkbutton" />}
			<div className="flex justify-between items-center mt-3">
				<div className="flex-1">
					<div className="mb-2 font-medium">{pathText.exit}</div>
					<Combobox
						value={value}
						side={side}
						onChange={onHandleChange}
						typeBorder
						radioText={SearchOptionText}
						downIcon={<Down />}
						options={currentOption}
						onComboxOpen={currentListFn}
						placeholder="请选择节点"
						muiltip={false}
						contentClass="p-2 pt-0 linkAdd_ComboxContent my-[4px]"
						commandListClassName={maxHeightClass}
						ellipsisTooltipClassName="text-[16px]"
						comboxTitle={<ComboxTitle type={type} />}
						showDelayLabel={showDelayLabel}
						// contentProps={{
						//   alignOffset: -380,
						// }}
					/>
				</div>
			</div>
		</>
	);
};
