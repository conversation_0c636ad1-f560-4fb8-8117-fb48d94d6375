import { useState, useRef, useMemo, useEffect } from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Input } from '@/components/ui/input'
import { PathList } from '../../types/proxy-bridge-type'
import { usePathListMutation } from '@/api/link'
import { errorToast, successToast } from '@/components/GlobalToast'
import { toast } from '@/components/ui/use-toast'
// import { currentUser } from '@/auth' // Removed - single user mode

interface IProps {
  // index: number,
  data: PathList
}

function isEnterKeyDown(e: React.KeyboardEvent<HTMLInputElement>) {
  return e.key === 'Enter' || e.keyCode === 13
}

function getTime(house: number, minute: number) {
  return Number(house) * 60 * 60 + Number(minute) * 60 // 转为秒数
}

function timeValidateStatus(time: number) {
  return time < 10 * 60 || time > 24 * 60 * 60
}

export const DurationDropDown = ({ data }: IProps) => {
  const pathListMutation = usePathListMutation()

  const [hours, setHours] = useState<string>('')
  const [minute, setMinute] = useState<string>('')
  const [openDrop, setOpenDrop] = useState(false)
  const hoursRef = useRef<HTMLInputElement>(null)
  const minuteRef = useRef<HTMLInputElement>(null)
  const preOpenStatus = useRef<boolean>(false)

  // 判断时间Minute是否设置正确 ,如果Hours为空并且Minute在1-9之间，那么Minute应该自动填充为10m
  const checkAutofillMinute = useMemo(() => {
    const mm = Number(minute)
    const hh = Number(hours)
    if (!mm) return
    const time = getTime(hh, mm) // 转为秒数
    return timeValidateStatus(time)
  }, [minute, hours])
  const onChangeHours = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (/^(?:2[0-4]|[01]?[0-9]|)$/.test(value)) {
      setHours(value)
    }
  }
  const onHoursInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (isEnterKeyDown(e) && minuteRef.current) {
      minuteRef.current.focus()
    }
  }

  const onChangeMinute = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    if (/^(5[0-9]|[0-4][0-9]|[0-9])?$/.test(value)) {
      setMinute(value)
    }
  }

  const onMinuteInputBlur = () => {
    if (checkAutofillMinute && openDrop) {
      setMinute('10')
    }
  }

  const onMinuteInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (isEnterKeyDown(e)) {
      setOpenDrop(false)
    }
  }
  const onOpenChangeHandle = (value: boolean) => {
    setOpenDrop(value)
  }
  const onSubmit = async () => {
    const hh = Number(hours)
    const mm = Number(minute)
    const time = getTime(hh, mm) // 转为秒数
    if (timeValidateStatus(time)) {
      errorToast('请输入10m-24h', toast)
      setHours(String(Math.floor(data.change_time / 60 / 60)))
      setMinute(String(Math.floor((data.change_time / 60) % 60)))
      return
    }

    if (time === data.change_time) return

    const parameters = {
      ...data,
      change_time: time,
    }

    pathListMutation.mutate(parameters, {
      onSuccess: () => {
        successToast('修改成功', toast)
      },
      onError: () => {
        errorToast('修改失败', toast)
      },
    })
  }

  useEffect(() => {
    setHours(String(Math.floor(data.change_time / 60 / 60)))
    setMinute(String(Math.floor((data.change_time / 60) % 60)))
  }, [data.change_time])

  useEffect(() => {
    if (!openDrop && preOpenStatus.current) {
      onSubmit()
      preOpenStatus.current = false
    }

    if (openDrop) preOpenStatus.current = true

    setTimeout(() => {
      if (hoursRef.current) {
        hoursRef.current.focus()
      }
    }, 0)
  }, [openDrop])

  return (
    <>
      {data.change_time === 0 ? (
        <div className="text-zinc-900 text-sm font-normal leading-snug">--</div>
      ) : (
        <DropdownMenu open={openDrop} onOpenChange={onOpenChangeHandle}>
          <DropdownMenuTrigger
            asChild
            disabled={false} // Account functionality removed - single user mode
          >
            <div className="min-w-[80px]">
              <div className="justify-start items-center gap-2 inline-flex">
                <div className="text-zinc-900 text-sm font-normal font-['PingFang SC'] leading-snug flex space-x-1">
                  <span>{Math.floor(data.change_time / 60 / 60)}h</span>
                  <span>{Math.floor((data.change_time / 60) % 60)}m</span>
                </div>
              </div>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            side="bottom"
            sideOffset={15}
            className="w-[300px] h-[109px] px-[23px]  bg-white rounded-lg shadow border border-zinc-200  flex flex-col justify-center"
            data-link-drop={data.name}
          >
            <div className=" justify-start items-center inline-flex">
              <div>
                <span className="text-zinc-900 text-sm font-medium font-['PingFang SC'] leading-normal">
                  跳变时长（10
                </span>
                <span className="text-zinc-900 text-sm font-medium font-['PingFang SC'] leading-normal tracking-wide">
                  m≤
                </span>
                <span className="text-zinc-900 text-sm font-medium font-['PingFang SC'] leading-normal">
                  Time≤24h）
                </span>
              </div>
            </div>
            <div className="flex items-center mt-[6px] gap-[11.5px]">
              <div className="flex items-center gap-2">
                <Input
                  ref={hoursRef}
                  className="w-[100.5px] h-10 shrink-0"
                  value={hours}
                  onChange={(e) => onChangeHours(e)}
                  onKeyDown={(e) => onHoursInputKeyDown(e)}
                />{' '}
                <span className="text-zinc-900 text-base font-medium font-['PingFang SC'] leading-normal">
                  h
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Input
                  ref={minuteRef}
                  className="w-[100.5px] h-10 shrink-0"
                  value={minute}
                  onChange={(e) => onChangeMinute(e)}
                  onBlur={onMinuteInputBlur}
                  onKeyDown={(e) => onMinuteInputKeyDown(e)}
                />{' '}
                <span className="text-zinc-900 text-base font-medium font-['PingFang SC'] leading-normal">
                  m
                </span>
              </div>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </>
  )
}
