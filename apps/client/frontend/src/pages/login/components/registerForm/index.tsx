import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/PasswordInput'
import { useRouterState } from '@tanstack/react-router'
import { Form } from 'antd'
import React, { useState, useEffect } from 'react'
import { useKeyPressEvent } from 'react-use'
import { PasVerification, validatePassword } from '../passVerification'
import { regist } from '@/api/user'
import { passCrypto } from '@/lib/utils'
import { isWretchError } from '@/auth'
import { errorToast } from '@/components/GlobalToast'
import { toast } from '@/components/ui/use-toast'

export const RegisterForm = ({
  setStatus,
}: { setStatus: (status: string) => void }) => {
  const [isDisabled, setIsDisabled] = React.useState(true)
  const [form] = Form.useForm()
  const isLoading = useRouterState({ select: (s) => s.isLoading })
  const [pass, setPass] = useState(0)

  const accountValue = Form.useWatch('account', form)
  const passwordValue = Form.useWatch('password', form)
  const rePasswordValue = Form.useWatch('rePassword', form)
  const handleLogin = async () => {
    // setIsDisabled(true);

    await form.validateFields()
    const addvalue = form.getFieldsValue()
    const res = await regist(addvalue.account, passCrypto(addvalue.password))

    if (isWretchError(res)) {
      const result = res.json
      result.msg.includes('account already exists') &&
        errorToast('当前账号已被注册，请重新填写', toast)
    } else {
      setStatus('login')
    }

    setIsDisabled(false)
  }
  useKeyPressEvent('Enter', handleLogin)

  const checkValid = async () => {
    if (!accountValue) return setIsDisabled(true)
    try {
      await form.validateFields(['account'])
      setIsDisabled(!validatePassword(passwordValue, rePasswordValue, pass))
    } catch (error) {
      setIsDisabled(true)
    }
  }

  useEffect(() => {
    checkValid()
  }, [accountValue, passwordValue, rePasswordValue, pass])
  return (
    <>
      <div className="flex-1">
        <div className="text-lg font-semibold mb-2 font-[#18181B]">注册</div>
        <div className="text-sm font-normal text-[#71717A] mb-9">
          为了保证您的安全，请勿使用手机号、身份证、真实姓名等信息作为账号或密码。
        </div>
        <div className="relative">
          <Form
            className="projectform"
            form={form}
            name="dynamic_form_nest_item"
            autoComplete="off"
            initialValues={{
              password: '',
              account: '',
              rePassword: '',
            }}
            layout="vertical"
          >
            <Form.Item
              name="account"
              label="账号"
              rules={[
                { required: true, message: '请输入账号' },
                { min: 4, max: 16, message: '长度在 4 到 16 个字符' },
                {
                  pattern: /^[\dA-Za-z]+$/,
                  message: '只能包含大小写字母和数字',
                },
              ]}
              className="ring-offset-background"
            >
              <Input
                placeholder="请输入账号"
                className="register_account_input"
              />
            </Form.Item>
            <PasVerification
              className="register_password_input"
              setSatisfiedConditions={setPass}
            />
            <Form.Item
              name="rePassword"
              label="确认密码"
              rules={[
                { required: true, message: '请输入确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'))
                  },
                }),
              ]}
              className="register_password_input"
            >
              <PasswordInput
                className="focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 focus-within:border-[#D1D5DB] register_confirm_input"
                placeholder="请输入密码"
              />
            </Form.Item>
          </Form>
        </div>
      </div>
      <div className="flex mt-8">
        <Button
          className="flex-1 mr-4 border border-[#D4D4D8] text-[#18181B] bg-white"
          onClick={() => {
            setStatus('login')
          }}
        >
          返回登录
        </Button>
        <Button
          className=" flex-1 bg-[#1E3A8A]"
          disabled={isDisabled}
          onClick={handleLogin}
        >
          {isLoading ? '注册中' : '注册'}
        </Button>
      </div>
    </>
  )
}
