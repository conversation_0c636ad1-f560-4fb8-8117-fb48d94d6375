import { PasswordInput } from '@/components/PasswordInput'
import React, { useState } from 'react'
import { Form } from 'antd'
import { cn } from '@/lib/utils'

import './index.scss'

function checkPassword(value: string) {
  const hasLower = /[a-z]/.test(value)
  const hasUpper = /[A-Z]/.test(value)
  const hasNumber = /\d/.test(value)
  const hasSpecial = /[\W_]/.test(value)
  return { hasLower, hasUpper, hasNumber, hasSpecial }
}

export const validatePassword = (
  password: string,
  confirmPassword: string,
  strength: number,
): boolean => {
  const maxLength = 16
  const minLength = 8
  const minStrength = 3
  const isSamePassword = password === confirmPassword
  const isMatchStrength = strength >= minStrength
  const isMatchLength =
    password?.length >= minLength && password?.length <= maxLength

  return isSamePassword && isMatchStrength && isMatchLength
}

interface IPasswordValidateStatusProps {
  status: 'success' | 'default'
  text: string
  className?: string
}

const PasswordValidateStatus: React.FC<IPasswordValidateStatusProps> = (
  props,
) => {
  const { status, text, className } = props

  const themeColor = status === 'success' ? '#059669' : '#9CA3AF'

  return (
    <div
      className={`flex items-center ${className}`}
      style={{ color: themeColor }}
    >
      <span
        className="w-2 h-2 rounded-full mr-2 text-sm"
        style={{ backgroundColor: themeColor }}
      />
      <span>{text}</span>
    </div>
  )
}

export const PasVerification = ({
  value,
  className,
  onChange,
  setSatisfiedConditions,
  des = '密码',
}: {
  className?: string
  des?: string
  value?: string
  onChange?: (value_: string) => void
  setSatisfiedConditions?: (value_: number) => void
}) => {
  const [realValue, setRealValue] = useState(value || '')
  const [dataType, stDataType] = useState({
    hasLower: false,
    hasUpper: false,
    hasNumber: false,
    hasSpecial: false,
  })

  const [showText, setShowText] = useState(false)

  const handleInputChange = (value_: string) => {
    setRealValue(value_)
    onChange?.(value_)

    const { hasLower, hasUpper, hasNumber, hasSpecial } = checkPassword(value_)
    stDataType({
      hasLower,
      hasUpper,
      hasNumber,
      hasSpecial,
    })

    if (value_.length > 0) {
      setShowText(true)
    }

    let count = 0
    if (hasLower) count++
    if (hasUpper) count++
    if (hasNumber) count++
    if (hasSpecial) count++
    setSatisfiedConditions?.(count)
  }

  return (
    <Form.Item
      name="password"
      label={des}
      rules={[
        { required: true, message: '请输入' + des },
        { min: 8, max: 16, message: '长度必须为8-16个字符' },
      ]}
      className="password-form-item"
      extra={
        <div className={`mt-3 ${showText ? '' : 'hidden'}`}>
          <div className="text-[#374151] text-sm">
            密码长度为8-16个字符，至少包含以下3个内容：
          </div>
          <PasswordValidateStatus
            className="mt-3"
            status={dataType.hasUpper ? 'success' : 'default'}
            text="大写字母"
          />
          <PasswordValidateStatus
            className="mt-3"
            status={dataType.hasLower ? 'success' : 'default'}
            text="小写字母"
          />
          <PasswordValidateStatus
            className="mt-3"
            status={dataType.hasNumber ? 'success' : 'default'}
            text="数字"
          />
          <PasswordValidateStatus
            className="mt-3 mb-3"
            status={dataType.hasSpecial ? 'success' : 'default'}
            text="特殊字符"
          />
        </div>
      }
    >
      <PasswordInput
        className={cn(
          'focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 focus-within:border-[#D1D5DB]',
          className,
        )}
        placeholder={'输入' + des}
        value={realValue}
        onChange={handleInputChange}
        // onBlur={() => {
        //   const vaildPass = Object.values(dataType).every(value => !!value)
        //   vaildPass && setShowText(false);
        // }}
      />
    </Form.Item>
  )
}
