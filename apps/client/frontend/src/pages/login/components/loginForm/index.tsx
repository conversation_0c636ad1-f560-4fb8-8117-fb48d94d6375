import { login, getUser, getUserActive } from '@/auth'
import { useGlobalNodeWarning } from '@/pages/proxies/hooks/useGlobalNodeWarning'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { PasswordInput } from '@/components/PasswordInput'
import { passCrypto } from '@/lib/utils'
import { useRouter, useRouterState, useNavigate } from '@tanstack/react-router'
import { Form } from 'antd'
import React, { useState, useEffect } from 'react'
import { useKeyPressEvent } from 'react-use'
import LoginErr from '@/assets/svg/LoginErr.svg?react'

export const LoginForm = ({
  setStatus,
}: { setStatus: (status: string) => void }) => {
  const { initGlobalNodeWarning } = useGlobalNodeWarning()
  const [isSubmitting, setIsSubmitting] = React.useState(true)
  const [concatText, setConcatText] = React.useState(false)
  const [initialValues] = useState({
    password: '',
    account: '',
  })
  const [form] = Form.useForm()

  const accountValue = Form.useWatch('account', form)
  const passwordValue = Form.useWatch('password', form)
  const [loginError, setLoginError] = useState<string>('')

  const navigate = useNavigate()
  const router = useRouter()
  const isLoading = useRouterState({ select: (s) => s.isLoading })

  const handleLogin = async () => {
    // setIsSubmitting(true);
    const addvalue = form.getFieldsValue()
    const account = addvalue.account
    const pass = addvalue.password
    if (!account || !pass) return

    const res = await login(account, passCrypto(pass))

    if (res?.code == 200) {
      await getUser()
      await router.invalidate()
      if (getUserActive() === false) {
        setStatus('repass')
      } else {
        navigate({ to: '/dashboard' })
      }
      initGlobalNodeWarning()
    } else {
      if (res?.status >= 500) return showTipError('网络异常，请检查网络')
      const { data, msg } = res?.json ?? {}
      if (msg?.includes('out of login limit'))
        return showTipError('账号已被锁定，请10分钟后重试；')
      if (msg?.includes('invalid account or password'))
        return showTipError(
          '账号或密码错误，还可尝试' + data + '次，连续错误5次将锁定10分钟',
        )
    }
  }
  useKeyPressEvent('Enter', handleLogin)

  let timer: NodeJS.Timeout | null
  const showTipError = (tip: string) => {
    timer && clearTimeout(timer)
    setLoginError(tip)
    timer = setTimeout(() => {
      setLoginError('')
    }, 5000)
  }

  useEffect(() => {
    setIsSubmitting(!(accountValue && passwordValue))
    return () => {
      timer && clearTimeout(timer)
    }
  }, [accountValue, passwordValue])

  return (
    <>
      <div className="flex-1 relative">
        <div className="text-lg font-semibold mb-2 font-[#18181B]">
          欢迎登录平台！
        </div>
        <div className="text-sm font-normal text-[#71717A] mb-9">
          请输入你账号和密码，登录平台
        </div>

        {!!loginError && (
          <div className="flex items-center w-fit h-[42px] rounded-md border border-[#E4E4E7] shadow-[0px 1px 2px 0px #1018280D] py-[9px] px-4 absolute top-[65px] left-0 right-0 m-auto">
            <LoginErr className="w-5 h-5 mr-2" />
            {/* 账号或密码错误，请重新输入 */}
            <span className="text-[#18181B] font-normal">{loginError}</span>
          </div>
        )}

        <div className="relative">
          <Form
            className="projectform"
            form={form}
            name="dynamic_form_nest_item"
            initialValues={initialValues}
            autoComplete="off"
            layout="vertical"
          >
            <Form.Item
              name="account"
              label="账号"
              rules={[{ required: true, message: '请输入账号' }]}
              className="ring-offset-background "
            >
              <Input
                className="placeholder:text-[16px] text-base"
                placeholder="请输入账号"
              />
            </Form.Item>
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <PasswordInput
                className="placeholder:text-[16px] text-base"
                placeholder="请输入密码"
              />
            </Form.Item>
          </Form>
        </div>
        <div className="flex justify-between mt-3">
          <span
            className="text-sm font-normal text-[#1E3A8A] cursor-pointer"
            onClick={() => {
              setStatus('register')
            }}
          >
            注册账号
          </span>
          <span
            className="text-sm text-[#71717A] font-normal  cursor-pointer"
            onClick={() => {
              setConcatText(!concatText)
            }}
          >
            忘记密码？
          </span>
        </div>
        {concatText && (
          <div className="text-sm text-[#71717A] font-normal cursor-default mt-3">
            普通用户，请联系您的管理员为您重置密码。管理员，请联系我们的的售后客服。
          </div>
        )}
      </div>
      <Button
        className="mt-8 bg-[#1E3A8A]"
        disabled={isSubmitting}
        onClick={handleLogin}
      >
        {isLoading ? '登录中' : '登录'}
      </Button>
    </>
  )
}
