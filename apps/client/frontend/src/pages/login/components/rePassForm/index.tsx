import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Form } from 'antd'
import { useKeyPressEvent } from 'react-use'
import { PasVerification, validatePassword } from '../passVerification'
import { rePassword } from '@/api/user'
import { passCrypto } from '@/lib/utils'

export const RePassForm = ({
  setStatus,
}: { setStatus: (status: string) => void }) => {
  const [submitLoading, setSubmitLoading] = React.useState(false)
  const [isValidate, setIsValidate] = React.useState(false)
  const [form] = Form.useForm()
  const [pass, setPass] = useState(0)

  const passwordValue = Form.useWatch('password', form)
  const rePasswordValue = Form.useWatch('rePassword', form)

  const handleRePassword = async () => {
    form.validateFields().then((formValue) => {
      setSubmitLoading(true)
      rePassword(passCrypto(formValue.password))
        .then(() => {
          setStatus('login')
        })
        .finally(() => {
          setSubmitLoading(false)
        })
    })
  }
  useKeyPressEvent('Enter', handleRePassword)

  useEffect(() => {
    setIsValidate(
      validatePassword(
        passwordValue as string,
        rePasswordValue as string,
        pass,
      ),
    )
  }, [passwordValue, rePasswordValue, pass])

  return (
    <>
      <div className="flex-1">
        <div className="text-lg font-semibold mb-2 font-[#18181B]">
          修改密码
        </div>
        <div className="text-sm font-normal text-[#71717A] mb-9">
          为了保证您的安全，请修改密码
        </div>
        <div className="relative">
          <Form
            className="projectform"
            form={form}
            name="dynamic_form_nest_item"
            autoComplete="off"
            layout="vertical"
          >
            <PasVerification setSatisfiedConditions={setPass} />
            <Form.Item
              name="rePassword"
              label="确认密码"
              rules={[
                { required: true, message: '请输入确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'))
                  },
                }),
              ]}
            >
              <Input
                type="password"
                className="focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 focus-within:border-[#D1D5DB]"
                placeholder="输入确认密码"
              />
            </Form.Item>
          </Form>
        </div>
      </div>
      <Button
        className="mt-8 bg-[#1E3A8A]"
        disabled={!isValidate}
        onClick={handleRePassword}
      >
        {submitLoading ? '修改密码中' : '确认'}
      </Button>
    </>
  )
}
