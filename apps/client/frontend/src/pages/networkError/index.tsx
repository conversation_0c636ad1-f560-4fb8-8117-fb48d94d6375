import { useEffect, useState } from 'react'
import { useSearch, useNavigate } from '@tanstack/react-router'
import { Button } from '@/components/ui/button'
import ConnectFailedSvg from '@/assets/svg/network-error/connect-failed.svg?react'
import LoadingFailedSvg from '@/assets/svg/network-error/loading-failed.svg?react'
import { RefreshCcw } from 'lucide-react'
import { GatewayStatusEnum } from './data'

import { checkGatewayStatus } from '@/api/common'

import './index.scss'

const NetworkError = () => {
  const navigator = useNavigate()

  const search = useSearch({ strict: false })
  const [isWan, setIsWan] = useState(true)

  useEffect(() => {
    const parameters = new URLSearchParams(search)
    const type = parameters.get('type')
    setIsWan(type === GatewayStatusEnum.WAN)
  }, [search])

  const refreshWan = async () => {
    const res = await checkGatewayStatus()
    if (res?.data.status === GatewayStatusEnum.OK) {
      navigator({ to: `/dashboard` })
    } else {
      setIsWan(res?.data.status === GatewayStatusEnum.WAN)
    }
  }

  useEffect(() => {
    refreshWan()
  }, [])

  return (
    <div className="network-error w-full h-full">
      <div className="network-error__content h-full flex flex-col justify-center items-center text-sm text-[#111322]">
        <div className="w-[609px]  flex flex-col items-center">
          {isWan ? (
            <>
              <ConnectFailedSvg className="w-[327px] h-[95px] mb-8" />
              <div className="text-xl mb-2 text-[#09090B] font-medium">
                WAN口网络异常
              </div>
              <div className="text-base text-center text-[#71717A] mb-8">
                请检查上游网络情况或SIM卡安装状态
              </div>
              <Button
                className="w-full flex-1 rounded-md bg-[#1E3A8A] text-[#fff] hover:bg-[#1E3A8A] flex font-medium"
                onClick={() => refreshWan()}
              >
                <RefreshCcw className="w-4 h-4 mr-2" />
                刷新
              </Button>
            </>
          ) : (
            <>
              <LoadingFailedSvg className="w-481px] h-[260px] mb-2" />
              <div className="text-xl text-[#09090B] font-medium">
                系统加载失败！请联系您的售后人员进行处理
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default NetworkError
