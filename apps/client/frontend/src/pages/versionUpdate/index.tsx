import { useState, useRef, useEffect, useMemo } from 'react'
import { cn } from '@/lib/utils'

import { useGlobalInfoContext } from '@/provider/GlobalInfoContextProvider'

import { Button } from '@/components/ui/button'
import { BaseContent } from '@/components/BaseContent'
import { UpdateDialog } from './components/UpdateDialog'
import { UpdateLogInfo } from './components/UpdateLogInfo'

import UpdateIcon from '@/assets/svg/update.svg?react'

import { useUpdateLogs } from '@/api/versionUpdate'

export const VersionUpdate = () => {
  const logListRef = useRef(null)

  const { globalInfo } = useGlobalInfoContext()
  const { data: updateLogs, refetch: refetchUpdateLogs } = useUpdateLogs()

  const [open, setOpen] = useState(false)
  const [isOverflowing, setIsOverflowing] = useState(false)
  const [updateMessage, setUpdateMessage] = useState<string>('')

  const currentVersion = useMemo(() => {
    return globalInfo?.data.version
  }, [globalInfo])

  const isUpdateRequired = useMemo(() => {
    const { new_version = '', version = '' } = globalInfo?.data || {}
    if (new_version) {
      if (new_version === version) {
        return false
      }
      return true
    }
    return false
  }, [globalInfo])

  const currentUpdateLogs = useMemo(() => {
    const updateList = updateLogs?.data ? [...updateLogs.data] : []
    const logs = []
    if (updateList.length) {
      for (const log of updateList.reverse()) {
        logs.unshift(log)
        if (log.version === currentVersion) {
          break
        }
      }
    }

    return logs
  }, [updateLogs, currentVersion])

  useEffect(() => {
    if (logListRef.current) {
      const $logList = logListRef.current as HTMLElement
      const resizeObserver = new ResizeObserver(() => {
        // 判断是否出现滚动条
        const isOverflowing = $logList.scrollHeight > $logList.clientHeight
        setIsOverflowing(isOverflowing)
      })
      resizeObserver.observe($logList)
    }
  }, [logListRef])

  return (
    <BaseContent
      title="版本更新"
      showBorderBottom={false}
      titleContain={
        <div className=" text-[#374151] text-sm font-normal  leading-snug flex flex-col">
          系统在更新期间，将无法使用，请选择合适的时间更新
        </div>
      }
      className="pb-0"
      titleClassName={cn('grid gap-0 mb-5 custom-1920:gap-0')}
    >
      <div className="p-6 bg-white rounded-lg border border-zinc-200 flex-col justify-start items-start gap-4 inline-flex">
        <div className="Heading text-gray-900 text-3xl font-semibold leading-9">
          版本： {currentVersion}
        </div>

        <div className="SupportingText text-xl font-normal leading-7">
          {!isUpdateRequired && updateMessage === 'success' && (
            <span className="text-[#059669] ">
              更新成功！系统已经是最新版本!
            </span>
          )}
          {isUpdateRequired && updateMessage === 'error' && (
            <span className="text-[#dc2626]">更新失败，请稍后重试！</span>
          )}
          {!isUpdateRequired && updateMessage === '' && (
            <span className="text-[#475467]">
              您的系统已经是最新版本，无需进行更新！
            </span>
          )}
        </div>

        {!isUpdateRequired && updateMessage === '' ? (
          <Button
            className="rounded-md shadow justify-center items-center gap-2 inline-flex"
            variant="link"
            disabled
            onClick={() => setOpen(true)}
          >
            <span>已是最新版本</span>
          </Button>
        ) : (
          <Button
            className="rounded-md shadow justify-center items-center gap-2 inline-flex"
            variant="link"
            disabled={!isUpdateRequired}
            onClick={() => setOpen(true)}
          >
            <UpdateIcon className="h-4 w-4" color="#fff" />
            <span>立即更新</span>
          </Button>
        )}
      </div>
      {currentUpdateLogs && currentUpdateLogs.length > 0 && (
        <div
          className={cn(
            'log-list flex-1  overflow-y-auto mt-6 flex flex-col gap-y-6',
            isOverflowing && 'pr-2',
          )}
          ref={logListRef}
        >
          {currentUpdateLogs.map((item) => {
            return <UpdateLogInfo key={item.version} {...item} />
          })}
        </div>
      )}

      {open && (
        <UpdateDialog
          open={open}
          setOpen={setOpen}
          setUpdateMessage={setUpdateMessage}
          refetchUpdateLogs={refetchUpdateLogs}
          updataLog={updateLogs?.data[0]}
        />
      )}
    </BaseContent>
  )
}
