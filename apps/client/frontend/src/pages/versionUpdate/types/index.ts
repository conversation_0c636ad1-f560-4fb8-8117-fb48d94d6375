import z from 'zod'
import { ApiResponseGatewaySchema } from '@/api/commonProxy'

export const UpdateLogEnum = z.enum([
  'bug',
  'design',
  'new_feature',
  'feature_update',
])

export type UpdateLogEnumType = z.infer<typeof UpdateLogEnum>

export const UpdateLogInfoSchema = z.object({
  version: z.string(),
  new_features: z.array(z.string()),
  tags: z.array(UpdateLogEnum),
  publish_time: z.string(),
})

export type UpdateLogInfoType = z.infer<typeof UpdateLogInfoSchema>
export const UpdateLogInfoResponseSchema = ApiResponseGatewaySchema(
  z.array(UpdateLogInfoSchema),
)
