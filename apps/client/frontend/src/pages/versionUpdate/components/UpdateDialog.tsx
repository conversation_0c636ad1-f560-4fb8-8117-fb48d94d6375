import { useCallback, useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Progress } from '@/components/ui/progress'
import { useGlobalInfoContext } from '@/provider/GlobalInfoContextProvider'
import { useConfirmUpdateMutation } from '@/api/versionUpdate'

import { UpdateLogInfoType } from '@/pages/versionUpdate/types'

type UpdateDialogProps = {
  open: boolean
  updataLog: UpdateLogInfoType | null | undefined
  setOpen: (open: boolean) => void
  setUpdateMessage: (message: string) => void
  refetchUpdateLogs: () => void
}

export const UpdateDialog = ({
  open,
  updataLog,
  setOpen,
  setUpdateMessage,
  refetchUpdateLogs,
}: UpdateDialogProps) => {
  const { refetchGlobalInfo } = useGlobalInfoContext()
  const [progress, setProgress] = useState(0)
  const [loading, setLoading] = useState(false)
  const confirmUpdateMutation = useConfirmUpdateMutation()

  // 定义一个递归函数，直到 dataInfo 不为 null,因为后端不能获取进度，只有前端模拟，当获取到dataInfo时代表更新成功
  const pollForGlobalInfo = async () => {
    const dataInfo = await refetchGlobalInfo(true)
    if (dataInfo) {
      setProgress(100)
      setTimeout(() => {
        refetchUpdateLogs()
        setUpdateMessage('success')
        setOpen(false)
        setLoading(false)
        setProgress(0)
      }, 3000)
    } else {
      setTimeout(pollForGlobalInfo, 1000) // 每隔 1 秒轮询一次
    }
  }

  const handleUpdateNew = useCallback(() => {
    setLoading(true)
    setProgress(1)
    confirmUpdateMutation.mutate(undefined, {
      onSuccess: () => {
        setTimeout(() => {
          pollForGlobalInfo()
        }, 3000)
      },
      onError: async () => {
        refetchGlobalInfo()
        setUpdateMessage('error')
        setLoading(false)
        setOpen(false)
        setProgress(0)
      },
    })
  }, [confirmUpdateMutation])

  useEffect(() => {
    let time = null
    if (progress === 0) return
    if (loading && progress < 99) {
      time = setTimeout(() => {
        setProgress(progress + 1)
      }, 200)
    } else {
      time && clearTimeout(time)
    }
    return () => {
      time && clearTimeout(time)
    }
  }, [loading, progress])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent
        className="update-dialog-content w-[660px] p-6 bg-white rounded-lg border border-[#f4f4f5]"
        showClose={!loading}
        onPointerDownOutside={(e) => e.preventDefault()}
        onEscapeKeyDown={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>
            <div>
              {loading ? (
                <span>系统版本更新中...</span>
              ) : (
                <span>
                  更新过程可能需要2-5分钟，期间系统将无法使用。确认立即更新？
                </span>
              )}
            </div>
          </DialogTitle>
          <DialogDescription className="text-[#71717A] text-sm font-normal  leading-tight">
            更新过程中请勿断开电源或关闭设备，否则会导致更新失败
          </DialogDescription>
          {updataLog && updataLog.new_features && (
            <div>
              <div className="mt-4 mb-[10px] text-black text-base font-normal  leading-normal sticky ">
                更新内容：
              </div>
              <div className="max-h-[940px] overflow-y-auto relative mb-[24px]">
                <div className="text-[#475569] text-base font-normal leading-normal flex flex-col gap-y-6">
                  {updataLog.new_features.map((item, index) => {
                    return <div key={index}>-{item}</div>
                  })}
                </div>
              </div>
            </div>
          )}
        </DialogHeader>

        {loading ? (
          <div className="flex items-center">
            <Progress
              className="bg-[#F4F4F5]"
              indicatorClassName="bg-[#1E3A8A]"
              value={progress}
            />
            <div className="ml-3 text-[#18181b] text-sm font-medium leading-tight">
              {progress}%
            </div>
          </div>
        ) : (
          <DialogFooter>
            <div className="flex gap-4 mt-4">
              <Button
                className="bg-[#f4f4f5] rounded-md  gap-2  text-[#18181b]"
                onClick={() => setOpen(false)}
              >
                稍后更新
              </Button>
              <Button
                className="rounded-md shadow justify-center items-center gap-2 inline-flex"
                variant="link"
                type="submit"
                onClick={handleUpdateNew}
              >
                立即更新
              </Button>
            </div>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  )
}
