import { useState } from 'react'
import dayjs from 'dayjs'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import { Button } from '@/components/ui/button'

import ChevronDownIcon from '@/assets/svg/chevron-down.svg?react'
import ChevronUpIcon from '@/assets/svg/chevron-up.svg?react'
import UpdateTimeIcon from '@/assets/svg/update-time.svg?react'

import {
  UpdateLogInfoType,
  UpdateLogEnum,
  UpdateLogEnumType,
} from '@/pages/versionUpdate/types'
UpdateLogEnum.Enum.feature_update
const updateTagConfig = {
  [UpdateLogEnum.Enum.feature_update]: {
    textColor: '#1d4ed8', // 700
    bgColor: '#eff6ff', // 50
    borderColor: '#3b82f6', // 500
    dotColor: '#2563eb', // 600
    label: '功能更新',
  },
  [UpdateLogEnum.Enum.new_feature]: {
    textColor: '#1d4ed8', // 700
    bgColor: '#eff6ff', // 50
    borderColor: '#3b82f6', // 500
    dotColor: '#2563eb', // 600
    label: '功能更新',
  },
  [UpdateLogEnum.Enum.bug]: {
    textColor: '#b45309',
    bgColor: '#ffedd5',
    borderColor: '#f97316',
    dotColor: '#ea580c',
    label: 'BUG修复',
  },
  [UpdateLogEnum.Enum.design]: {
    textColor: '#15803d',
    bgColor: '#dcfce7',
    borderColor: '#22c55e',
    dotColor: '#16a34a',
    label: 'Design',
  },
}

const UpdateTags = ({ tags }: { tags: UpdateLogEnumType[] }) => {
  return tags.map((item, index) => {
    const tagInfo = updateTagConfig[item]
    return (
      <div
        key={index}
        className="Tag ml-2 h-6 rounded-md justify-start items-start inline-flex"
      >
        <div
          className="TagBase pl-2 pr-2.5 py-0.5 rounded-3xl border justify-center items-center gap-1 flex"
          style={{
            backgroundColor: tagInfo.bgColor,
            borderColor: tagInfo.borderColor,
          }}
        >
          <div className="Dot w-2 h-2 relative">
            <div
              className={
                'Dot w-1.5 h-1.5 left-[1px] top-[1px] absolute rounded-full'
              }
              style={{ backgroundColor: tagInfo.dotColor }}
            />
          </div>
          <div
            className={'text-sm font-normal leading-tight'}
            style={{ color: tagInfo.textColor }}
          >
            {tagInfo.label}
          </div>
        </div>
      </div>
    )
  })
}

export function UpdateLogInfo({
  version,
  new_features,
  tags,
  publish_time,
}: UpdateLogInfoType) {
  const [isOpen, setIsOpen] = useState(true)
  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className="w-full space-y-2 pb-6 border-b border-gray-200"
    >
      <div className="flex items-center justify-between space-x-4">
        <div className="Text text-gray-900 text-lg font-semibold font-['Inter'] leading-7 flex items-center">
          <div>{version}</div>
          <UpdateTags tags={tags} />
        </div>
        <CollapsibleTrigger asChild>
          <Button className="Button w-10 h-10 p-2.5 bg-white rounded-md border border-[#d4d4d8] justify-center items-center gap-2.5 inline-flex">
            {isOpen ? (
              <ChevronDownIcon className="h-6 w-6" color="#000000" />
            ) : (
              <ChevronUpIcon className="h-4 w-4" color="#000000" />
            )}
          </Button>
        </CollapsibleTrigger>
      </div>
      <CollapsibleContent className="space-md">
        <div className="SupportingText text-[#475569] text-base font-normal leading-normal flex flex-col gap-y-6">
          {new_features.map((item, index) => {
            return <div key={index}>-{item}</div>
          })}
        </div>
        <div className="flex items-center mt-6">
          <UpdateTimeIcon className="Icon w-5 h-5 mr-2" />
          {publish_time && (
            <div className="text-zinc-500 text-base font-normal leading-normal">
              {dayjs(publish_time).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          )}
        </div>
      </CollapsibleContent>
    </Collapsible>
  )
}
