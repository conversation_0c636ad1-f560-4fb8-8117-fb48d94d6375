import { z } from 'zod'

export const ProxyDnsDataSchema = z.object({
  City: z.string().optional(),
  Country: z.string().optional(),
  CountryCode: z.string().optional(),
  IP: z.string().optional(),
  ISP: z.string().optional(),
  Leak: z.boolean().optional(),
})

export const ProxyDnsSchema = ProxyDnsDataSchema.partial().catchall(z.any())
export type ProxyDns = z.infer<typeof ProxyDnsSchema>

export const ProxyOutDataSchema = z.object({
  organization: z.string().optional(),
  longitude: z.number().optional(),
  city: z.string().optional(),
  timezone: z.string().optional(),
  isp: z.string().optional(),
  offset: z.number().optional(),
  asn: z.number().optional(),
  asn_organization: z.string().optional(),
  country: z.string().optional(),
  ip: z.string().optional(),
  latitude: z.number().optional(),
  continent_code: z.string().optional(),
  country_code: z.string().optional(),
  region: z.string().optional(),
  region_code: z.string().optional(),
  postal_code: z.string().optional(),
})
export const ProxyOutSchema = ProxyOutDataSchema.partial().catchall(z.any())

export type ProxyOut = z.infer<typeof ProxyOutSchema>

// 定义代理信息的 schema
export const ProxiesSchema = z.object({
  name: z.string(), // 代理名称
  server: z.string(), // 服务器地址
  port: z.number(), // 端口号
  protocol: z.string(), // 协议
  country_name: z.string(), // 国家名称
  country_code: z.string(), // 国家代码
  city_name: z.string(), // 城市名称
  delay: z.number(), // 延迟
})

// 定义代理信息集合的 schema
export const ProxyInfoSchema = z.object({
  name: z.string(), // 代理信息名称
  proxies: z.array(ProxiesSchema), // 代理数组
})

// 定义整个代理桥接链接的 schema
export const ProxyBridgeLinkSchema = z.object({
  proxy_info: ProxyInfoSchema, // 包含代理信息
})
export type ProxyBridgeLink = z.infer<typeof ProxyBridgeLinkSchema>
