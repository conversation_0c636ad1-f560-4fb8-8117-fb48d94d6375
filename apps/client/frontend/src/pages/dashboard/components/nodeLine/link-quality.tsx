import { useEffect, useRef } from "react";
import * as echarts from "echarts";
import type { EChartsType } from "echarts";

import { SpeedType } from "../../index";
import { PROXY_QUALITY_COLOR, PROXY_STATUS_KEY } from "@/data/index";
import { getDelayStatus } from "@/hooks/useDelayStatus";

const DATA_ZOOM_NUM = 16;

const LinkQuality = ({
  speed,
  speedTimeout,
}: { speed: SpeedType[]; speedTimeout: boolean }) => {
  const outQualityDomRef = useRef<HTMLDivElement | null>(null);
  const qualityDomRef = useRef<HTMLDivElement | null>(null);
  const qualityChart = useRef<EChartsType | null>(null);

  const getQualityOption = () => {
    const data = speed.slice(-DATA_ZOOM_NUM).map((value) => value?.speed) ??
      new Array(DATA_ZOOM_NUM).fill("");
    const categories =
      speed.slice(-DATA_ZOOM_NUM).map((value) => value?.time) ??
        new Array(DATA_ZOOM_NUM).fill("");
    const delayType = getDelayStatus(data[data.length - 1] ?? 0);
    const option = {
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
        containLabel: false,
      },
      legend: {
        show: false,
      },
      tooltip: {
        show: true,
      },
      dataZoom: [
        {
          show: false,
          startValue: 0,
          endValue: DATA_ZOOM_NUM,
        },
      ],
      xAxis: [
        {
          type: "category",
          boundaryGap: [0.1, 0.1],
          data: categories,
          axisLabel: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          z: 1,
        },
      ],
      yAxis: [
        {
          type: "value",
          scale: true,
          min: -1,
          boundaryGap: false,
          splitLine: {
            show: false, // 显示分割线
          },
          axisLabel: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
      ],
      series: [
        {
          name: "上传Bar",
          type: "bar",
          data,
          itemStyle: {
            color: PROXY_QUALITY_COLOR[delayType],
            borderRadius: 5,
            // shadowBlur: 0,
            // shadowColor: 'rgba(30, 58, 138, 0.3)',
            // shadowOffsetX: 0,
            // shadowOffsetY: 24,
          },
          emphasis: {
            itemStyle: {
              // color: PROXY_QUALITY_COLOR[delayType],
            },
          },
          tooltip: {
            show: false,
          },
          barWidth: 4,
          barCategoryGap: "0%",
        },
      ],
      barCategoryGap: "10px",
    };
    return option;
  };

  const handleResize = () => {
    const documentHeight = document.body.clientHeight;
    if (outQualityDomRef.current && qualityDomRef.current) {
      documentHeight > 1080
        ? (qualityDomRef.current.style.height = "79px")
        : (qualityDomRef.current.style.height = "70px");
    }
    qualityChart.current?.resize();
  };

  useEffect(() => {
    // 修复：当延迟超时时，柱状图应该没有高度
    let data = speed.slice(-DATA_ZOOM_NUM).map((value) => value?.speed) ?? [];
    const categories = speed.slice(-DATA_ZOOM_NUM).map((value) =>
      value?.time
    ) ?? [];

    // 如果当前状态为超时，将所有数据设为0或负值，使柱状图没有高度
    if (speedTimeout) {
      data = data.map(() => 0); // 设为0使柱状图没有高度
    }

    categories.length < DATA_ZOOM_NUM &&
      categories.unshift(...Array(DATA_ZOOM_NUM - categories.length).fill(""));
    data.length < DATA_ZOOM_NUM &&
      data.unshift(
        ...Array(DATA_ZOOM_NUM - data.length).fill(speedTimeout ? 0 : ""),
      );

    const delayType = speedTimeout
      ? PROXY_STATUS_KEY.TIMEOUT
      : getDelayStatus(data[data.length - 1] ?? 0);
    qualityChart.current?.setOption({
      xAxis: [{ data: categories }],
      series: [
        {
          itemStyle: {
            color: PROXY_QUALITY_COLOR[delayType],
          },
          data,
        },
      ],
    });
  }, [speed, speedTimeout]);

  useEffect(() => {
    if (qualityChart.current) qualityChart.current.dispose();
    qualityChart.current = echarts.init(qualityDomRef.current);
    const option = getQualityOption();
    option && qualityChart.current.setOption(option);
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
      qualityChart.current?.dispose();
      qualityChart.current = null;
    };
  }, []);
  // h-[74px] custom-1920:h-[103px] max-h-[125px]
  return (
    <div ref={outQualityDomRef} className="flex-1 w-full">
      <div
        ref={qualityDomRef}
        className="h-[70px] custom-1920:h-[79px] max-h-[79px]"
      >
      </div>
    </div>
  );
};

export default LinkQuality;
