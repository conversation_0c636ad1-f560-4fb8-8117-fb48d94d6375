import { useEffect, useMemo, useState } from "react";

import { Combobox, IComboboxValue, optionsType } from "@/components/Combobox";
import { But<PERSON> } from "@/components/ui/button";
import LinkQuality from "./link-quality";

import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";
import { isWretchError } from "@/auth";
import { errorToast } from "@/components/GlobalToast";
import { toast } from "@/components/ui/use-toast";
import { useProxyDelay } from "@/api/dashboard";
import { useQueryClient } from "@tanstack/react-query";

import DropdownButtonSvg from "@/assets/svg/dashborad/dropdown-button.svg";

import { cn, getUrl, testSpeed } from "@/lib/utils";
import {
  PROXY_QUALITY_COLOR,
  PROXY_QUALITY_TYPE,
  PROXY_STATUS_KEY,
} from "@/data/index";
import { getDelayStatus } from "@/hooks/useDelayStatus";
import { SpeedType } from "../../index";
import { useLink, usePathListMutation, useProxiesExit } from "@/api/link";
import { disableNode } from "@/pages/link/data/link-state";
import { DELAY_TYPE_STYLE } from "@/pages/link/types/config";
import { getMedianIndex } from "@/pages/link/components/formAlertDialog/pathChoose";

const netButton = (proxyDelay: number) => {
  const delayType = getDelayStatus(proxyDelay);
  return (
    <>
      <Button
        size="line"
        className="h-[34px] w-[48px] custom-1920:w-[68px] custom-1920:h-[42px] my-3 cursor-default"
        style={{ backgroundColor: PROXY_QUALITY_COLOR[delayType] }}
      >
        {PROXY_QUALITY_TYPE[delayType]}
      </Button>
    </>
  );
};

interface ExitProxieProps {
  exitProxie: {
    name: string;
    country_code: string;
    delay: number;
  } | null;
  setIsTimeout: (value: boolean) => void;
  onLinkChange?: () => void; // 新增：链路变化回调
}

const ExitProxie = ({
  exitProxie,
  setIsTimeout,
  onLinkChange,
}: ExitProxieProps) => {
  const { globalInfo } = useGlobalInfoContext();
  const queryClient = useQueryClient();

  const { data: exitList, refetch: refetchProxies } = useProxiesExit();
  const { data: currentPathList } = useLink();
  const pathListMutation = usePathListMutation();
  const {
    data: proxyDelayData,
    cancelRequest: cancelDelayRequest,
  } = useProxyDelay({ enabled: true }); // 获取代理链路延迟，支持取消

  const url = getUrl();

  // 智能状态检测：检查当前节点是否属于活跃链路
  const isNodeInActivePath = useMemo(() => {
    if (!currentPathList?.data || !exitProxie?.name) {
      return false;
    }

    // 查找活跃的链路（use=true 或 current_ip_use=true）
    const activePaths = currentPathList.data.filter(
      (path) => path.use || path.current_ip_use,
    );

    // 检查当前出口节点是否在任何活跃链路中
    return activePaths.some((path) => path.proxies?.includes(exitProxie.name));
  }, [currentPathList, exitProxie?.name]);

  const proxyInfo = useMemo(() => {
    return globalInfo?.data.proxy_info;
  }, [globalInfo]);

  const flags = useMemo(() => {
    return globalInfo?.data.flags || [];
  }, [globalInfo]);

  const isWg = useMemo(() => {
    const result = false;
    return result;
  }, [proxyInfo]);

  const proxiesOption = useMemo(() => {
    console.log("proxiesOption calculation - exitList:", exitList);
    if (!exitList || !exitList.data) {
      console.log("proxiesOption: exitList is null or has no data");
      return [];
    }

    console.log("proxiesOption: exitList.data length:", exitList.data.length);
    exitList.data.forEach((item, index) => {
      item.i = index;
    });

    const optionsFilter = exitList.data
      ?.filter(
        (proxie) =>
          !disableNode.includes(proxie.use_status) ||
          proxie.name === exitProxie?.name,
      );

    console.log("proxiesOption: optionsFilter length:", optionsFilter?.length);
    // 超时了的数组
    const delayListTimeout = [];
    // 未超时的数组
    const okList = [];
    for (let i = 0; i < optionsFilter.length; i++) {
      const item = optionsFilter[i];
      if (item.delay > 0) {
        okList.push(item);
      } else {
        item.delayStyle = DELAY_TYPE_STYLE.HIGH;
        delayListTimeout.push(item);
      }
    }
    // okList 通过 delay 排序从小到大
    okList.sort((a, b) => a.delay - b.delay);
    const midianIndex = getMedianIndex(okList);
    okList.forEach((item, index) => {
      if (index <= midianIndex) {
        item.delayStyle = DELAY_TYPE_STYLE.LOW;
      } else {
        item.delayStyle = DELAY_TYPE_STYLE.MID;
      }
    });
    const finalOptions = [...okList, ...delayListTimeout]
      .sort((a, b) => (a?.i ?? 0) - (b?.i ?? 0))
      .map((proxie) => ({
        label: proxie.name,
        value: proxie.name,
        icon: (
          <img
            src={`${url}/res/flag3/${proxie.country_code.toLowerCase()}.svg`}
            alt=""
            className="w-4 h-4 mr-2"
          />
        ),
        num: proxie.delay,
        delayStyle: proxie.delayStyle,
      }));

    console.log("proxiesOption: final options length:", finalOptions.length);
    console.log("proxiesOption: final options:", finalOptions);
    return finalOptions;
  }, [exitList, exitProxie]);

  const [speed, setSpeed] = useState<SpeedType[]>([]);
  const [exitProxieName, setExitProxieName] = useState(exitProxie?.name ?? "");

  const getSpeed = async () => {
    // 智能状态检测：区分真正超时的节点和活跃链路中 delay=-1 的节点
    const currentNodeDelay = exitProxie?.delay ?? 0;
    const isNodeUnavailable =
      getDelayStatus(currentNodeDelay) === PROXY_STATUS_KEY.TIMEOUT;

    // 如果节点不可用且不在活跃链路中，则保持超时状态
    if (isNodeUnavailable && !isNodeInActivePath) {
      setIsTimeout(true);
      return;
    }

    // 对于活跃链路中的节点（即使 delay=-1），也进行端到端连通性测试
    const res = await testSpeed(`https://one.one.one.one/favicon.ico`, true);
    const isTimeout = getDelayStatus(res.speed) === PROXY_STATUS_KEY.TIMEOUT;
    setIsTimeout(isTimeout);

    if (isTimeout) return;
    setSpeed((pre) => [...pre, res].slice(-100));
  };

  const errorTosatShow = (err: unknown, errType?: string) => {
    if (isWretchError(err)) {
      const res = err.json;

      const errorMessages = {
        "already use": () =>
          errorToast("当前链路出口节点已被使用，无法独占", toast),
        default: () => errorToast(errType ?? "修改失败", toast),
      };

      for (const [pattern, handler] of Object.entries(errorMessages)) {
        if (res.msg.includes(pattern)) {
          handler();
          break;
        }
      }
    } else {
      errorToast("修改失败", toast);
    }
  };

  const changeLine = (value: IComboboxValue) => {
    if (!currentPathList?.data || currentPathList.data.length === 0) {
      errorToast("无法获取当前路径信息", toast);
      return;
    }

    const currentPath =
      currentPathList.data.find((path) => path.current_ip_use) ||
      currentPathList.data.find((path) => path.use);

    if (!currentPath) {
      errorToast("当前无活动路径", toast);
      return;
    }

    console.log("Found active path:", currentPath);

    console.log("changeLine called with value:", value);
    console.log("currentPath:", currentPath);

    const exitNodeIndex = 2; // 出口节点在三跳路径中的索引位置
    const updatedPathList = {
      ...currentPath,
      use: currentPath.use,
      proxies: currentPath.proxies?.map((proxie, i) => {
        if (i === exitNodeIndex) {
          return value as string;
        }
        return proxie;
      }),
    };

    console.log("updatedPathList:", updatedPathList);

    pathListMutation.mutate(
      updatedPathList,
      {
        onError: (error) => {
          console.error("节点切换失败：", error);
          setExitProxieName(exitProxie?.name ?? "");
          errorTosatShow(error, "节点切换失败");
        },
        onSuccess: (res) => {
          console.log("节点切换成功：", res);
          setExitProxieName(value as string);

          // 链路切换成功后，立即取消正在进行的 API 请求并重新获取数据
          console.log("链路切换成功，取消现有 API 请求并重新获取数据...");

          // 1. 取消正在进行的 API 请求
          cancelDelayRequest();

          // 2. 清除相关查询缓存，强制重新获取
          queryClient.invalidateQueries({ queryKey: ["userOutProxy"] });
          queryClient.invalidateQueries({ queryKey: ["useUserDnsProxy"] });
          queryClient.invalidateQueries({ queryKey: ["proxyDelay"] });

          // 3. 触发父组件的链路变化回调
          onLinkChange?.();

          console.log("已触发数据重新获取");
        },
      },
    );
  };

  const lastSpeed = useMemo(() => {
    // 改进的延迟计算逻辑：根据用户要求，优先使用后端 API，失败时使用节点延迟
    const currentNodeDelay = exitProxie?.delay ?? 0;

    // 获取代理链路的真实延迟（通过后端 API 测试）
    const proxyApiResponse = proxyDelayData as any;
    const proxyEndToEndDelay = proxyApiResponse?.data?.delay ??
      proxyApiResponse?.delay ?? -1;
    const isProxyApiSuccess = proxyApiResponse?.code === 200 ||
      (proxyApiResponse && !proxyApiResponse.error);

    console.log("lastSpeed calculation:", {
      exitProxieName: exitProxie?.name,
      isNodeInActivePath,
      currentNodeDelay,
      proxyEndToEndDelay,
      isProxyApiSuccess,
      proxyApiResponse,
    });

    // 显示优先级（根据用户要求修复的逻辑）：
    if (isNodeInActivePath) {
      // 1. 活跃链路中的节点：优先显示后端 API 测试的端到端延迟（仅当 API 成功时）
      if (isProxyApiSuccess && proxyEndToEndDelay > 0) {
        console.log(
          "Using backend proxy delay for active path:",
          proxyEndToEndDelay,
        );
        return proxyEndToEndDelay;
      }
      // 2. 如果后端延迟测试失败，使用节点自身的延迟（不使用前端测试）
      if (currentNodeDelay > 0) {
        console.log(
          "Backend API failed, using node delay for active path:",
          currentNodeDelay,
        );
        return currentNodeDelay;
      }
      // 3. 活跃链路但无法获取任何延迟数据，标记为超时
      console.log(
        "Active path but no delay data available, marking as timeout",
      );
      return -1;
    } else {
      // 4. 非活跃链路中的节点：显示节点自身的延迟
      if (currentNodeDelay > 0) {
        console.log("Using node delay for inactive path:", currentNodeDelay);
        return currentNodeDelay;
      }
      // 5. 非活跃链路且节点无延迟数据，标记为超时
      console.log("Inactive path with no node delay, marking as timeout");
      return -1;
    }
  }, [
    exitProxie?.delay,
    exitProxie?.name,
    isNodeInActivePath,
    proxyDelayData,
  ]);

  // 修复：确保超时状态正确计算，用于柱状图显示
  const isCurrentTimeout = useMemo(() => {
    return getDelayStatus(lastSpeed) === PROXY_STATUS_KEY.TIMEOUT;
  }, [lastSpeed]);

  useEffect(() => {
    setExitProxieName(exitProxie?.name ?? "");

    // 修复：使用 lastSpeed 而不是原始节点延迟来判断超时状态
    setIsTimeout(isCurrentTimeout);

    // 修复：超时时清空 speed 数组，确保柱状图没有高度
    if (isCurrentTimeout) {
      setSpeed([]); // 清空历史数据
      return;
    }

    // 只有在非超时状态下才添加速度数据
    if (lastSpeed > 0) {
      setSpeed((pre) =>
        [
          ...pre,
          {
            speed: lastSpeed,
            time: new Date().toLocaleTimeString().replace(/^\D*/, ""),
          },
        ].slice(-100)
      );
    }
  }, [exitProxie, lastSpeed, isCurrentTimeout]);

  useEffect(() => {
    // 智能状态检测：活跃链路中的 delay=-1 节点也要进行间隔测试
    const currentNodeDelay = exitProxie?.delay ?? 0;
    const isNodeUnavailable =
      getDelayStatus(currentNodeDelay) === PROXY_STATUS_KEY.TIMEOUT;

    // 只有在节点真正不可用（不在活跃链路且 delay=-1）时才停止测试
    if (isNodeUnavailable && !isNodeInActivePath) {
      return;
    }

    const intervalId = setInterval(() => getSpeed(), 2000);
    return () => clearInterval(intervalId);
  }, [exitProxie?.delay, isNodeInActivePath]); // 添加 isNodeInActivePath 依赖

  const SearchOptionText = (number_: number, option?: optionsType) => {
    const delayStyle = option?.delayStyle;
    const timeout = getDelayStatus(number_) === PROXY_STATUS_KEY.TIMEOUT;
    return (
      <span
        style={{
          color: delayStyle || "",
        }}
        className={`text-[#71717A] text-sm font-normal mr-2 ${
          timeout && "text-[#DC2626]"
        }`}
      >
        {timeout ? "超时" : number_ + "ms"}
      </span>
    );
  };

  const comboboxProps = {
    title: "输入节点名称...",
    showRadio: true,
    showSearch: true,
    className: "px-0 py-0 w-[22px] h-[22px]",
    contentClass:
      "border border-[#D1D5DB] mt-[10px] rounded-md p-2 min-w-[520px] left-[-20px]",
    itemClass: `px-2 py-2.5 `,
    contentProps: {
      alignOffset: -229,
      sideOffset: 10,
    },
    inputClassName: "dashboard_node_search",
    radioText: SearchOptionText,
  };
  return (
    <div
      className={cn(
        "roundCardType rounded-lg mt-[25px] mb-5 flex flex-col justify-between",
        "w-[282px] custom-1920:w-[297px] p-3 custom-1920:p-4",
        "my-3 custom-1920:mt-[25px] custom-1920:mb-5",
        flags?.includes("low_mode_enable") && "!w-full",
      )}
    >
      <div>
        <p className="font-semibold mb-1.5 custom-1920:mb-3">出口节点</p>

        <div
          className={cn(
            "w-full h-[54px] p-3 transition-all rounded-[5px] flex items-center justify-between bg-[#F3F4F6] text-[#172554]",
          )}
        >
          <div className="flex items-center flex-1">
            <div className="w-[30px] h-[30px] flex items-center justify-center rounded-full bg-[#FFF] mr-[11px]">
              <img
                src={`${url}/res/flag3/${exitProxie?.country_code?.toLowerCase()}.svg`}
                alt=""
                className="w-5 h-5"
              />
            </div>
            <div>{exitProxie?.name ?? ""}</div>
          </div>
          <div className="flex items-center space-x-[7px]">
            <div className="flex flex-nowrap">
              {
                // 修复：使用正确的超时状态显示延迟
                isCurrentTimeout
                  ? <span className="text-[#DC2626]">超时</span>
                  : (
                    lastSpeed + "ms"
                  )
              }
            </div>
            {(() => {
              console.log("Combobox render check:");
              console.log("- proxiesOption.length:", proxiesOption.length);
              console.log("- exitProxieName:", exitProxieName);
              return !isWg && (
                <Combobox
                  value={exitProxieName}
                  {...comboboxProps}
                  muiltip={false}
                  typeBorder
                  options={proxiesOption}
                  onChange={(value) => {
                    console.log("Combobox onChange called with:", value);
                    setExitProxieName(value as string);
                    changeLine(value);
                  }}
                  onComboxOpen={() => {
                    console.log("Combobox onComboxOpen called");
                    refetchProxies();
                  }}
                >
                  <div className="w-[22px] h-[22px] flex cursor-pointer">
                    <img
                      src={DropdownButtonSvg}
                      alt=""
                      className="w-full h-full"
                    />
                  </div>
                </Combobox>
              );
            })()}
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <p className="font-semibold ">链路质量</p>
        <div className="flex flex-col">
          {netButton(isCurrentTimeout ? -1 : lastSpeed)}
          {/* 修复：使用正确的超时状态传递给 LinkQuality */}
          {!flags?.includes("low_mode_enable") && (
            <LinkQuality speed={speed} speedTimeout={isCurrentTimeout} />
          )}
        </div>
      </div>
    </div>
  );
};

export default ExitProxie;
