import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from "react";

import { CardTitle } from "../../../../components/CardTitle";
import { ProxyGeo } from "./proxy-geo";

import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";
import { useUserDnsProxy, useUserOutProxy } from "@/api/dashboard";
import { SpecialLinksType, useStorageQuery } from "@/api/kv";
import { UseQueryResult } from "@tanstack/react-query";

import { debounce } from "@/lib/utils";
import { v4 as uuidv4 } from "uuid";

import ExitProxie from "./exit-proxie";
import { MultipleLinks } from "@/pages/link/components/multipleLinks";
import { MultipleLinksJump3 } from "@/pages/link/components/multipleLinksJump3";
import SpecialLinks from "@/pages/link/components/multipleLinks/special-links";
import { PathList } from "@/pages/link/types/proxy-bridge-type";
import { countryCodeMap } from "../../data";
import { KV_KEYS } from "@/data";

interface NodeLineProp {
	setIsTimeout: (value: boolean) => void;
}

export const NodeLine = ({ setIsTimeout }: NodeLineProp) => {
	const { globalInfo } = useGlobalInfoContext();

	// 状态管理：控制节点切换状态
	const [isNodeSwitching, setIsNodeSwitching] = useState(false);

	// 使用稳定的 UUID，避免每次重新渲染时生成新的查询键
	const stableUuid = useRef(uuidv4()).current;

	// 修改 DNS hook 配置，不再受 isNodeSwitching 控制，确保数据持久性
	const {
		data: dnsData,
		refetch: refetchDns,
		cancelRequest: cancelDnsRequest,
	} = useUserDnsProxy(stableUuid, {
		enabled: true, // 始终启用，确保数据可用
	});

	const {
		data: ipData,
		refetch: refetchIp,
		cancelRequest: cancelIpRequest,
	} = useUserOutProxy({
		enabled: true, // 始终启用，确保数据可用
	});

	const { data: specialLinks } = useStorageQuery(
		KV_KEYS.SPECIAL_LINKS,
	) as UseQueryResult<{ data: SpecialLinksType } | null>;

	const proxyInfo = useMemo(() => {
		return globalInfo?.data.proxy_info;
	}, [globalInfo]);

	const isWg = useMemo(() => false, [proxyInfo]); // WG functionality removed

	const flags = useMemo(() => {
		return globalInfo?.data.flags || [];
	}, [globalInfo]);

	const proxieList = useMemo(() => {
		// 检查是否正在加载中
		if (!globalInfo) {
			return {
				name: "System loading...",
				proxies: ["Loading", "Loading", "Loading"],
				use: false,
				proxies_code: ["", "", ""],
				current_ip_use: false,
				change_time: 0,
				change_country_array: null,
			} as PathList;
		}

		// 检查 proxyInfo 是否存在且包含有效数据
		if (!proxyInfo || !proxyInfo.proxies) {
			return {
				name: "No link data available",
				proxies: ["", "", ""],
				use: false,
				proxies_code: ["", "", ""],
				current_ip_use: false,
				change_time: 0,
				change_country_array: null,
			} as PathList;
		}

		// 检查 proxies 是否为 null 或空数组
		if (proxyInfo.proxies === null || proxyInfo.proxies.length === 0) {
			return {
				name: proxyInfo.name || "Empty link",
				proxies: ["", "", ""],
				use: false,
				proxies_code: ["", "", ""],
				current_ip_use: false,
				change_time: proxyInfo.change_time || 0,
				change_country_array: null,
			} as PathList;
		}

		// 构建当前活跃链路的节点信息
		const proxies = [];
		const proxies_code = [];

		// 处理实际的代理节点数据，确保至少显示 3 个位置
		for (let i = 0; i < Math.max(3, proxyInfo.proxies.length); i++) {
			if (i < proxyInfo.proxies.length && proxyInfo.proxies[i]) {
				const proxy = proxyInfo.proxies[i];
				// 确保节点名称和国家代码都有值
				const nodeName = proxy.name && proxy.name.trim() !== ""
					? proxy.name
					: `Node ${i + 1}`;
				const countryCode =
					proxy.country_code && proxy.country_code.trim() !== ""
						? proxy.country_code
						: "";

				proxies.push(nodeName);
				proxies_code.push(countryCode);
			} else if (i < 3) {
				// 为缺失的节点位置填充空字符串，但只限前3个位置
				proxies.push("");
				proxies_code.push("");
			}
		}

		const result = {
			name: proxyInfo.name || "当前链路",
			proxies,
			use: true,
			proxies_code,
			current_ip_use: false,
			change_time: proxyInfo.change_time || 0,
			change_country_array: null,
		} as PathList;

		return result;
	}, [proxyInfo]);

	const exitProxie = useMemo(
		() =>
			isWg
				? {
					country_code: ipData?.country_code ?? "",
					name: countryCodeMap[
						ipData?.country_code?.toUpperCase() ?? ""
					] ?? "",
					delay: ipData?.delay ?? 110,
				}
				: proxyInfo?.proxies
				? proxyInfo.proxies[proxyInfo.proxies.length - 1]
				: null,
		[proxieList, isWg, ipData],
	);

	const [onlyIcon, setOnlyIcon] = useState(false);
	const [, setHopCount] = useState("3");

	const linksDomRef = useRef<HTMLDivElement | null>(null);
	const outDomRef = useRef<HTMLDivElement | null>(null);
	const minWidth = useRef(0);

	const hop = useMemo(() => {
		return (
			specialLinks?.data
				?.[globalInfo?.data.device_information.you_ip ?? ""]
				?.hop ?? "3"
		);
	}, [specialLinks, globalInfo]);

	const handleResize = () => {
		if (linksDomRef.current && outDomRef.current) {
			const innerW = linksDomRef.current.offsetWidth;
			const outW = outDomRef.current.offsetWidth;
			setOnlyIcon((preOnlyIcon) => {
				if (preOnlyIcon) {
					return minWidth.current > outW;
				} else {
					innerW > outW && (minWidth.current = outW);
					return innerW > outW;
				}
			});
		}
	};

	useEffect(() => {
		setHopCount(hop);
	}, [hop]);

	useEffect(() => {
		refetchDns();
		refetchIp();
	}, [exitProxie]);

	useEffect(() => {
		refetchDns();
		refetchIp();
		const resizeObserver = new ResizeObserver(debounce(handleResize, 100));
		if (outDomRef.current) {
			resizeObserver.observe(outDomRef.current);
		}
		return () => {
			resizeObserver?.disconnect();
		};
	}, []);

	// 链路变化回调函数
	const handleLinkChange = useCallback(() => {
		// 设置节点切换状态，暂时禁用 API 调用
		setIsNodeSwitching(true);

		cancelDnsRequest();
		cancelIpRequest();

		// 延迟重新启用 API 并触发数据获取
		setTimeout(() => {
			setIsNodeSwitching(false);

			setTimeout(() => {
				refetchIp();
				refetchDns();
			}, 1000);
		}, 500);
	}, [cancelDnsRequest, cancelIpRequest, refetchIp, refetchDns]);

	// 定期刷新机制 - 仅在非切换状态下
	useEffect(() => {
		if (isNodeSwitching) return;

		const refreshInterval = setInterval(() => {
			refetchIp();
			refetchDns();
		}, 30000); // 30 秒刷新一次

		return () => clearInterval(refreshInterval);
	}, [isNodeSwitching, refetchIp, refetchDns]);

	return (
		<div className="cardContainer">
			<CardTitle
				title="当前链路"
				className="mb-0 pr-1"
				toolbar={
					<div
						ref={outDomRef}
						className="ml-5 min-w-[60px] flex space-x-2 flex-1 justify-end"
					>
						<div
							ref={linksDomRef}
							className="flex-1 flex justify-end space-x-3"
						>
							<div className="flex-1 flex justify-end">
								{
									isWg
										? (
											<SpecialLinks
												hopCount={hop}
												exitNode={exitProxie}
												onlyIcon={onlyIcon}
											/>
										)
										: flags?.includes("jump3_disable")
										? (
											<MultipleLinks
												linkList={proxieList}
												className="text-xs"
												onlyIcon={onlyIcon}
												hideExclusiveStyle={true}
												onLinkChange={handleLinkChange}
											/>
										)
										: (
											<MultipleLinksJump3
												linkList={proxieList}
												className="text-xs"
												onlyIcon={onlyIcon}
												hideExclusiveStyle={true}
												onLinkChange={handleLinkChange}
											/>
										) //  noSelect={!!proxyInfo?.change_time}
								}
							</div>
						</div>
					</div>
				}
			/>
			<div className="flex flex-1 justify-between space-x-8 custom-1920:space-x-[64px] pr-1">
				{!flags?.includes("low_mode_enable") && (
					<div className="flex-1">
						<ProxyGeo
							dnsData={dnsData}
							ipData={ipData}
							proxyInfo={proxyInfo ?? undefined}
						/>
					</div>
				)}
				<ExitProxie
					exitProxie={exitProxie}
					setIsTimeout={setIsTimeout}
					onLinkChange={handleLinkChange}
				/>
			</div>
		</div>
	);
};
