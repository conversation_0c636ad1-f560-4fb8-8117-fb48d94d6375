import { useEffect, useMemo, useRef } from "react";
import { z } from "zod";

import * as echarts from "echarts";
// import 'echarts-gl';
import { useQueryClient } from "@tanstack/react-query";
import type { EChartsType } from "echarts";

import { startCountry } from "@/pages/data";
import worldGeoJson from "@/assets/echarts-map/json/world.json";
import { countryCodeMap, countryNameMap, geoCoordMap } from "../../data";
import {
	ProxyDns,
	ProxyDnsSchema,
	ProxyOut,
} from "@/pages/dashboard/types/proxy-out-type";
import { ProxyInfo } from "../../types/proxy";
import "@/scss/country-flag.scss";
import { getUrl } from "@/lib/utils";

interface LinesItemType {
	name: string;
	country_code: string;
	value: number[];
}

type LinesDataType = [LinesItemType, LinesItemType];

type LinesType = [string, LinesDataType[]];

interface ProxyGeoProps {
	ipData: ProxyOut | undefined;
	dnsData: ProxyDns | undefined;
	proxyInfo: ProxyInfo | undefined;
}

export const ProxyGeo = ({ ipData, dnsData, proxyInfo }: ProxyGeoProps) => {
	const queryClient = useQueryClient();
	const proxyGeoRef = useRef<EChartsType | null>(null);

	const ipInfo = useMemo(() => {
		if (!ipData && !proxyInfo) return null;
		const length_ = proxyInfo?.proxies?.length ?? 1;
		const exit = proxyInfo?.proxies?.[length_ - 1];
		const countryCode = exit?.country_code ?? ipData?.country_code ?? "";
		const ip = ipData?.ip ?? "-";
		return {
			country_code: countryCode?.toUpperCase(),
			ip,
		};
	}, [ipData, proxyInfo]);

	const mainToData = useMemo(() => {
		const proxiesList = proxyInfo?.proxies ?? [];
		const data: typeof startCountry[] = [];
		// console.log(proxiesList,'proxiesList')
		proxiesList.forEach((item) => {
			data.push({ country_code: item.country_code });
		});
		return data;
	}, [proxyInfo]);

	const getLineItem = (
		preCode: string,
		nextCode: string,
	): [LinesItemType, LinesItemType] => {
		return [
			{
				name: countryCodeMap[preCode] ?? "",
				value: geoCoordMap[preCode] ?? [],
				country_code: preCode,
			},
			{
				name: countryCodeMap[nextCode] ?? "",
				value: geoCoordMap[nextCode] ?? [],
				country_code: nextCode,
			},
		];
	};

	// 适配连线数据格式
	const lineData = useMemo(() => {
		// 实线数据处理
		const lineData: LinesType[] = [[startCountry.country_code, []]];
		mainToData.forEach((item, index) => {
			if (index === mainToData.length - 1) return;
			const countryCode = item.country_code.toUpperCase();
			const nextCountryCode =
				mainToData[index + 1]?.country_code.toUpperCase() ?? "";
			lineData[0]?.[1].push(getLineItem(countryCode, nextCountryCode));
		});
		return lineData;
	}, [proxyInfo]);

	// 获取连线经纬度数据
	const convertData = (data: LinesDataType[]) => {
		const res = [];
		for (let index = 0; index < data.length; index++) {
			const dataIndex = data[index];
			if (!dataIndex) continue;
			const fromCoord = geoCoordMap[dataIndex[0]?.country_code];
			const toCoord = geoCoordMap[dataIndex[1]?.country_code];
			if (fromCoord && toCoord) {
				// res.push([
				//     {
				//         coord: fromCoord,
				//     },
				//     {
				//         coord: toCoord,
				//     },
				// ]);
				res.push([fromCoord, toCoord]);
			}
		}
		return res;
	};

	const getSeries = () => {
		const series: echarts.SeriesOption[] = [];
		lineData.forEach((item) => {
			series.push(
				{
					name: item[0],
					type: "lines",
					zlevel: 1,
					lineStyle: {
						curveness: -0.4, // 飞线弧度
						type: "solid", // 飞线类型
						// color: '#0253F0', // 飞线颜色
						color: {
							type: "linear",
							x: 0,
							y: 0,
							x2: 1,
							y2: 0,
							colorStops: [
								{
									offset: 0,
									color: "#0253F0", // 0% 处的颜色
								},
								{
									offset: 1,
									color: "#0052B4", // 100% 处的颜色
								},
							],
							global: false, // 缺省为 false
						},
						width: 1.5, // 飞线宽度
						opacity: 1,
					},
					data: (convertData(
						item[1].slice(0, -1),
					) as echarts.LinesSeriesOption["data"]),
				},
				{
					name: item[0],
					type: "lines",
					zlevel: 1,
					lineStyle: {
						curveness: -0.4, // 飞线弧度
						type: "solid", // 飞线类型
						// color: '#0253F0', // 飞线颜色
						color: {
							type: "linear",
							x: 0,
							y: 0,
							x2: 0,
							y2: 1,
							colorStops: [
								{
									offset: 0,
									color: "#059669", // 0% 处的颜色
								},
								{
									offset: 1,
									color: "#0052B4", // 100% 处的颜色
								},
							],
							global: false, // 缺省为 false
						},
						width: 1.5, // 飞线宽度
						opacity: 1,
					},
					data: (convertData(
						item[1].slice(-1),
					) as echarts.LinesSeriesOption["data"]),
				},
				{
					type: "effectScatter",
					coordinateSystem: "geo",
					zlevel: 0,
					color: "#0052B4",
					rippleEffect: {
						period: 8, //动画时间，值越小速度越快
						brushType: "fill", //波纹绘制方式 stroke, fill
						scale: 3, //波纹圆环最大限制，值越大波纹越大
					},
					symbol: "circle",
					symbolSize: 14,
					tooltip: {
						trigger: "item",
						formatter: (value) => {
							const parameters = value as unknown as {
								name: string;
								data: { datas: { country_code: string } };
							};
							return countryCodeMap[
								parameters.data.datas.country_code.toUpperCase()
							] as string;
						},
					},
					data: (item[1].map((dataItem) => {
						return {
							name: dataItem[0].name,
							value: geoCoordMap[
								dataItem[0].country_code.toUpperCase()
							],
							datas: { ...dataItem[0] },
						};
					}) ?? []),
				},
				{
					type: "effectScatter",
					coordinateSystem: "geo",
					zlevel: 0,
					color: "#059669",
					rippleEffect: {
						//涟漪特效
						period: 8, //动画时间，值越小速度越快
						brushType: "fill", //波纹绘制方式 stroke, fill
						scale: 3, //波纹圆环最大限制，值越大波纹越大
					},
					label: {
						show: false,
					},
					symbol: "circle",
					symbolSize: 14,
					tooltip: {
						backgroundColor: "#fff",
						textStyle: {
							color: "#fff",
						},
						position: "top",
						extraCssText:
							"box-shadow: 0px 12px 16px -4px rgba(16, 24, 40, 0.08), 0px 4px 6px -2px rgba(16, 24, 40, 0.03);border-radius: 8px;color: #111827;",
						padding: [12, 16],
						borderWidth: 0,
						// borderCorlor: '#fff',
						formatter: function (value) {
							console.log(
								"ProxyGeo tooltip formatter - value:",
								value,
							);
							const parameters = value as unknown as {
								data: {
									datas: {
										ip: string;
										tradingCountry: string;
										dns:
											| z.infer<typeof ProxyDnsSchema>
											| null;
										countryCode: string;
									};
								};
							};
							const { dns, ip, tradingCountry, countryCode } =
								parameters.data.datas;

							console.log("ProxyGeo tooltip - DNS data:", dns);
							console.log("ProxyGeo tooltip - IP data:", ip);
							console.log(
								"ProxyGeo tooltip - Current dnsData from closure:",
								dnsData,
							);

							// 修复 DNS 数据处理逻辑 - 使用实时的 dnsData 而不是参数中的 dns
							let dnsInfo: string[] = [];
							console.log(
								"ProxyGeo tooltip - Raw DNS data from params:",
								dns,
							);
							console.log(
								"ProxyGeo tooltip - Using real-time dnsData:",
								dnsData,
							);

							// 优先使用实时的 dnsData，如果没有则使用参数中的 dns
							const actualDnsData = dnsData || dns;

							if (
								actualDnsData &&
								typeof actualDnsData === "object"
							) {
								// 后端返回格式：{"*************":{"City":"Hong Kong","IP":"*************",...}}
								const dnsKeys = Object.keys(actualDnsData);
								console.log(
									"ProxyGeo tooltip - DNS keys:",
									dnsKeys,
								);

								// 直接使用 key 作为 IP 地址，因为后端返回的 key 就是 IP 地址
								dnsKeys.forEach((key) => {
									const value = actualDnsData[key];
									console.log(
										`ProxyGeo tooltip - Processing DNS key: ${key}, value:`,
										value,
									);

									// 首先检查 key 是否为 IP 地址格式（这是后端的主要格式）
									if (
										key &&
										/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/
											.test(key)
									) {
										if (!dnsInfo.includes(key)) {
											dnsInfo.push(key);
											console.log(
												`ProxyGeo tooltip - Added DNS IP from key: ${key}`,
											);
										}
									}

									// 然后检查 value 中是否有 IP 字段
									if (
										value && typeof value === "object" &&
										value.IP
									) {
										if (!dnsInfo.includes(value.IP)) {
											dnsInfo.push(value.IP);
											console.log(
												`ProxyGeo tooltip - Added DNS IP from object: ${value.IP}`,
											);
										}
									}

									// 最后检查 value 是否为字符串格式的 IP
									if (
										typeof value === "string" &&
										/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/
											.test(value)
									) {
										if (!dnsInfo.includes(value)) {
											dnsInfo.push(value);
											console.log(
												`ProxyGeo tooltip - Added DNS IP from string: ${value}`,
											);
										}
									}
								});
							}

							console.log(
								"ProxyGeo tooltip - Final DNS info array:",
								dnsInfo,
							);

							// 如果仍然没有找到 DNS 信息，记录调试信息
							if (dnsInfo.length === 0) {
								console.log(
									"ProxyGeo tooltip - No DNS data found, params DNS:",
									dns,
								);
								console.log(
									"ProxyGeo tooltip - No DNS data found, real-time dnsData:",
									dnsData,
								);
							}

							const url = getUrl();
							//   < div class="fi fi-${countryCode.toLowerCase()}" style = 'width: 20px; height: 20px; margin-right: 5px;' ></div >
							let res = "";
							res += `
							                             <div style='display:flex;flex-direction: column;align-items: center;font-size: 12px;line-height: 18px; max-height: 140px;overflow: hidden;'>
							                                 <div style='color:#111827; width: 24px; height: 24px; min-height: 24px;border-radius: 50%;overflow: hidden;'>
							                                     <img src="${url}/res/flag3/${countryCode.toLowerCase()}.svg" alt="${countryCode}" style='width: 24px; height: 24px; margin-right: 5px;display: inline-block;' />
							                                 </div>
							                                 <div style='color:#111827;margin: 8px 0px 4px 0px;font-weight:600; '>
							                                     ${tradingCountry}
							                                 </div>
							                                 <div style='color:#6B7280;flex: 1'>
							                                     <div>IP 出口：${
								ip || "-"
							}</div>
							                                     <div style='display:flex;'>
							                                         <div>DNS 出口：</div>
							                                         <div>
							                                         `;
							if (dnsInfo.length > 0) {
								dnsInfo.forEach((dnsIP) => {
									res += `<div>${dnsIP}</div>`;
								});
							} else {
								res += `<div>-</div>`;
							}

							res += `</div>
							                                     </div>
							                                 </div>
							                             </div>`;

							return res;
						},
					},
					data: ipInfo
						? ([
							{
								name: ipInfo.country_code,
								// concat([10000]) 决定图例的颜色
								value: geoCoordMap[ipInfo.country_code ?? ""]
									?.concat([
										10000,
									]),
								datas: {
									tradingCountry: countryCodeMap[
										ipInfo.country_code ?? ""
									],
									ip: ipInfo.ip,
									dns: dnsData, // 确保使用最新的 dnsData
									countryCode: ipInfo.country_code,
								},
								type: "exit",
							},
						] as unknown as echarts.EffectScatterSeriesOption[
							"data"
						])
						: [],
				} as echarts.EffectScatterSeriesOption,
			);
		});
		return series;
	};

	const getOption = (domImg: HTMLCanvasElement) => {
		const series = getSeries();

		const option = {
			backgroundColor: "#fff",
			// 底图样式
			geo: {
				map: "world", // 地图类型
				roam: false, // 是否开启缩放
				zoom: 1.1, // 初始缩放大小
				// center: [11.3316626, 19.5845024], // 地图中心点
				layoutCenter: ["50%", "50%"], //地图位置
				scaleLimit: {
					// 缩放等级
					min: 0.5,
					max: 3,
				},
				label: {
					show: false,
				},
				nameMap: countryNameMap, // 自定义地区的名称映射
				// 三维地理坐标系样式
				itemStyle: {
					areaColor: {
						type: "pattern", // 使用图案填充
						image: domImg, // 图像路径
						repeat: "repeat", // 重复填充
					},
					borderColor: "#ffffff", // 边框颜色
					borderWidth: 0, // 边框宽度
				},

				// 鼠标悬浮样式
				emphasis: {
					itemStyle: {
						color: "#213266",
						// areaColor: 'rgba(0,162,248, .6)',
					},
					label: {
						show: false,
					},
				},
				tooltip: {
					show: true,
					trigger: "item",
					triggerOn: "click", // 提示框触发的条件
					enterable: true, // 鼠标是否可进入提示框浮层中，默认为 false，如需详情内交互，如添加链接，按钮，可设置为 true
					backgroundColor: "rgba(0,0,0,0.8)",
					borderColor: "rgba(0,0,0,0.2)",
					textStyle: {
						color: "#fff",
					},
					formatter: (parameters: {
						name: string;
						data:
							| {
								name: string;
								datas: { tradingCountry: string };
							}
							| undefined;
					}) => {
						if (parameters.data?.name) {
							return parameters.data.datas.tradingCountry;
						}
						return parameters.name;
					},
				},
			},
			animation: false,
			series,
			tooltip: {
				show: true,
				// triggerOn: 'click',
				trigger: "item",
				enterable: false,
			},
		};

		return option;
	};

	const createBgCanvas = () => {
		// 创建一个 2x2 像素的 Canvas 元素
		const canvas = document.createElement("canvas");
		canvas.width = 5;
		canvas.height = 5;

		// 获取 Canvas 的上下文
		const context = canvas.getContext("2d");
		if (context == null) return canvas;

		// 绘制白色正方形
		context.fillStyle = "white"; // 设置填充颜色为白色
		context.fillRect(0, 0, 5, 5); // 填充整个画布

		// 绘制灰色圆形
		context.fillStyle = "#D1D5DB"; // 设置填充颜色为灰色
		context.beginPath(); // 开始路径
		context.arc(2, 2, 1.5, 0, Math.PI * 2); // 绘制圆形，圆心在 (2, 2)，半径为 1.5
		context.fill(); // 填充圆形
		return canvas;
	};

	const handleResize = () => {
		if (!proxyGeoRef.current) return;
		requestAnimationFrame(() => proxyGeoRef.current?.resize());
	};

	useEffect(() => {
		const series = getSeries();
		proxyGeoRef.current?.setOption({ series }, false); // 不合并，完全替换

		// 延迟一点时间再显示 tooltip，确保数据已更新
		setTimeout(() => {
			proxyGeoRef.current?.dispatchAction({
				type: "showTip",
				seriesIndex: 3,
				dataIndex: 0,
			});
		}, 100);
	}, [dnsData, ipInfo, lineData]);

	useEffect(() => {
		const domImg = createBgCanvas();

		const chartDom = document.getElementById("proxyGeo");
		proxyGeoRef.current = echarts.init(chartDom, null, {
			renderer: "canvas",
		});
		echarts.registerMap(
			"world",
			worldGeoJson as unknown as Parameters<
				typeof echarts.registerMap
			>[1],
		);

		const option = getOption(domImg);
		option && proxyGeoRef.current?.setOption(option);

		// 页面 resize 时触发
		window.addEventListener("resize", handleResize);

		proxyGeoRef.current.dispatchAction({
			type: "showTip",
			seriesIndex: 3,
			dataIndex: 0,
		});

		return () => {
			queryClient.invalidateQueries({
				queryKey: [
					"useUserDnsProxy",
					"userOutProxy",
					"proxyBridgeLinkInfo",
				],
			});
			window.removeEventListener("resize", handleResize);
			proxyGeoRef.current?.dispose();
			proxyGeoRef.current = null;
		};
	}, []);

	return (
		<div className="w-full h-full flex flex-col relative z-[5]">
			<div id="proxyGeo" className="flex-1"></div>
		</div>
	);
};
