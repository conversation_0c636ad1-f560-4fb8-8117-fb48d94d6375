import { useMemo } from "react";
import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";
import { cn } from "@/lib/utils";
import "./index.scss";
import type { InterfaceInfo } from "../../types/proxy";

export const InterfaceStatus = () => {
  const { globalInfo } = useGlobalInfoContext();

  const interfaceInformation = useMemo(
    () => globalInfo?.data.interface_information,
    [globalInfo],
  );

  const wanState = (proxy: InterfaceInfo[number]) => {
    return proxy.status === "up" ? "wan-link" : "default-line";
  };

  const interfaceText = (proxy: InterfaceInfo[number]) => {
    const { type, interface: interfaceType } = proxy;
    if (["wwan0", "wlan0", "wlan1"].includes(interfaceType)) {
      return interfaceType === "wwan0"
        ? "sim 卡"
        : interfaceType === "wlan0"
        ? "上游 WiFi"
        : "下游 WiFi";
    }
    return `${interfaceType} (${type.toUpperCase()})`;
  };

  return (
    <div className="h-full flex justify-start items-center">
      <div className="text-base font-bold">硬件接口状态：</div>
      {interfaceInformation?.map((item) => {
        return (
          <div
            className={cn(
              "ml-4  px-2.5 h-[44px] flex justify-center items-center rounded relative cursor-pointer",
              wanState(item),
            )}
            key={item.interface}
          >
            <span className=" text-base">{interfaceText(item)}</span>
            <div className="absolute left-[2px] bottom-[2px] border w-[16px] h-[10px]  wan-box " />
          </div>
        );
      })}
    </div>
  );
};
