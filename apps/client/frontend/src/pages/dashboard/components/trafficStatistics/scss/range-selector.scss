.range-selector {
    position: relative;
    width: 80%;
    height: 24px;
    background-color: #e0e0e0;
    border-radius: 12px;
    margin: 20px auto;

    .track {
        position: absolute;
        top: 50%;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: #ccc;
        transform: translateY(-50%);
    }

    .range {
        position: absolute;
        top: 50%;
        left: 20%;
        right: 20%;
        height: 4px;
        background-color: #1E3A8A;
        transform: translateY(-50%);
    }

    .handle {
        position: absolute;
        top: 50%;
        width: 12px;
        height: 12px;
        background-color: #fff;
        border: 2px solid #1E3A8A;
        border-radius: 50%;
        cursor: pointer;
        transform: translateY(-50%);
    }

    .handle-left {
        left: 20%;
    }

    .handle-right {
        right: 20%;
    }
}
