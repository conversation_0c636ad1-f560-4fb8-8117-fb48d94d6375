import { useState, useRef, useEffect } from 'react'
import '../scss/range-selector.scss'

interface RangeSelectorProps {
  onRangeChange: (range: { start: number; end: number }) => void
}

const RangeSelector = ({ onRangeChange }: RangeSelectorProps) => {
  const rangeSelectorRef = useRef<HTMLDivElement | null>(null)
  const rangeRef = useRef<HTMLDivElement | null>(null)
  const handleLeftRef = useRef<HTMLDivElement | null>(null)
  const handleRightRef = useRef<HTMLDivElement | null>(null)

  const [isDraggingLeft, setIsDraggingLeft] = useState(false)
  const [isDraggingRight, setIsDraggingRight] = useState(false)

  const handleMouseDownLeft = () => {
    setIsDraggingLeft(true)
  }

  const handleMouseDownRight = () => {
    setIsDraggingRight(true)
  }

  const handleMouseUp = () => {
    setIsDraggingLeft(false)
    setIsDraggingRight(false)
  }

  const handleMouseMove = (event: MouseEvent) => {
    if (
      !rangeSelectorRef.current ||
      !handleRightRef.current ||
      !handleLeftRef.current ||
      !rangeRef.current
    )
      return
    if (isDraggingLeft || isDraggingRight) {
      console.log('handleMouseMove', isDraggingLeft, isDraggingRight)
      const rect = rangeSelectorRef.current.getBoundingClientRect()
      const offsetX = event.clientX - rect.left
      const percentage = Math.max(
        0,
        Math.min(100, (offsetX / rect.width) * 100),
      )

      if (isDraggingLeft) {
        const rightPercentage = parseFloat(handleRightRef.current.style.right)
        if (percentage < 100 - rightPercentage) {
          handleLeftRef.current.style.left = `${percentage}%`
          rangeRef.current.style.left = `${percentage}%`
          onRangeChange({ start: percentage, end: 100 - rightPercentage })
        }
      }

      if (isDraggingRight) {
        const leftPercentage = parseFloat(handleLeftRef.current.style.left)
        if (percentage > leftPercentage) {
          handleRightRef.current.style.right = `${100 - percentage}%`
          rangeRef.current.style.right = `${100 - percentage}%`
          onRangeChange({ start: leftPercentage, end: percentage })
        }
      }
    }
  }

  useEffect(() => {
    document.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('mousemove', handleMouseMove)

    return () => {
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('mousemove', handleMouseMove)
    }
  }, [isDraggingLeft, isDraggingRight])

  return (
    <div className="range-selector" ref={rangeSelectorRef}>
      <div className="track"></div>
      <div className="range" ref={rangeRef}></div>
      <div
        className="handle handle-left"
        ref={handleLeftRef}
        onMouseDown={handleMouseDownLeft}
      ></div>
      <div
        className="handle handle-right"
        ref={handleRightRef}
        onMouseDown={handleMouseDownRight}
      ></div>
    </div>
  )
}

export default RangeSelector
