import React, { useEffect, useMemo, useRef } from 'react'
import * as echarts from 'echarts'
// import 'echarts-gl';
import type { EChartsType, EChartsCoreOption } from 'echarts'
import { tabTrafficMap } from '../data'

import { useTrafficRealTimeContext } from '@/provider/TrafficRealTimeContextProvider'

import { dateFormat } from '@/lib/utils'
import { useEchartsOption } from '../hooks/useEchartsOption'
import { HistoricalTraffic } from '@/pages/dashboard/types/proxy'

import MinusCircleSvg from '@/assets/svg/nodeLine/minus_circle_outline.svg'
import PlusCircleSvg from '@/assets/svg/nodeLine/plus_circle_outline.svg'

import '../scss/summary-graph.scss'

interface SummaryGraphProp {
  type: string
  activeType: string
  historicalTraffic: HistoricalTraffic | undefined
}

interface DataZoomType {
  start: number
  startValue: number
  end: number
  endValue: number
}

const initData = {
  categories: [],
  uploadData: [],
  downloadData: [],
}

export const SummaryGraph = React.memo(
  ({ type, activeType, historicalTraffic }: SummaryGraphProp) => {
    const lineChart = React.useRef<EChartsType | null>(null)

    const [option, updateOption] = useEchartsOption(initData)
    const { realTimeTrafficData } = useTrafficRealTimeContext()

    const isActive = useMemo(() => activeType === type, [activeType, type])
    const isRealTime = useMemo(() => type === tabTrafficMap.REAL_TIME, [type])

    const initLineChartOption = (option: EChartsCoreOption | null) => {
      if (!option || !lineChart.current) return
      const eOption = JSON.parse(JSON.stringify(option)) as EChartsCoreOption
      if (type === tabTrafficMap.REAL_TIME) {
        ;(eOption['grid'] as { bottom: number }).bottom = 48
        eOption['dataZoom'] = {
          show: false,
          startValue: 0,
          endValue: 29,
        }
      }

      lineChart.current.setOption(eOption)
    }

    const handleZoom = (type: string) => {
      if (!lineChart.current) return
      const dataZoom = lineChart.current.getOption()[
        'dataZoom'
      ] as Array<DataZoomType>
      const { end } = dataZoom[0] as DataZoomType
      lineChart.current?.dispatchAction({
        type: 'dataZoom',
        start: 0,
        end: type === 'add' ? end + 5 : end - 5 < 5 ? 5 : end - 5,
      })
    }

    const updateLineChartOption = ({
      categories,
      upload,
      download,
    }: {
      categories: string[]
      upload: (number | string)[]
      download: (number | string)[]
    }) => {
      const allData = [...upload, ...download].filter(
        (index) => index !== '',
      ) as number[]
      const MaxData = allData.length ? Math.max(...allData) : 0

      let factor = 1
      if (MaxData >= 1024 ** 2) {
        // 大于等于 1 GB
        factor = 3
      } else if (MaxData >= 1024) {
        // 大于等于 1 MB
        factor = 2
      }

      function getUnit(factor: number) {
        return factor === 3 ? 'GB' : factor === 2 ? 'MB' : 'KB'
      }

      const conversion = 1024 ** (factor - 1) || 1 // 换算值，转为GB/MB/KB
      const uploadData = upload.map((value) =>
        value === '' ? 0 : ((value as number) / conversion).toFixed(factor * 4),
      )
      const downloadData = download.map((value) =>
        value === '' ? 0 : ((value as number) / conversion).toFixed(factor * 4),
      )

      updateOption({ categories, uploadData, downloadData })
      lineChart.current?.setOption({
        tooltip: {
          valueFormatter: (value: number) => {
            const number_ = Number(value)
            function formatter(number_: number, factor: number): string {
              if (number_ < 1) {
                if (factor === 3) {
                  // 当前单位是 GB
                  return number_ * 1024 < 1
                    ? formatter(number_ * 1024, 2)
                    : `${(number_ * 1024).toFixed(2)} MB`
                } else if (factor === 2) {
                  // 当前单位是 MB
                  return `${(number_ * 1024).toFixed(2)} KB`
                }
              }
              return `${number_.toFixed(2)} ${getUnit(factor)}`
            }
            return `${formatter(number_, factor)}`
          },
        },
        xAxis: [{ data: categories }],
        yAxis: [
          {
            unit: getUnit(factor),
            axisLabel: {
              formatter: (value: number) => `${value}${getUnit(factor)}`,
            },
          },
        ],
        series: [
          { data: uploadData },
          { data: downloadData },
          { data: uploadData },
          { data: downloadData },
        ],
      })
    }

    const getRealTimeTrafficData = () => {
      if (!isActive || !isRealTime) return
      const categories = realTimeTrafficData.categories.slice(-30)
      const upload = realTimeTrafficData.upload.slice(-30)
      const download = realTimeTrafficData.download.slice(-30)

      updateLineChartOption({ categories, upload, download })
    }

    const timer = useRef<NodeJS.Timeout | null>(null)
    useEffect(() => {
      getRealTimeTrafficData()
      timer.current && clearTimeout(timer.current)
      timer.current = setTimeout(() => {
        lineChart.current?.dispatchAction({
          type: 'downplay',
        })
        lineChart.current?.dispatchAction({
          type: 'hideTip',
        })

        timer.current && clearTimeout(timer.current)
      }, 300)
    }, [isActive, isRealTime, realTimeTrafficData])

    const getNormalData = () => {
      if (!isActive || isRealTime || !historicalTraffic) {
        console.log('getNormalData early return:', { isActive, isRealTime, historicalTraffic: !!historicalTraffic })
        return
      }
      
      console.log('getNormalData processing type:', type)
      console.log('historicalTraffic keys:', Object.keys(historicalTraffic))
      
      const data = historicalTraffic[type as keyof HistoricalTraffic]
      console.log('Raw data for type', type, ':', data)
      
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('No data found for type:', type)
        return
      }
      
      const categories: string[] = [],
        upload: number[] = [],
        download: number[] = []
      
      // 按时间顺序排序数据（从旧到新）
      const sortedData = [...data].sort((a, b) => new Date(a.Date).getTime() - new Date(b.Date).getTime())
      console.log('Sorted data:', sortedData)
      
      sortedData.forEach((item) => {
        // 使用更清晰的日期格式
        categories.push(dateFormat(item.Date, 'MM-DD'))
        // 保持原始字节数据，让updateLineChartOption内部处理单位转换
        upload.push(item.Up)
        download.push(item.Down)
      })
      
      console.log('Processed data:', { categories, upload, download })
      updateLineChartOption({ categories, upload, download })
    }

    useEffect(() => {
      getNormalData()
    }, [isActive, isRealTime, type, historicalTraffic])

    const handleResize = () => {
      lineChart.current?.resize()
    }

    useEffect(() => {
      if (!isActive) return
      handleResize()
    }, [isActive])

    useEffect(() => {
      const chartDom = document.getElementById(`traffic${type}Chart`)
      lineChart.current = echarts.init(chartDom)

      // getData()

      initLineChartOption(option)

      getNormalData()
      getRealTimeTrafficData()
      // 页面resize时触发
      window.addEventListener('resize', handleResize)
      return () => {
        window.removeEventListener('resize', handleResize)
        lineChart.current?.dispose()
        lineChart.current = null
      }
    }, [])

    return (
      <div className="summary-graph relative flex-1 w-full flex flex-col overflow-hidden ">
        <div id={`traffic${type}Chart`} className="flex-1"></div>
        {!isRealTime && (
          <div className="absolute right-0 bottom-[8px]  flex space-x-[2px]">
            <div
              className="rounded-sm border border-[#F0F0F0] bg-white py-1 px-[9px] cursor-pointer"
              onClick={() => handleZoom('reduce')}
            >
              <img src={MinusCircleSvg} alt="" className="w-4 h-4" />
            </div>
            <div
              className="rounded-sm border border-[#F0F0F0] bg-white py-1 px-[9px] cursor-pointer"
              onClick={() => handleZoom('add')}
            >
              <img src={PlusCircleSvg} alt="" />
            </div>
          </div>
        )}
      </div>
    )
  },
)
