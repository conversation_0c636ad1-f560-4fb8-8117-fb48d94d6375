import { useState, useMemo } from 'react'
import Actions from '@/assets/svg/Actions.svg?react'

import { CardTitle } from '@/components/CardTitle'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'

import { SummaryGraph } from './summary-graph'
import { tabTrafficMap } from './data'

import { useGlobalInfoContext } from '@/provider/GlobalInfoContextProvider'

const tabTrafficList = [
  {
    key: tabTrafficMap.SIX_MONTHS,
    value: '近半年',
  },
  {
    key: tabTrafficMap.LAST_MONTH,
    value: '近30天',
  },
  // {
  //     key: "account2",
  //     value: '近30天'
  // },
  {
    key: tabTrafficMap.LAST_7_DAYS,
    value: '近7天',
  },
  {
    key: tabTrafficMap.REAL_TIME,
    value: '实时',
  },
]

export const TrafficStatistics = () => {
  const { globalInfo } = useGlobalInfoContext()

  const historicalTraffic = useMemo(
    () => globalInfo?.data.historical_traffic,
    [globalInfo],
  )

  const [tabChoose, setTabChoose] = useState(tabTrafficMap.REAL_TIME)

  return (
    <CardTitle
      title="系统流量统计"
      toolbar={<Actions />}
      className="flex flex-col h-full"
      showBorderBottom={false}
    >
      <Tabs defaultValue={tabChoose} className="w-full flex-1 flex flex-col">
        <TabsList className="w-full flex justify-start bg-[#F4F4F5]">
          {tabTrafficList.map((item) => (
            <TabsTrigger
              key={item.key}
              value={item.key}
              onClick={() => {
                setTabChoose(item.key)
              }}
            >
              {item.value}
            </TabsTrigger>
          ))}
        </TabsList>
        <div className="w-full flex-1 border-t border-t-[#E4E4E7] mt-5 overflow-hidden">
          {tabTrafficList.map((item) => (
            <TabsContent
              key={item.key}
              value={item.key}
              className={` ${tabChoose === item.key ? 'block w-full h-full' : 'hidden h-0 m-0'}  flex`}
            >
              <SummaryGraph
                type={item.key}
                activeType={tabChoose}
                historicalTraffic={historicalTraffic}
              />
            </TabsContent>
          ))}
        </div>
      </Tabs>
    </CardTitle>
  )
}
