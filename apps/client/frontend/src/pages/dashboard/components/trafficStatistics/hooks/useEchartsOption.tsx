import { useState } from 'react'
import type { EChartsCoreOption } from 'echarts'

export interface EchartsDataType {
  categories: string[]
  uploadData: (number | string)[]
  downloadData: (number | string)[]
}

const getOption = (data: EchartsDataType) => {
  const { categories, uploadData, downloadData } = data

  return {
    animation: true,
    animationDuration: 1000,
    // animationEasing: 'exponentialOut',
    animationDurationUpdate: 900,
    grid: {
      left: 60,
      right: '0',
      top: 20,
      // bottom: 80,
      bottom: 50,
      containLabel: false,
    },
    legend: {
      show: false,
    },

    tooltip: {
      trigger: 'axis',
      position: 'top',
      padding: 8,
      borderWidth: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      formatter: function (params: any[]) {
        if (!params || params.length === 0) return '';
        
        const date = params[0].axisValue;
        let content = `<div style="font-weight: 600; margin-bottom: 4px;">${date}</div>`;
        
        // 只显示线型图的数据（上传和下载），忽略柱状图
        const lineData = params.filter(param => param.seriesType === 'line');
        
        lineData.forEach(param => {
          const value = typeof param.data === 'number' ? param.data.toFixed(2) : param.data;
          content += `<div style="display: flex; align-items: center; margin: 2px 0;">
            <span style="display: inline-block; width: 8px; height: 8px; background-color: ${param.color}; border-radius: 50%; margin-right: 6px;"></span>
            <span>${param.seriesName}: ${value} MB</span>
          </div>`;
        });
        
        return content;
      }
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        xAxisIndex: [0],
        backgroundColor: '#F3F4F6',
        dataBackground: {
          lineStyle: {
            color: '#F3F4F6',
          },
          areaStyle: {
            color: '#F3F4F6',
          },
        },
        selectedDataBackground: {
          lineStyle: {
            color: '#1E3A8A',
            shadowBlur: 0,
          },
          areaStyle: {
            color: '#1E3A8A',
            shadowBlur: 0,
          },
        },
        fillerColor: '#1E3A8A',
        borderColor: 'transparent',
        borderRadius: 10,
        handleIcon:
          'image://data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdmlld0JveD0iMCAwIDEyIDIwIiBmaWxsPSJub25lIj4KICA8cmVjdCB4PSIzLjExNTIiIHk9IjAuNSIgd2lkdGg9IjkuNjIwNTYiIGhlaWdodD0iMTkiIHJ4PSI0LjgxMDI4IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSIjQ0VDRUNFIi8+CiAgPGxpbmUgeDE9IjUuNzcwMTQiIHkxPSI3LjUiIHgyPSIxMC4wODA0MiIgeTI9IjcuNSIgc3Ryb2tlPSIjRjBGMEYwIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KICA8bGluZSB4MT0iNS43NzAxNCIgeTE9IjExLjUiIHgyPSIxMC4wODA0MiIgeTI9IjExLjUiIHN0cm9rZT0iI0YwRjBGMCIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPg==',
        handleSize: 20,
        handleStyle: {
          shadowBlur: 0,
          color: '#fff',
          borderColor: '#1E3A8A',
        },
        moveHandleStyle: {
          color: 'trsnsparent',
          borderColor: '#F3F4F6',
          borderWidth: 0,
        },
        brushStyle: {
          borderWidth: 0,
        },
        showDetail: false,
        showDataShadow: false,
        start: 0,
        end: 100,
        // end: categories.length > 30 ? 100 * (30 / categories.length) : 100, // 初始显示数量比例
        left: 0,
        right: 86,
        height: 8,
        bottom: 15,
        emphasis: {
          handleStyle: {
            shadowBlur: 0,
          },
        },
      },
    ],
    xAxis: [
      {
        type: 'category',
        boundaryGap: [0.2, 0.2],
        data: categories ?? [],
        axisLabel: {
          color: '#999',
          align: 'center',
          margin: 0,
          // padding: [10, 0, 10, 0],
          // height: 48,
          // lineHeight: 34
          padding: [5, 0, 5, 0],
          height: 30,
          lineHeight: 30,
          // formatter: '{MM}/{dd}'
        },
        axisLine: {
          lineStyle: {
            color: '#E8E8E8',
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        scale: true,
        min: 0,
        boundaryGap: [0.2, 0.2],
        splitNumber: 4,
        splitLine: {
          show: true, // 显示分割线
          lineStyle: {
            color: '#E8E8E8',
            type: 'dashed',
          },
        },
        axisLabel: {
          color: '#999',
          formatter: '{value} MB',
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
    ],
    series: [
      {
        name: '上传Bar',
        type: 'bar',
        data: uploadData ?? [],
        itemStyle: {
          color: '#F5F5F5',
        },
        emphasis: {
          itemStyle: {
            color: '#F5F5F5',
          },
        },
        tooltip: {
          show: false,
        },
        barWidth: '40%',
        barCategoryGap: '0%',
      },
      {
        name: '下载Bar',
        type: 'bar',
        data: downloadData ?? [],
        itemStyle: {
          color: '#F5F5F5',
        },
        emphasis: {
          itemStyle: {
            color: '#F5F5F5',
          },
        },
        tooltip: {
          show: false,
        },
        barWidth: '40%',
        barCategoryGap: '0%',
      },
      {
        name: '上传',
        type: 'line',
        data: uploadData ?? [],
        symbol: 'circle',
        symbolSize: 8,
        smooth: 0.35,
        // hoverAnimation: true, // 启用悬停动画
        lineStyle: {
          color: '#2563EB',
          width: 4,
          shadowBlur: 20,
          shadowColor: 'rgba(60, 143, 255, 0.3)',
          shadowOffsetX: 0,
          shadowOffsetY: -4,
        },
        itemStyle: {
          show: false,
          opacity: 0, // 默认不显示符号
          color: '#2563EB',
          borderColor: 'white',
          borderWidth: 2,
          borderType: 'solid',
          shadowColor: 'rgba(0, 0, 0, 0.10)',
          shadowBlur: 4,
          shadowOffsetX: 0,
          shadowOffsetY: 4,
        },
        emphasis: {
          itemStyle: {
            show: true,
            opacity: 1, // 鼠标悬停时显示符号
          },
        },
      },
      {
        name: '下载',
        type: 'line',
        data: downloadData ?? [],
        smooth: 0.35,
        symbol: 'circle',
        symbolSize: 8,
        // hoverAnimation: true, // 启用悬停动画
        lineStyle: {
          color: '#059669',
          width: 4,
          shadowBlur: 20,
          shadowColor: 'rgba(79, 222, 77, 0.3)',
          shadowOffsetX: 0,
          shadowOffsetY: -4,
        },
        itemStyle: {
          opacity: 0, // 默认不显示符号
          color: '#059669',
          borderColor: 'white',
          borderWidth: 2,
          borderType: 'solid',
          shadowColor: 'rgba(0, 0, 0, 0.10)',
          shadowBlur: 4,
          shadowOffsetX: 0,
          shadowOffsetY: 4,
        },
        emphasis: {
          itemStyle: {
            opacity: 1, // 鼠标悬停时显示符号
          },
        },
      },
    ],
    barCategoryGap: '0%',
  }
}

export const useEchartsOption = (
  data: EchartsDataType,
): [EChartsCoreOption | null, (newData: EchartsDataType) => void] => {
  const [option, setOption] = useState<EChartsCoreOption | null>(
    getOption(data),
  )

  const updateOption = (data: EchartsDataType) => {
    setOption(getOption(data))
  }

  return [option, updateOption]
}
