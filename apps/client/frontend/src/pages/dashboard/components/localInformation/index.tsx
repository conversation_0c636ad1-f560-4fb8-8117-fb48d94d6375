import { CardTitle } from "../../../../components/CardTitle";
import HasNetwork from "@/assets/svg/FeaturedOutline.svg?react";
import NetOutline from "@/assets/svg/netOutline.svg?react";
import HasEquipment from "@/assets/svg/Featured icon outline.svg?react";
import { DeviceInfo } from "../../types/proxy";
import { NETWORK_STATUS } from "../../data/config";
import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";
import { useEffect, useMemo, useState } from "react";
import { cn } from "@/lib/utils";

const INTERFACE_NAME = "interface_name";
const INTERFACE_TYPE = {
  eth0: "有线",
  wwan0: "5G/4G",
  wlan0: "Wi-Fi",
  enp10s0: "有线",
};

const INTERFACE_TYPE_IS_WIFI = [
  {
    key: ["eth", "ens", "enp", "eno", "enp10s0"],
    value: "有线",
  },
];

const InformationItem = ({
  value,
  label,
  className,
}: {
  value: string | number | undefined;
  label: string | number;
  className?: string;
}) => (
  <div
    className={cn(
      "text-[#000000] font-semibold flex flex-col space-y-[6px]",
      className,
    )}
  >
    <p className="leading-[22px]">{value}</p>
    <span className="font-normal text-xs leading-4.5">{label}</span>
  </div>
);

export const LocalInformation = () => {
  const { globalInfo } = useGlobalInfoContext();

  const deviceInformation = useMemo(
    () => globalInfo?.data.device_information,
    [globalInfo],
  );
  const deviceNumber = useMemo(() => globalInfo?.data.devices_num, [
    globalInfo,
  ]);
  const wifiDisable = useMemo(() => {
    return !globalInfo?.data.flags.includes("wifi_disable");
  }, [globalInfo]);
  const [updateTime, setUpdateTime] = useState(deviceInformation?.uptime ?? 0);

  // 秒数转年月时分
  function formatDuration(seconds: number) {
    // 计算天数、小时数、分钟数和秒数
    const days = Math.floor(seconds / (24 * 60 * 60));
    seconds %= 24 * 60 * 60;
    const hours = Math.floor(seconds / (60 * 60));
    seconds %= 60 * 60;
    const minutes = Math.floor(seconds / 60);
    seconds %= 60;

    // 根据各个单位是否为 0 来构建输出字符串
    let result = "";
    if (days > 0) result += `${days}天`;
    if (hours > 0) result += `${hours}小时`;
    if (days > 0) return result;
    if (minutes > 0) result += `${minutes}分钟`;
    if (hours > 0) return result;
    result += `${seconds}秒`;

    return result;
  }

  useEffect(() => {
    const intervalId = setInterval(() => {
      setUpdateTime((previousUptime) => previousUptime + 1);
    }, 1000);

    return () => clearInterval(intervalId);
  }, []);
  const texts = {
    networkInterface: "入网方式",
    ip: "系统 IP 地址",
    you_ip: "当前设备 IP",
    mac: "系统 MAC 地址",
    gateway: "上游网关",
    connectedDevices: "活跃设备",
  };

  const textsWIfi = {
    connectedDevices: "活跃设备",
    networkInterface: "入网方式",
    you_ip: "设备 IP",
    mac: "系统 MAC 地址",
    ip: "网关 IP 地址",
    upstreamNetworkConnectionMode: "上游网络连接模式",
    gateway: "上游网关",
  };

  const informationItems = [
    { key: INTERFACE_NAME, label: texts.networkInterface },
    { key: "ip", label: texts.ip },
    { key: "you_ip", label: texts.you_ip, defaultValue: "-" },
    { key: "mac", label: texts.mac },
    { key: "gateway", label: texts.gateway },
  ];

  const informationWifiItems = [
    { key: "mac", label: textsWIfi.mac },
    { key: "ip", label: textsWIfi.ip },
    {
      key: INTERFACE_NAME,
      label: textsWIfi.upstreamNetworkConnectionMode,
      defaultValue: "",
    },
    { key: "you_ip", label: textsWIfi.you_ip, defaultValue: "" },
  ];

  const informationList = useMemo(() => {
    const list = wifiDisable ? informationWifiItems : informationItems;
    return list;
  }, [wifiDisable]);

  return (
    <div className="cardContainer">
      <CardTitle title="系统信息" />
      <div className="flex justify-between">
        <div
          className="roundCardType flex items-center p-4 "
          style={{ width: "calc(50% - 8px)" }}
        >
          {deviceInformation?.network_status === "up"
            ? (
              <>
                <HasNetwork className="w-[46px] h-[46px] mr-1 -ml-2 shrink-0" />
                <InformationItem
                  value={NETWORK_STATUS[
                    deviceInformation?.network_status ?? ""
                  ]}
                  label={formatDuration(updateTime)}
                />
              </>
            )
            : <NetOutline className="w-[46px] h-[46px] mr-1 shrink-0" />}
        </div>
        <div
          className="roundCardType flex items-center p-4 "
          style={{ width: "calc(50% - 8px)" }}
        >
          <HasEquipment className="w-[46px] h-[46px] mr-1 -ml-2" />
          <InformationItem
            value={deviceNumber}
            label={texts.connectedDevices}
          />
        </div>
      </div>
      <div
        className={cn(
          "roundCardType mt-2.5 custom-1920:mt-5 p-3.5 custom-1920:p-4 flex-1 grid grid-cols-2 content-between",
        )}
      >
        {informationList.map((item) => {
          let value = deviceInformation?.[item.key as keyof DeviceInfo] ||
            item.defaultValue;
          if (item.key === INTERFACE_NAME) {
            if (wifiDisable) {
              value = INTERFACE_TYPE_IS_WIFI.find((type) =>
                type.key.some((v) => value && (value as string).includes(v))
              )?.value || value;
            } else {
              value = INTERFACE_TYPE[value as keyof typeof INTERFACE_TYPE];
            }
          }
          return (
            <InformationItem
              key={item.key}
              value={value}
              label={item.label}
              className="space-y-1 custom-1920:space-y-1.5"
            />
          );
        })}
      </div>
    </div>
  );
};
