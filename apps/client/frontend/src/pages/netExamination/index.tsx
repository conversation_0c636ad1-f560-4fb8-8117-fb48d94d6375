import { BaseContent } from '@/components/BaseContent'
import { Button } from '@/components/ui/button'
import { cardListData } from './data/cardList'
import { useState } from 'react'
import { NetTestCard } from './components/netTestCard'
import { testSpeed } from '@/lib/utils'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import './index.scss'
import { PROXY_STATUS_KEY } from '@/data'
export interface CardListType {
  id: number
  icon: React.ReactNode
  url: string
  status: string | number | null
  loading: boolean
}

export const NetExamination = () => {
  const [netData, setNetData] = useState(cardListData)

  const netStatus = useNetworkStatus().isOnline
  const updateStatus = (
    itemId: number,
    loading: boolean,
    status?: string | number,
  ) => {
    setNetData((previousData) =>
      previousData.map((item) => {
        if (item.id === itemId) {
          return status ? { ...item, loading, status } : { ...item, loading }
        }
        return item
      }),
    )
  }

  const netTestFactory = async (currentCard: CardListType) => {
    try {
      updateStatus(currentCard.id, true)
      const res = await testSpeed(currentCard.url)
      updateStatus(
        currentCard.id,
        false,
        res.speed > 0 && netStatus ? res.speed : PROXY_STATUS_KEY.TIMEOUT,
      )
    } catch (error) {
      console.error('Error while pinging:', error)
    }
  }

  const testAll = async () => {
    try {
      netData.map(async (item) => {
        await netTestFactory(item)
      })
    } catch (error) {
      console.error('Error while pinging:', error)
    }
  }

  return (
    <BaseContent
      title="网络测试"
      toolbar={
        <Button variant="link" onClick={() => testAll()}>
          测试全部
        </Button>
      }
    >
      <div className="flex">
        {netData.map((item) => {
          return (
            <NetTestCard
              key={item.id}
              cardMsg={item}
              loading={item.loading}
              netTestFactory={netTestFactory}
            />
          )
        })}
      </div>
    </BaseContent>
  )
}
