import { Button } from '@/components/ui/button'
import NetTest from '@/assets/svg/netTest.svg?react'
import LoadIcon from '@/assets/svg/loadIcon.svg?react'
import { CardListType } from '../..'
import { PROXY_QUALITY_COLOR, PROXY_QUALITY_TYPE } from '@/data'

export const NetTestCard = ({
  cardMsg,
  loading,
  netTestFactory,
}: {
  cardMsg: CardListType
  loading: boolean
  netTestFactory: (data: CardListType) => void
}) => {
  const handleTest = async () => {
    if (loading) return
    await netTestFactory(cardMsg)
  }

  const stateType = typeof cardMsg.status === 'string'
  return (
    <div
      className="w-[320px] h-[144px] p-[18px] flex flex-col items-center border border-[#E4E4E7] hover:border-[#1E3A8A] rounded-md mr-5 netExamination-shadow"
      key={cardMsg.id}
    >
      {cardMsg.icon}
      <div className="mt-[18px] pt-[18px] border-t border-[#E5E7EB] w-full flex items-center justify-center">
        {cardMsg.status ? (
          <Button
            id={'speed-test-btn-after' + cardMsg.id}
            className="w-[116px]  bg-transparent font-medium cursor-pointer hover:bg-[#DBEAFE]"
            style={{
              color: stateType
                ? PROXY_QUALITY_COLOR[cardMsg.status]
                : '#1E3A8A',
            }}
            onClick={handleTest}
          >
            {loading ? <LoadIcon className="mr-2 animate-spin" /> : undefined}
            {stateType
              ? PROXY_QUALITY_TYPE[cardMsg.status]
              : cardMsg.status + 'ms'}
          </Button>
        ) : (
          <Button
            id={'speed-test-btn' + cardMsg.id}
            className="w-[116px]  bg-[#DBEAFE] text-[c] font-medium"
            onClick={handleTest}
          >
            {loading ? (
              <LoadIcon className="mr-2 animate-spin" />
            ) : (
              <NetTest className="mr-2" />
            )}
            测试
          </Button>
        )}
      </div>
    </div>
  )
}
