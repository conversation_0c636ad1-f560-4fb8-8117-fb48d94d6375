import NetTestBD from '@/assets/svg/netTest1.svg?react'
import NetTestGoog from '@/assets/svg/netTest2.svg?react'
import NetTestGit from '@/assets/svg/netTest3.svg?react'
import { CardListType } from '..'

export const cardListData: CardListType[] = [
  {
    id: 1,
    icon: <NetTestBD />,
    url: 'https://apps.bdimg.com/favicon.ico',
    status: null,
    loading: false,
  },
  {
    id: 2,
    icon: <NetTestGoog />,
    url: 'https://www.gstatic.com/glue/cookienotificationbar/config/2b.json',
    status: null,
    loading: false,
  },
  {
    id: 3,
    icon: <NetTestGit />,
    url: 'https://github.githubassets.com/favicon.ico',
    status: null,
    loading: false,
  },
]
