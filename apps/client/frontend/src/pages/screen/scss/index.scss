.text-shadow {
    text-shadow: 0px 5px 8px rgba(3, 140, 255, 0.30);
}

.big-screen {
    scrollbar-width: none;

    ::-webkit-scrollbar {
        width: 0;
        height: 0;
    }

    .screen-big-title {
        background: linear-gradient(270deg, rgba(14, 15, 18, 0.00) -0.82%, #0E0F12 84.87%);
    }

    // .aside-custom {
    //     width: 430px;
    // }

    // @media (min-width: 2000px) {
    //     .aside-custom {
    //         width: 489px;
    //     }
    // }

    .screen-header {
        &__right {
            &_bg {
                background-image: url('@/assets/image/screen/header-right-bg-sm.png');
                background-repeat: no-repeat;
                background-position: left 54px;
                background-size: 337px 35px;
                pointer-events: none;
            }
        }
    }

    @media (min-width: 1920px) {
        .screen-header {
            &__right {
                &_bg {
                    background-image: url('@/assets/image/screen/header-right-bg-md.png');
                    background-repeat: no-repeat;
                    background-position: left 54px;
                    background-size: 458px 35px;
                }
            }
        }
    }

    @media (min-width: 2439px) {
        .screen-header {
            &__right {
                &_bg {
                    background-image: url('@/assets/image/screen/header-right-bg-lg.png');
                    background-repeat: no-repeat;
                    background-position: left 54px;
                    background-size: 570px 35px;
                }
            }
        }
    }

    .proxy-overview {
        .online-devices {
            .device-progress {
                width: var(--width);
                background-image: url('@/assets/svg/screen/proxy-overview/device-progress.svg');
                background-position: center;
                background-repeat: repeat;
            }
        }
    }

    .line-map {
        background-color: #0f0e0e;
        /* 背景颜色 */
        background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
        background-size: 212px 120px;
        background-position: -1px 60px;
    }
}
