import { z } from "zod";
import { ApiResponseSchema } from "@/api/commonProxy";
import { ProxyInfoSchema } from "@/pages/dashboard/types/proxy";

const pathListSchema = z.object({
  name: z.string(),
  proxies: z.array(z.string()),
  proxies_code: z.array(z.string()),
  use: z.boolean(),
  current_ip_use: z.boolean(),
  change_time: z.number().optional(),
  change_country_array: z.array(z.array(z.string())).nullable().optional(),
});

const proxiesDataSchema = z.object({
  CityName: z.string(),
  CountryCode: z.string(),
  CountryName: z.string(),
  CreatedAt: z.number(),
  Delay: z.number(),
  Name: z.string(),
  Port: z.number(),
  Protocol: z.string(),
  Raw: z.union([z.string(), z.null()]),
  Server: z.string(),
  Type: z.string(),
  UpdatedAt: z.number(),
  UseStatus: z.string(),
  Config: z.record(z.any()),
  Source: z.string(),
});

export const ProxyRegionBestDelayMapSchema = z.record(z.number());

export const ProxyRegionMapSchema = z.record(z.number());

export const ScreenDataSchema = z.object({
  total_proxy_num: z.number(), //代理总数
  active_proxy_num: z.number(), //活跃代理数
  idle_proxy_num: z.number(), //空闲代理数

  high_quality_proxy_num: z.number(), //高质量代理数
  low_quality_proxy_num: z.number(), //低质量代理数
  mid_quality_proxy_num: z.number(), //中质量代理数
  time_out_proxy_num: z.number(), //超时代理数

  proxy_region_map: ProxyRegionMapSchema, //节点分布总数--代理区域分布
  proxy_region_best_delay_map: ProxyRegionBestDelayMapSchema, //节点数展示--代理区域最快延迟
  path_list: z.array(pathListSchema).optional(), //路径列表 - 地图链路
  proxies_data: z.array(proxiesDataSchema), //代理列表
  proxy_info: ProxyInfoSchema,
  version: z.string(), //版本
});

export type ScreenDataType = z.infer<typeof ScreenDataSchema>;

export const ScreenInfoSchema = ApiResponseSchema(ScreenDataSchema);
export type ScreenInfoType = z.infer<typeof ScreenInfoSchema>;
