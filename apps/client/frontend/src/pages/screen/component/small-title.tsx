import SmallTitleLeftIcon from '@/assets/image/screen/small-title-left.png'
import SmallTitleRightIcon from '@/assets/image/screen/small-title-right.png'
import SmallTitleModeIcon from '@/assets/image/screen/small-title-mode.png'

import OverviewTitleSvg from '@/assets/svg/screen/overview-title.svg?react'
import { cn } from '@/lib/utils'

export const SmallTitle = ({ title }: { title: string }) => {
  return (
    <div
      className={cn(
        ' font-extrabold flex w-full mt-2',
        ' text-[20px] custom-1920:text-[20px] custom-2439:text-[24px]',
      )}
    >
      <img
        src={SmallTitleLeftIcon}
        alt=""
        className="w-6 h-6 custom-1920:w-6.5 custom-1920:h-6.5 custom-2439:w-8 custom-2439:h-8"
      />
      <div className="flex-1 px-1 bg-[#0e0e11] relative">
        <div className="text-shadow -mt-2">{title}</div>
        <img
          src={SmallTitleModeIcon}
          alt=""
          className={cn(
            'w-[81x] h-1.5 absolute right-[14px] top-[50%] translate-y-[-50%]',
            'custom-1920:w-[92px] custom-1920:h-[7px] custom-2439:w-[114px] custom-2439:h-2',
          )}
        />
      </div>
      <img
        src={SmallTitleRightIcon}
        alt=""
        className="w-[40px] h-6 custom-1920:w-[44px] custom-1920:h-[26px] custom-2439:w-[55px] custom-2439:h-8"
      />
    </div>
  )
}

export const BigTitle = ({ title }: { title: string }) => {
  return (
    <div
      className={cn(
        'screen-big-title text-[36px] flex space-x-4.5 px-[6px] items-center rounded-[2px]',
        'text-[25px] custom-1920:text-[29px] custom-2439:text-[36px]',
        ' py-1 custom-2439:py-1.5',
      )}
    >
      <div
        className={cn(
          'w-[56px] h-[56px] flex justify-center items-center border border-[rgba(255,255,255,0.40)] bg-[rgba(1,140,255,0.10)] rounded-[2px] mr-[18px]',
          'w-[40px] h-[40px] custom-1920:w-[45px] custom-1920:h-[45px] custom-2439:w-[56px] custom-2439:h-[56px] ',
        )}
      >
        <OverviewTitleSvg className="w-[17px] h-[17px]  custom-1920:w-[19px] custom-1920:h-[19px] custom-2439:w-6 custom-2439:h-6" />
      </div>
      <div className=" font-[FZZongYi-M05S]">{title}</div>
    </div>
  )
}
