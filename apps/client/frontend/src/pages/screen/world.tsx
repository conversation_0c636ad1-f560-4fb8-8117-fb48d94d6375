// import * as React from "react";
// import * as echarts from 'echarts';
// import 'echarts-gl';
// 引入echarts 世界地图数据

// import IconEm from '@/assets/image/empty.png'

// import worldGeoJson from '@/assets/echarts-map/json/world.json';

// import {
//     createFileRoute,
//     redirect,
//     useRouter,
//     useRouterState,
// } from "@tanstack/react-router";

// export const Route = createFileRoute("/world")({
//     component: WorldComponent,
// });

// 飞行线样式
// const planePath =
//     'path://M1705.06,1318.313v-89.254l-319.9-221.799l0.073-208.063c0.521-84.662-26.629-121.796-63.961-121.491c-37.332-0.305-64.482,36.829-63.961,121.491l0.073,208.063l-319.9,221.799v89.254l330.343-157.288l12.238,241.308l-134.449,92.931l0.531,42.034l175.125-42.917l175.125,42.917l0.531-42.034l-134.449-92.931l12.238-241.308L1705.06,1318.313z';
// // 飞线颜色
// const flyLineColor = "yellow";
// //线条颜色
// const lineColor = "rgba(31,20,252,1)";
// //高亮地图填充色
// const lightColor = "red";
// // 散点图默认颜色
// const ScatterColor = "yellow"
// // 地图默认状态填充色
// const mapAreaColor = "#06265c"
// //字体颜色
// const textColor = "#fff";

// const domImgSrc = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAIBAMAAAA2IaO4AAAABGdBTUEAALGPC/xhBQAAAAFzUkdCAK7OHOkAAAASUExURQgcajHk/SKeySrB5Bx/shNTk3XNSEwAAAAGdFJOU4yMjIyMjE9afYoAAAAJcEhZcwAAAEgAAABIAEbJaz4AAAAlSURBVAjXY2AAA0UhBgYWQUEHBiZBQQMQS4GBwVAYKMEaAFEAACK5Ac0MBL09AAAAAElFTkSuQmCC'

// TODO 页面应该响应式布局
// function WorldComponent() {

//     var domImg = document.createElement('img')
//     // domImg.style.height = domImg.height = domImg.width = domImg.style.width = '2px'
//     domImg.src = IconEm

//     const getOption = () => {

//         // 为防止各个国家写法不兼容，从而采用国家地区编码的方式编写数据。
//         // 国家地理坐标点，取国家中间位置而非国家首都
//         const geoCoordMap: { [key: string]: number[] } = {
//             AD: [1.601554, 42.546245],   // 安道尔
//             AE: [53.847818, 23.424076],   // 阿联酋
//             AF: [67.709953, 33.93911],    // 阿富汗
//             AG: [-61.796428, 17.060816],  // 安提瓜和巴布达
//             AI: [-63.068615, 18.220554],  // 安圭拉
//             AL: [20.168331, 41.153332],    // 阿尔巴尼亚
//             AM: [45.038189, 40.069099],    // 亚美尼亚
//             AO: [17.873887, -11.202692],   // 安哥拉
//             AQ: [-0.071389, -75.250973],   // 南极洲
//             AR: [-63.616672, -38.416097],   // 阿根廷
//             AS: [-170.132217, -14.270972],  // 美属萨摩亚
//             AT: [14.550072, 47.516231],     // 奥地利
//             AU: [133.775136, -25.274398],   // 澳大利亚
//             AW: [-69.968338, 12.52111],     // 阿鲁巴
//             AZ: [47.576927, 40.143105],      // 阿塞拜疆
//             BA: [17.679076, 43.915886],      // 波斯尼亚和黑塞哥维那
//             BB: [-59.543198, 13.193887],     // 巴巴多斯
//             BD: [90.356331, 23.684994],      // 孟加拉国
//             BE: [4.469936, 50.503887],       // 比利时
//             BF: [-1.561593, 12.238333],      // 布基纳法索
//             BG: [25.48583, 42.733883],       // 保加利亚
//             BH: [50.637772, 25.930414],      // 巴林
//             BI: [29.918886, -3.373056],      // 布隆迪
//             BJ: [2.315834, 9.30769],          // 贝宁
//             BM: [-64.75737, 32.321384],       // 百慕大
//             BN: [114.727669, 4.535277],      // 文莱
//             BO: [-63.588653, -16.290154],    // 玻利维亚
//             BR: [-51.92528, -14.235004],     // 巴西
//             BS: [-77.39628, 25.03428],        // 巴哈马
//             BT: [90.433601, 27.514162],       // 不丹
//             BV: [3.413194, -54.423199],       // 布维岛
//             BW: [24.684866, -22.328474],      // 博茨瓦纳
//             BY: [27.953389, 53.709807],       // 白俄罗斯
//             BZ: [-88.49765, 17.189877],       // 伯利兹
//             CA: [-106.346771, 56.130366],     // 加拿大
//             CC: [96.870956, -12.164165],      // 科科斯（基林）岛
//             CD: [21.758664, -4.038333],       // 刚果（金）
//             CF: [20.939444, 6.611111],        // 中非共和国
//             CG: [15.827659, -0.228021],       // 刚果（布）
//             CH: [8.227512, 46.818188],        // 瑞士
//             CI: [-5.54708, 7.539989],         // 科特迪瓦
//             CK: [-159.777671, -21.236736],    // 库克群岛
//             CL: [-71.542969, -35.675147],     // 智利
//             CM: [12.354722, 7.369722],        // 喀麦隆
//             CN: [104.195397, 35.86166],       // 中国
//             CO: [-74.297333, 4.570868],       // 哥伦比亚
//             CR: [-83.753428, 9.748917],       // 哥斯达黎加
//             CU: [-77.781167, 21.521757],      // 古巴
//             CV: [-24.013197, 16.002082],      // 佛得角
//             CX: [105.690449, -10.447525],     // 圣诞岛
//             CY: [33.429859, 35.126413],       // 塞浦路斯
//             CZ: [15.472962, 49.817492],       // 捷克
//             DE: [10.451526, 51.165691],       // 德国
//             DJ: [42.590275, 11.825138],       // 吉布提
//             DK: [9.501785, 56.26392],         // 丹麦
//             DM: [-61.370976, 15.414999],      // 多米尼克
//             DO: [-70.162651, 18.735693],      // 多米尼加共和国
//             DZ: [1.659626, 28.033886],        // 阿尔及利亚
//             EC: [-78.183406, -1.831239],      // 厄瓜多尔
//             EE: [25.013607, 58.595272],       // 爱沙尼亚
//             EG: [30.802498, 26.820553],       // 埃及
//             EH: [-12.885834, 24.215527],      // 西撒哈拉
//             ER: [39.782334, 15.179384],       // 厄立特里亚
//             ES: [-3.74922, 40.463667],        // 西班牙
//             ET: [40.489673, 9.145],           // 埃塞俄比亚
//             FI: [25.748151, 61.92411],        // 芬兰
//             FJ: [179.414413, -16.578193],     // 斐济
//             FM: [150.550812, 7.425554],       // 密克罗尼西亚
//             FO: [-6.911806, 61.892635],       // 法罗群岛
//             FR: [2.213749, 46.227638],        // 法国
//             GA: [11.609444, -0.803689],       // 加蓬
//             GB: [-3.435973, 55.378051],       // 英国
//             GD: [-61.604171, 12.262776],      // 格林纳达
//             GE: [43.356892, 42.315407],       // 格鲁吉亚
//             GF: [-53.125782, 3.933889],       // 法属圭亚那
//             GG: [-2.585278, 49.465691],       // 根西岛
//             GH: [-1.023194, 7.946527],        // 加纳
//             GI: [-5.345374, 36.137741],       // 直布罗陀
//             GL: [-42.604303, 71.706936],      // 格林兰
//             GM: [-15.310139, 13.443182],      // 冈比亚
//             GN: [-9.696645, 9.945587],        // 几内亚
//             GP: [-62.067641, 16.995971],      // 瓜德罗普
//             GQ: [10.267895, 1.650801],        // 赤道几内亚
//             GR: [21.824312, 39.074208],       // 希腊
//             GS: [-36.587909, -54.429579],     // 南乔治亚和南桑威奇群岛
//             GT: [-90.230759, 15.783471],      // 危地马拉
//             GU: [144.793731, 13.444304],      // 关岛
//             GW: [-15.180413, 11.803749],      // 几内亚比绍
//             GY: [-58.93018, 4.860416],        // 圭亚那
//             HK: [114.109497, 22.396428],      // 香港
//             HM: [73.504158, -53.08181],       // 赫德岛和麦克唐纳岛
//             HN: [-86.241905, 15.199999],      // 洪都拉斯
//             HR: [15.2, 45.1],                  // 克罗地亚
//             HT: [-72.285215, 18.971187],      // 海地
//             HU: [19.503304, 47.162494],       // 匈牙利
//             ID: [113.921327, -0.789275],      // 印度尼西亚
//             IE: [-8.24389, 53.41291],         // 爱尔兰
//             IL: [34.851612, 31.046051],       // 以色列
//             IM: [-4.548056, 54.236107],       // 马恩岛
//             IN: [78.96288, 20.593684],         // 印度
//             IO: [71.876519, -6.343194],       // 英属印度洋领地
//             IQ: [43.679291, 33.223191],       // 伊拉克
//             IR: [53.688046, 32.427908],       // 伊朗
//             IS: [-19.020835, 64.963051],      // 冰岛
//             IT: [12.56738, 41.87194],         // 意大利
//             JE: [-2.13125, 49.214439],        // 泽西岛
//             JM: [-77.297508, 18.109581],      // 牙买加
//             JO: [36.238414, 30.585164],       // 约旦
//             JP: [138.252924, 36.204824],      // 日本
//             KE: [37.906193, -0.023559],       // 肯尼亚
//             KG: [74.766098, 41.20438],        // 吉尔吉斯斯坦
//             KH: [104.990963, 12.565679],      // 柬埔寨
//             KI: [-168.734039, -3.370417],     // 基里巴斯
//             KM: [43.872219, -11.875001],      // 科摩罗
//             KN: [-62.782998, 17.357822],      // 圣基茨和尼维斯
//             KP: [127.510093, 40.339852],      // 朝鲜
//             KR: [127.766922, 35.907757],      // 韩国
//             KW: [47.481766, 29.31166],        // 科威特
//             KY: [-80.566956, 19.513469],      // 开曼群岛
//             KZ: [66.923684, 48.019573],       // 哈萨克斯坦
//             LA: [102.495496, 19.85627],       // 老挝
//             LB: [35.862285, 33.854721],       // 黎巴嫩
//             LC: [-60.978893, 13.909444],      // 圣露西亚
//             LI: [9.555373, 47.166],           // 列支敦士登
//             LK: [80.771797, 7.873054],        // 斯里兰卡
//             LR: [-9.429499, 6.428055],        // 利比里亚
//             LS: [28.233608, -29.609988],      // 莱索托
//             LT: [23.881275, 55.169438],       // 立陶宛
//             LU: [6.129583, 49.815273],        // 卢森堡
//             LV: [24.603189, 56.879635],       // 拉脱维亚
//             LY: [17.228331, 26.3351],         // 利比亚
//             MA: [-7.09262, 31.791702],        // 摩洛哥
//             MC: [7.412841, 43.750298],        // 摩纳哥
//             MD: [28.369885, 47.411631],       // 摩尔多瓦
//             ME: [19.37439, 42.708678],        // 黑山
//             MG: [46.869107, -18.766947],      // 马达加斯加
//             MH: [171.184478, 7.131474],       // 马绍尔群岛
//             MK: [21.745275, 41.608635],       // 北马其顿
//             ML: [-3.996166, 17.570692],       // 马里
//             MM: [95.956223, 21.913965],       // 缅甸
//             MN: [103.846656, 46.862496],      // 蒙古
//             MO: [113.543873, 22.198745],      // 澳门
//             MP: [145.38469, 17.33083],        // 北马里亚纳群岛
//             MQ: [-61.024174, 14.641528],      // 马提尼克
//             MR: [-10.940835, 21.00789],       // 毛里塔尼亚
//             MS: [-62.187366, 16.742498],      // 蒙特塞拉特
//             MT: [14.375416, 35.937496],       // 马耳他
//             MU: [57.552152, -20.348404],      // 毛里求斯
//             MV: [73.22068, 3.202778],         // 马尔代夫
//             MW: [34.301525, -13.254308],      // 马拉维
//             MX: [-102.552784, 23.634501],     // 墨西哥
//             MY: [101.975766, 4.210484],       // 马来西亚
//             MZ: [35.529562, -18.665695],      // 莫桑比克
//             NA: [18.49041, -22.95764],        // 纳米比亚
//             NC: [165.618042, -20.904305],     // 新喀里多尼亚
//             NE: [8.081666, 17.607789],        // 尼日尔
//             NF: [167.954712, -29.040835],     // 诺福克岛
//             NG: [8.675277, 9.081999],         // 尼日利亚
//             NI: [-85.207229, 12.865416],      // 尼加拉瓜
//             NL: [5.291266, 52.132633],        // 荷兰
//             NO: [8.468946, 60.472024],        // 挪威
//             NP: [84.124008, 28.394857],       // 尼泊尔
//             NR: [166.931503, -0.522778],      // 瑙鲁
//             NU: [-169.867233, -19.054445],    // 纽埃
//             NZ: [174.885971, -40.900557],     // 新西兰
//             OM: [55.923255, 21.512583],       // 阿曼
//             PA: [-80.782127, 8.537981],       // 巴拿马
//             PE: [-75.015152, -9.189967],      // 秘鲁
//             PF: [-149.406843, -17.679742],    // 法属波利尼西亚
//             PG: [143.95555, -6.314993],       // 巴布亚新几内亚
//             PH: [121.774017, 12.879721],      // 菲律宾
//             PK: [69.345116, 30.375321],       // 巴基斯坦
//             PL: [19.145136, 51.919438],       // 波兰
//             PM: [-56.27111, 46.941936],       // 圣皮埃尔和密克隆
//             PN: [-127.439308, -24.703615],    // 皮特凯恩群岛
//             PR: [-66.590149, 18.220833],      // 波多黎各
//             PS: [35.233154, 31.952162],       // 巴勒斯坦
//             PT: [-8.224454, 39.399872],       // 葡萄牙
//             PW: [134.58252, 7.51498],         // 帕劳
//             PY: [-58.443832, -23.442503],     // 巴拉圭
//             QA: [51.183884, 25.354826],       // 卡塔尔
//             RE: [55.536384, -21.115141],      // 留尼旺
//             RO: [24.96676, 45.943161],        // 罗马尼亚
//             RS: [21.005859, 44.016521],       // 塞尔维亚
//             RU: [105.318756, 61.52401],       // 俄罗斯
//             RW: [29.873888, -1.940278],       // 卢旺达
//             SA: [45.079162, 23.885942],       // 沙特阿拉伯
//             SB: [160.156194, -9.64571],       // 所罗门群岛
//             SC: [55.491977, -4.679574],       // 塞舌尔
//             SD: [30.217636, 12.862807],       // 苏丹
//             SE: [18.643501, 60.128161],       // 瑞典
//             SG: [103.819836, 1.352083],       // 新加坡
//             SH: [-10.030696, -24.143474],     // 圣赫勒拿
//             SI: [14.995463, 46.151241],       // 斯洛文尼亚
//             SJ: [23.670272, 77.553604],       // 斯瓦尔巴和扬马延
//             SK: [19.699024, 48.669026],       // 斯洛伐克
//             SL: [-11.779889, 8.460555],       // 塞拉利昂
//             SM: [12.457777, 43.94236],        // 圣马力诺
//             SN: [-14.452362, 14.497401],      // 塞内加尔
//             SO: [46.199616, 5.152149],        // 索马里
//             SR: [-56.027783, 3.919305],       // 苏里南
//             ST: [6.613081, 0.18636],          // 圣多美和普林西比
//             SV: [-88.89653, 13.794185],       // 萨尔瓦多
//             SY: [38.996815, 34.802075],       // 叙利亚
//             SZ: [31.465866, -26.522503],      // 斯威士兰
//             TC: [-71.797928, 21.694025],      // 特克斯和凯科斯群岛
//             TD: [18.732207, 15.454166],       // 查德
//             TF: [69.348557, -49.280366],      // 法属南部领地
//             TG: [0.824782, 8.619543],         // 多哥
//             TH: [100.992541, 15.870032],      // 泰国
//             TJ: [71.276093, 38.861034],       // 塔吉克斯坦
//             TK: [-171.855881, -8.967363],     // 托克劳
//             TL: [125.727539, -8.874217],      // 东帝汶
//             TM: [59.556278, 38.969719],       // 土库曼斯坦
//             TN: [9.537499, 33.886917],        // 突尼斯
//             TO: [-175.198242, -21.178986],    // 汤加
//             TR: [35.243322, 38.963745],       // 土耳其
//             TT: [-61.222503, 10.691803],      // 特立尼达和多巴哥
//             TV: [177.64933, -7.109535],       // 图瓦卢
//             TW: [120.960515, 23.69781],       // 台湾
//             TZ: [34.888822, -6.369028],       // 坦桑尼亚
//             UA: [31.16558, 48.379433],        // 乌克兰
//             UG: [32.290275, 1.373333],        // 乌干达
//             // UM: ['', ''],                     // 美国本土外小岛屿
//             US: [-95.712891, 37.09024],       // 美国
//             UY: [-55.765835, -32.522779],     // 乌拉圭
//             UZ: [64.585262, 41.377491],       // 乌兹别克斯坦
//             VA: [12.453389, 41.902916],       // 梵蒂冈
//             VC: [-61.287228, 12.984305],      // 圣文森特和格林纳丁斯
//             VE: [-66.58973, 6.42375],         // 委内瑞拉
//             VG: [-64.639968, 18.420695],      // 英属维尔京群岛
//             VI: [-64.896335, 18.335765],      // 美属维尔京群岛
//             VN: [108.277199, 14.058324],      // 越南
//             VU: [166.959158, -15.376706],     // 瓦努阿图
//             WF: [-177.156097, -13.768752],    // 瓦利斯和富图纳
//             WS: [-172.104629, -13.759029],    // 萨摩亚
//             YE: [48.516388, 15.552727],       // 也门
//             YT: [45.166244, -12.8275],        // 马约特
//             ZA: [22.937506, -30.559482],      // 南非
//             ZM: [27.849332, -13.133897],      // 赞比亚
//             ZW: [29.154857, -19.015438],      // 津巴布韦
//         };
//         // 地理映射
//         const nameMap = {
//             China: '中国',
//             Afghanistan: '阿富汗',
//             Angola: '安哥拉',
//             Albania: '阿尔巴尼亚',
//             'United Arab Emirates': '阿拉伯联合酋长国',
//             Argentina: '阿根廷',
//             Armenia: '亚美尼亚',
//             Antarctica: '南极洲',
//             'French Southern and Antarctic Lands': '法国南部和南极地',
//             Australia: '澳大利亚',
//             Austria: '奥地利',
//             Azerbaijan: '阿塞拜疆',
//             Burundi: '布隆迪',
//             Belgium: '比利时',
//             Benin: '贝宁',
//             'Burkina Faso': '布基纳法索',
//             Bangladesh: '孟加拉国',
//             Bulgaria: '保加利亚',
//             'The Bahamas': '巴哈马',
//             'Bosnia and Herzegovina': '波斯尼亚和黑塞哥维那',
//             Belarus: '白俄罗斯',
//             Belize: '伯利兹',
//             Bermuda: '百慕大',
//             Bolivia: '玻利维亚',
//             Brazil: '巴西',
//             Brunei: '文莱',
//             Bhutan: '不丹',
//             Botswana: '博茨瓦纳',
//             'Central African Republic': '中非共和国',
//             Canada: '加拿大',
//             Switzerland: '瑞士',
//             Chile: '智利',
//             'Ivory Coast': '海牙',
//             Cameroon: '喀麦隆',
//             'Dem. Rep. Congo': '刚果民主共和国',
//             'Republic of the Congo': '刚果共和国',
//             Colombia: '哥伦比亚',
//             'Costa Rica': '哥斯达黎加',
//             Cuba: '古巴',
//             'Northern Cyprus': '塞浦路斯北部',
//             Cyprus: '塞浦路斯',
//             'Czech Republic': '捷克共和国',
//             Germany: '德国',
//             Djibouti: '吉布提',
//             Denmark: '丹麦',
//             'Dominican Republic': '多明尼加共和国',
//             Algeria: '阿尔及利亚',
//             Ecuador: '厄瓜多尔',
//             Egypt: '埃及',
//             Eritrea: '厄立特里亚',
//             Spain: '西班牙',
//             Estonia: '爱沙尼亚',
//             Ethiopia: '埃塞俄比亚',
//             Finland: '芬兰',
//             Fiji: '斐济',
//             'Falkland Is.': '福克兰群岛',
//             France: '法国',
//             Gabon: '加蓬',
//             'United Kingdom': '英国',
//             Georgia: '格鲁吉亚',
//             Ghana: '加纳',
//             Guinea: '几内亚',
//             Gambia: '冈比亚',
//             'Guinea Bissau': '几内亚比绍',
//             'Equatorial Guinea': '赤道几内亚',
//             Greece: '希腊',
//             Greenland: '格陵兰',
//             Guatemala: '危地马拉',
//             'French Guiana': '法属圭亚那',
//             Guyana: '圭亚那',
//             Honduras: '洪都拉斯',
//             Croatia: '克罗地亚',
//             Haiti: '海地',
//             Hungary: '匈牙利',
//             Indonesia: '印度尼西亚',
//             India: '印度',
//             Ireland: '爱尔兰',
//             Iran: '伊朗',
//             Iraq: '伊拉克',
//             Iceland: '冰岛',
//             Israel: '以色列',
//             Italy: '意大利',
//             Jamaica: '牙买加',
//             Jordan: '约旦',
//             Japan: '日本',
//             Kazakhstan: '哈萨克斯坦',
//             Kenya: '肯尼亚',
//             Cambodia: '柬埔寨',
//             Korea: '韩国',
//             Kosovo: '科索沃',
//             Kuwait: '科威特',
//             'Lao PDR': '老挝',
//             Lebanon: '黎巴嫩',
//             Liberia: '利比里亚',
//             Libya: '利比亚',
//             'Sri Lanka': '斯里兰卡',
//             Lesotho: '莱索托',
//             Lithuania: '立陶宛',
//             Luxembourg: '卢森堡',
//             Latvia: '拉脱维亚',
//             Morocco: '摩洛哥',
//             Moldova: '摩尔瓦西亚',
//             Madagascar: '马达加斯加',
//             Mexico: '墨西哥',
//             Macedonia: '马其顿',
//             Mali: '马里',
//             Malta: '马耳他',
//             Myanmar: '缅甸',
//             Montenegro: '黑山',
//             Mongolia: '蒙古',
//             Mozambique: '莫桑比克',
//             Mauritania: '毛里塔尼亚',
//             Malawi: '马拉维',
//             Malaysia: '马来西亚',
//             Namibia: '纳米比亚',
//             'New Caledonia': '新喀里多尼亚',
//             Niger: '尼日尔',
//             Nigeria: '尼日利亚',
//             Nicaragua: '尼加拉瓜',
//             Netherlands: '荷兰',
//             Norway: '挪威',
//             Nepal: '尼泊尔',
//             'New Zealand': '新西兰',
//             Oman: '阿曼',
//             Pakistan: '巴基斯坦',
//             Panama: '巴拿马',
//             Peru: '秘鲁',
//             Philippines: '菲律宾',
//             'Papua New Guinea': '巴布亚新几内亚',
//             Poland: '波兰',
//             'Puerto Rico': '波多黎各',
//             'Dem. Rep. Korea': '朝鲜',
//             Portugal: '葡萄牙',
//             Paraguay: '巴拉圭',
//             Qatar: '卡塔尔',
//             Romania: '罗马尼亚',
//             Russia: '俄罗斯',
//             Rwanda: '卢旺达',
//             'W. Sahara': '西撒哈拉',
//             'Saudi Arabia': '沙特阿拉伯',
//             Sudan: '苏丹',
//             'South Sudan': '南苏丹',
//             Senegal: '塞内加尔',
//             'Solomon Islands': '所罗门群岛',
//             'Sierra Leone': '塞拉利昂',
//             'El Salvador': '萨尔瓦多',
//             Somaliland: '索马里兰',
//             Somalia: '索马里',
//             'Republic of Serbia': '塞尔维亚共和国',
//             Suriname: '苏里南',
//             Slovakia: '斯洛伐克',
//             Slovenia: '斯洛文尼亚',
//             Swaziland: '斯威士兰',
//             Sweden: '瑞典',
//             Syria: '叙利亚',
//             Chad: '乍得',
//             Togo: '多哥',
//             Thailand: '泰国',
//             Tajikistan: '塔吉克斯坦',
//             Turkmenistan: '土库曼斯坦',
//             'East Timor': '东帝汶',
//             'Trinidad and Tobago': '特立尼达和多巴哥',
//             Tunisia: '哈萨克突尼斯斯坦',
//             Turkey: '土耳其',
//             'Taiwan(China)': '台湾',
//             'United Republic of Tanzania': '坦桑尼亚',
//             Uganda: '乌干达',
//             Ukraine: '乌克兰',
//             Uruguay: '乌拉圭',
//             'United States': '美国',
//             Uzbekistan: '乌兹别克斯坦',
//             Venezuela: '委内瑞拉',
//             Vietnam: '越南',
//             Vanuatu: '瓦努阿图',
//             'West Bank': '约旦河西岸',
//             Yemen: '也门',
//             'South Africa': '南非',
//             'S. Sudan': '南苏丹',
//             Tanzania: '坦桑尼亚',
//             'Central African Rep.': '中非共和国',
//             Zambia: '赞比亚',
//             Zimbabwe: '津巴布韦',
//             Congo: '刚果',
//             'Eq. Guinea': '几内亚',
//             "Côte d'Ivoire": '科特迪瓦共和国',
//             "Cape Verde": '佛得角共和国',
//             'U.A.E': '阿联酋',
//             Mauritius: '‌毛里求斯共和国',
//             'S. Geo. and S. Sandw. Is.': '南乔治亚和南桑威奇群岛',
//             'Saint Helena': '圣赫勒拿'
//         };

//         const convertData = (data: typeof toData) => {
//             let res = [];
//             for (let i = 0; i < data.length; i++) {
//                 let dataIndex = data[i] as typeof toData[number];
//                 let fromCoord = geoCoordMap[dataIndex[0]?.name as string];
//                 let toCoord = geoCoordMap[dataIndex[1]?.name as string];
//                 if (fromCoord && toCoord) {
//                     res.push([
//                         {
//                             coord: fromCoord,
//                         },
//                         {
//                             coord: toCoord,
//                         },
//                     ]);
//                 }
//             }
//             return res;
//         };

//         let series: any = [];

//         // 飞线数据
//         let toData = [
//             [
//                 {
//                     name: 'FR',
//                     // 这里为自定义数据，tooltip会用到。
//                     value: {
//                         tradingCountry: '法国',
//                         total: '1200万美元',
//                         tradingNum: '5',
//                         table: [
//                             {
//                                 tradeEnterprises: '内蒙古xx有限公司',
//                                 shopping: '乘用车',
//                                 mode: '进口',
//                                 money: '90',
//                             },
//                             {
//                                 tradeEnterprises: '内蒙古xx有限公司',
//                                 shopping: '发动机',
//                                 mode: '出口',
//                                 money: '60',
//                             },
//                         ],
//                     },
//                 },

//                 { name: 'CN' },
//             ],
//             [
//                 {
//                     name: 'RU',
//                     // 这里为自定义数据，tooltip会用到
//                     value: {
//                         tradingCountry: '俄罗斯',
//                         total: '200万美元',
//                         tradingNum: '18',
//                         table: [
//                             {
//                                 tradeEnterprises: '内蒙古xx有限公司',
//                                 shopping: '乘用车',
//                                 mode: '进口',
//                                 money: '34',
//                             },
//                             {
//                                 tradeEnterprises: '内蒙古xx有限公司',
//                                 shopping: '发动机',
//                                 mode: '出口',
//                                 money: '40',
//                             },
//                         ],
//                     },
//                 },

//                 { name: 'CN' },
//             ],
//             [
//                 {
//                     name: 'AG',
//                     // 这里为自定义数据，tooltip会用到
//                     value: {
//                         tradingCountry: '安提瓜和巴布达',
//                         total: '200万美元',
//                         tradingNum: '18',
//                         table: [
//                             {
//                                 tradeEnterprises: '内蒙古xx有限公司',
//                                 shopping: '乘用车',
//                                 mode: '进口',
//                                 money: '34',
//                             },
//                             {
//                                 tradeEnterprises: '内蒙古xx有限公司',
//                                 shopping: '发动机',
//                                 mode: '出口',
//                                 money: '40',
//                             },
//                         ],
//                     },
//                 },

//                 { name: 'RU' },
//             ],
//         ];

//         let datas = [['Peking', toData]];
//         datas.forEach((item, i) => {

//             series.push(
//                 {
//                     type: 'scatter',
//                     coordinateSystem: 'geo',
//                     zlevel: 2,
//                     symbol: 'circle',
//                     symbolSize: 10,
//                     data: (item[1] as typeof toData).map((dataItem: any, i) => {
//                         return {
//                             name: dataItem[0].name,
//                             value: geoCoordMap[dataItem[0].name],
//                             datas: dataItem.value,
//                             itemStyle: {
//                                 color: i === 0 ? 'red' : 'green', // 使用数据中的颜色
//                             },
//                         };
//                     })
//                 },
//                 {
//                     name: item[0],
//                     type: 'lines',
//                     zlevel: 1,
//                     // 飞行线特效
//                     effect: {
//                         show: true, // 是否显示
//                         period: 6, // 特效动画时间
//                         trailLength: 0, // 特效尾迹长度。取从 0 到 1 的值，数值越大尾迹越长
//                         symbol: planePath, // 特效图形标记
//                         symbolSize: 15, // 特效图标大小
//                     },
//                     // 线条样式
//                     lineStyle: {
//                         curveness: -0.2, // 飞线弧度
//                         type: 'solid', // 飞线类型
//                         color: 'rgb(255,236,61)', // 飞线颜色
//                         width: 2, // 飞线宽度
//                     },
//                     data: convertData(item[1] as typeof toData),
//                 },
//                 {
//                     type: "effectScatter",
//                     coordinateSystem: "geo",
//                     zlevel: 2,
//                     rippleEffect: {
//                         //涟漪特效
//                         period: 4, //动画时间，值越小速度越快
//                         brushType: "stroke", //波纹绘制方式 stroke, fill
//                         scale: 4 //波纹圆环最大限制，值越大波纹越大
//                     },
//                     label: {
//                         normal: {
//                             show: true,
//                             position: "right", //显示位置
//                             offset: [8, 0], //偏移设置
//                             color: '#fff',
//                             formatter: (params: any) => params.data.datas.tradingCountry, //圆环显示文字
//                         },
//                         emphasis: {
//                             show: true
//                         }
//                     },
//                     symbol: "circle",
//                     itemStyle: {
//                         color: 'rgb(62,212,255)',
//                         normal: {
//                             show: false
//                         }
//                     },
//                     // 这里用来组装自定义数据，以便在tooltip中取得。
//                     data: (item[1] as typeof toData).map((dataItem: any, i) => {
//                         return {
//                             name: dataItem[0].name,
//                             value: geoCoordMap[dataItem[0].name],
//                             datas: dataItem[0].value,
//                             itemStyle: {
//                                 color: i === 0 ? 'red' : 'green', // 使用数据中的颜色
//                             },
//                         };
//                     }),
//                 },
//                 // 被攻击点1
//                 {
//                     type: "effectScatter",
//                     coordinateSystem: "geo",
//                     zlevel: 3,
//                     rippleEffect: {
//                         //涟漪特效
//                         period: 4, //动画时间，值越小速度越快
//                         brushType: "stroke", //波纹绘制方式 stroke, fill
//                         scale: 4 //波纹圆环最大限制，值越大波纹越大
//                     },
//                     label: {
//                         normal: {
//                             show: true,
//                             position: "right",
//                             color: "#00ffff",
//                             formatter: (params: any) => params.data.datas.tradingCountry, //圆环显示文字
//                             textStyle: {
//                                 color: "#00ffff"
//                             }
//                         },
//                         emphasis: {
//                             show: true
//                         }
//                     },
//                     symbol: "circle",
//                     symbolSize: 15,
//                     itemStyle: {
//                         normal: {
//                             show: false,
//                         }
//                     },
//                     data: [
//                         {
//                             name: 'CN',
//                             // concat([10000])决定图例的颜色
//                             value: geoCoordMap['CN']?.concat([10000]),
//                             datas: {
//                                 total: "200",
//                                 tradingCountry: "中国"
//                             },
//                             type: "destination",
//                         }
//                     ]
//                 },
//             );
//         });

//         const option = {
//             // backgroundColor: '#100c2a',
//             backgroundColor: '#000',
//             // 底图样式
//             geo: {
//                 // show: false,
//                 map: 'world', // 地图类型
//                 roam: true, // 是否开启缩放
//                 zoom: 1.0, // 初始缩放大小
//                 center: [11.3316626, 19.5845024], // 地图中心点
//                 scaleLimit: {
//                     // 缩放等级
//                     min: 0.5,
//                     max: 5,
//                 },
//                 label: {
//                     emphasis: {
//                         show: false
//                     }
//                 },
//                 nameMap: nameMap, // 自定义地区的名称映射
//                 // 三维地理坐标系样式
//                 itemStyle: {
//                     // color: 'rgba(0,116,177, .6)',
//                     // borderColor: 'rgb(79,228,255)',
//                     // borderWidth: 0.5,
//                     // normal: {
//                     //     areaColor: '#323c48',
//                     //     borderColor: '#111',
//                     //     borderWidth: 0.5,
//                     // },
//                     // emphasis: {
//                     //     areaColor: '#2a333d'
//                     // }
//                     // normal: {
//                     //     areaColor: {
//                     //         image: <img alt="" src={IconEm} className="w-[40px] h-[40px]" />,
//                     //         repeat: 'repeat'
//                     //     },
//                     //     borderColor: '#ffffff'
//                     // },
//                     // emphasis: {
//                     //     color: {
//                     //         image: <img alt="" src={IconEm} className="w-[40px] h-[40px]" />,
//                     //         repeat: 'repeat'
//                     //     }
//                     // }

//                     normal: {
//                         areaColor: {
//                             type: 'pattern', // 使用图案填充
//                             image: domImg, // 图像路径
//                             // repeat: 'repeat' // 重复填充
//                         },
//                         borderColor: '#ffffff', // 边框颜色
//                         borderWidth: 0.5, // 边框宽度
//                     },
//                     emphasis: {
//                         areaColor: '#2a333d'
//                     }
//                 },

//                 // 鼠标悬浮样式
//                 emphasis: {
//                     itemStyle: {
//                         areaColor: 'rgba(0,162,248, .6)',
//                     },
//                     label: {
//                         show: true,
//                         color: '#90d9ff',
//                         fontSize: 16,
//                     }
//                 },
//                 regions: [
//                     {
//                         name: '中国', // 中国
//                         itemStyle: {
//                             areaColor: '#FF0000' // 红色
//                         }
//                     },
//                     {
//                         name: '美国', // 美国
//                         itemStyle: {
//                             areaColor: '#00FF00' // 绿色
//                         }
//                     },
//                     {
//                         name: '巴西', // 巴西
//                         itemStyle: {
//                             areaColor: '#0000FF' // 蓝色
//                         }
//                     },
//                     {
//                         name: 'Algeria', // 阿尔及利亚
//                         itemStyle: {
//                             areaColor: '#FFFF00' // 黄色
//                         }
//                     },
//                     {
//                         name: 'India', // 印度
//                         itemStyle: {
//                             areaColor: '#FF00FF' // 品红色
//                         }
//                     },
//                     {
//                         name: 'Russia', // 俄罗斯
//                         itemStyle: {
//                             areaColor: '#FFA500' // 橙色
//                         }
//                     },
//                     {
//                         name: 'Japan', // 日本
//                         itemStyle: {
//                             areaColor: '#800080' // 紫色
//                         }
//                     },
//                     {
//                         name: 'Germany', // 德国
//                         itemStyle: {
//                             areaColor: '#008000' // 绿色
//                         }
//                     },
//                     {
//                         name: 'France', // 法国
//                         itemStyle: {
//                             areaColor: '#00FFFF' // 青色
//                         }
//                     },
//                     {
//                         name: 'United Kingdom', // 英国
//                         itemStyle: {
//                             areaColor: '#FFC0CB' // 粉色
//                         }
//                     }
//                 ],
//                 tooltip: {
//                     show: true,
//                     trigger: 'item',
//                     enterable: true, // 鼠标是否可进入提示框浮层中，默认为false，如需详情内交互，如添加链接，按钮，可设置为 true
//                     backgroundColor: 'rgba(0,0,0,0.8)',
//                     borderColor: 'rgba(0,0,0,0.2)',
//                     textStyle: {
//                         color: '#fff',
//                     },
//                     formatter: function (params: any) {
//                         let res = '';

//                         // 是series定义的节点
//                         if (typeof params.data?.name !== 'undefined') {

//                             // 是series定义的连线
//                             if (params.seriesType == "lines") {
//                                 return params.data.fromName + " -> " + params.data.toName + "<br />" + params.data.value;
//                             }

//                             // 是series定义被攻击点
//                             if (params.data.type === 'destination') {
//                                 res =
//                                     "<span style='color:#fff;'>" +
//                                     params.data.datas.tradingCountry +
//                                     "</span><br/>目的地：" + 121
//                             } else {
//                                 res += "<div style='font-size: 18px;line-height: 18px'>基本信息</div>";
//                                 res +=
//                                     "<div style='display: flex; flex-direction: column;justify-content:flex-start;align-items: center;'>" +
//                                     "<div style='display: flex; width: 400px; justify-content: space-between; font-size: 14px;'>" +
//                                     '<div>对外贸易方向: ' +
//                                     params.data.datas.tradingCountry +
//                                     '</div>' +
//                                     '<div>外贸总金额: ' +
//                                     params.data.datas.total +
//                                     '</div>' +
//                                     '<div>外贸次数: ' +
//                                     params.data.datas.tradingNum +
//                                     '次</div>' +
//                                     '</div>';
//                                 res += "<div style='margin-top: 20px;'>";
//                                 res +=
//                                     "<table style='width:400px;font-weight:bold;' border='1' cellspacing='0' cellpadding='5'>" +
//                                     "<tbody style='height:14px;font-size: 14px;'>" +
//                                     "<tr style='border-bottom:1px solid #FFF;'><th style='text-align:center;color:#00D5EE;'>外贸企业</th>" +
//                                     "<th style='text-align:center;color:#00D5EE;'>外贸商品</th>" +
//                                     "<th style='text-align:center;color:#00D5EE;'>交易方式</th>" +
//                                     "<th style='text-align:center;color:#00D5EE;'>交易金额(万美元)</th></tr>";
//                                 for (let i = 0; i < params.data.datas.table.length; i++) {
//                                     let tData = params.data.datas.table[i];
//                                     res += '<tr>';
//                                     res +=
//                                         "<td style='text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;'>" +
//                                         tData.tradeEnterprises +
//                                         '</td>' +
//                                         "<td style='text-align:center;'>" +
//                                         tData.shopping +
//                                         '</td>' +
//                                         "<td style='text-align:center;'>" +
//                                         tData.mode +
//                                         '</td>' +
//                                         "<td style='text-align:center;'>" +
//                                         tData.money +
//                                         '</td>';
//                                     res += '</tr>';
//                                 }
//                                 res += '</tbody>';
//                                 res += '</table>';
//                                 res += '</div>';
//                                 res += '</div>';
//                             }
//                         } else {
//                             var name = params.name;
//                             res =
//                                 "<span style='color:#fff;'>" +
//                                 name +
//                                 "</span><br/>数据：" + 121
//                         }

//                         return res;
//                     },
//                 },
//             },

//             series: series,
//             tooltip: {
//                 show: true,

//             },
//         };

//         return option
//     }

//     React.useEffect(() => {

//         const chartDom = document.getElementById('main');
//         const footerDom = document.getElementById('footer');
//         const myChart = echarts.init(chartDom);

//         echarts.registerMap('world', worldGeoJson as unknown as Parameters<typeof echarts.registerMap>[1]);
//         const option = getOption()
//         option && myChart.setOption(option);

//         footerDom?.appendChild(domImg)
//     }, [])
//     return (
//         <div className="w-full h-full flex flex-col">
//             <div id='main' className="flex-1"></div>
//             <div className="w-full h-[0px] " id='footer'>

//             </div>
//         </div>
//     );
// }

// function WorldComponent() {
//     React.useEffect(() => {
//         var ROOT_PATH = 'https://echarts.apache.org/examples';
//         var app = {};

//         var chartDom = document.getElementById('main');
//         var myChart = echarts.init(chartDom);
//         var option;

//         var dataCount = 0;
//         var CHUNK_COUNT = 230;

//         // Fetch GPS data
//         function fetchData(idx) {
//             if (idx >= CHUNK_COUNT) {
//                 return;
//             }
//             var dataURL = ROOT_PATH + '/data/asset/data/gps/gps_' + idx + '.bin';
//             var xhr = new XMLHttpRequest();
//             xhr.open('GET', dataURL, true);
//             xhr.responseType = 'arraybuffer';
//             xhr.onload = function (e) {
//                 var rawData = new Int32Array(this.response);
//                 var data = new Float32Array(rawData.length);
//                 var addedDataCount = rawData.length / 2;
//                 for (var i = 0; i < rawData.length; i += 2) {
//                     data[i] = rawData[i + 1] / 1e7;
//                     data[i + 1] = rawData[i] / 1e7;
//                 }
//                 myChart.appendData({
//                     seriesIndex: 0,
//                     data: data
//                 });
//                 fetchData(idx + 1);
//             };
//             xhr.send();
//         }

//         // Register the world map
//         echarts.registerMap('world', {});

//         option = {
//             backgroundColor: '#000',
//             title: {
//                 text: '10000000 GPS Points',
//                 left: 'center',
//                 textStyle: {
//                     color: '#fff'
//                 }
//             },
//             geo: {
//                 map: 'world', // Ensure the map is registered
//                 roam: true,
//                 label: {
//                     emphasis: {
//                         show: false
//                     }
//                 },
//                 silent: true,
//                 itemStyle: {
//                     normal: {
//                         areaColor: '#323c48',
//                         borderColor: '#111'
//                     },
//                     emphasis: {
//                         areaColor: '#2a333d'
//                     }
//                 }
//             },
//             series: [
//                 {
//                     name: '弱',
//                     type: 'scatterGL',
//                     progressive: 1e6,
//                     coordinateSystem: 'geo',
//                     symbolSize: 1,
//                     zoomScale: 0.002,
//                     blendMode: 'lighter',
//                     large: true,
//                     itemStyle: {
//                         color: 'rgb(20, 15, 2)'
//                     },
//                     postEffect: {
//                         enable: true
//                     },
//                     silent: true,
//                     dimensions: ['lng', 'lat'],
//                     data: new Float32Array()
//                 }
//             ]
//         };

//         // Fetch the data and set the option
//         fetchData(0);
//         myChart.setOption(option); // Move this line after fetchData to ensure data is loaded

//     }, []);

//     return (
//         <div className="w-full h-full">
//             <div id='main' style={{ width: '100%', height: '100%' }}></div>
//         </div>
//     );
// }
