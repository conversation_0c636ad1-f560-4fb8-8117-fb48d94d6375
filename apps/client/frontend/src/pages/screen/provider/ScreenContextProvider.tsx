import React, { createContext, useEffect, useMemo, useRef } from 'react'
import { useScreenInfo } from '@/api/screen'

import { ScreenDataType } from '../types'

type ScreenStateStorage = {
  screenData: ScreenDataType | null
}

const ScreenContext = createContext<ScreenStateStorage>(null!)

export const useScreenContext = () => React.useContext(ScreenContext)

export function ScreenContextProvider({
  children,
}: { children: React.ReactNode }) {
  // const [screenData, setScreenData] = useState<ScreenInfoType>({});

  const { data: screenData, refetch } = useScreenInfo()

  const timer = useRef<NodeJS.Timeout | null>(null)

  const ScreenMemo = useMemo(() => {
    return {
      screenData,
    }
  }, [screenData])

  useEffect(() => {
    timer.current && clearInterval(timer.current)
    timer.current = setInterval(() => {
      refetch()
    }, 60000)
    return () => {
      timer.current && clearInterval(timer.current)
    }
  }, [])

  return (
    <ScreenContext.Provider value={ScreenMemo}>
      {children}
    </ScreenContext.Provider>
  )
}
