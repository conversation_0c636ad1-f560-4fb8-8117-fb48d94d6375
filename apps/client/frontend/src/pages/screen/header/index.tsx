import { useEffect, useState } from 'react'
import { dateFormat } from '@/lib/utils'

import { useNavigate } from '@tanstack/react-router'

import HeaderJsonSvg from '@/assets/svg/screen/header-json.svg?react'
import RightArrowSvg from '@/assets/svg/right-arrow.svg?react'
import HeaderLogoSvg from '@/assets/svg/screen/header-logo.svg?react'

import packageJson from 'package.json'

const ScreenHeader = () => {
  const navigate = useNavigate()
  const [date, setDate] = useState(new Date())

  const quitScreen = () => {
    navigate({ to: '/dashboard' })
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setDate(new Date())
    }, 1000)

    return () => {
      clearInterval(timer)
    }
  }, [])

  // font-[Furore]
  return (
    <div className="screen-header flex items-center bg-[#050506]">
      <div className=" relative flex-1 flex items-center pb-2">
        <div className=" relative flex-1 flex items-center bg-[#121213] pt-4 pb-2 pl-8 space-x-4">
          <div className=" flex space-x-4 relative">
            <div
              className=" w-[120px] h-[56px] mr-[410px] bg-[#1E3A8A] rounded-md px-4 py-2 flex items-center space-x-2 text-base cursor-pointer"
              onClick={quitScreen}
            >
              <RightArrowSvg className="w-4 h-4" />
              <span>返回系统</span>
            </div>
            <div className="absolute -top-4 left-[104px]">
              {/* <div className="text-[32px] leading-[36px]">雾阵匿名网络系统</div>
                            <div className="uppercase">Raykite · Visual Testing</div> */}
              {/* <img src={HeaderLogoSvg} alt="" className="w-[410px]" /> */}
              <HeaderLogoSvg className="w-[410px]" />
            </div>
          </div>
          <div className=" flex space-x-6 text-sm  items-center">
            <div className="">
              <div>基础地图数据库</div>
              <div className="text-[8px]">Basic Map Database</div>
            </div>
            <div className="w-[1px] h-6 bg-[#C4C4C4]"></div>
            <div className="flex space-x-2">
              <HeaderJsonSvg className="w-[44px] h-8" />
              <div className="">
                <div>数据展示版本</div>
                <div className="text-[12px]">
                  V{packageJson.version.split('-')[0]}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="absolute left-0 bottom-[6px] w-full h-[1px]"
          style={{
            background:
              'repeating-linear-gradient(to right, white, white 8px, transparent 8px, transparent 10px)',
          }}
        ></div>
      </div>
      <div
        className={`screen-header__right w-[550px] custom-2439:w-[900px] custom-1920:w-[750px] h-full flex flex-col justify-end relative pb-2`}
      >
        <div className="screen-header__right_bg w-full h-full absolute top-0 left-0" />
        <div className="screen-header__right_content bg-[#121213] w-full h-full flex flex-col justify-center space-y-2.5 py-[14px] pr-[43px] text-sm">
          <div className="flex justify-end text-right text-[26px] leading-6">
            {dateFormat(date, 'HH:mm:ss')}
          </div>
          <div className="flex justify-end items-end leading-[14px]">
            <div>{dateFormat(date, 'DD/MM/YYYY')}</div>
            <div className="w-[1px] h-[11px] bg-[rgba(255,255,255,0.30)] ml-[7px] mr-[3px]"></div>
            <div className="text-[#B3B5B7]">{dateFormat(date, 'dddd')}</div>
          </div>
          <div
            className="absolute right-[0] bottom-[6px] w-[calc(100%-337px)] custom-2439:w-[calc(100%-570px)] custom-1920:w-[calc(100%-458px)] h-[1px]"
            style={{
              background:
                'repeating-linear-gradient(to right, white, white 8px, transparent 8px, transparent 10px)',
            }}
          ></div>
        </div>
      </div>
    </div>
  )
}

export default ScreenHeader
