import { ScreenContextProvider } from './provider/ScreenContextProvider'

import ScreenHeader from './header'
import ProxyOverview from './content/proxy-overview'
import LineMap from './content/line-map'
import NodeAnalysis from './content/node-analysis'
import RealTimeTraffic from './content/real-time-traffic'

import AsideDottedSvg from '@/assets/svg/screen/aside-dotted.svg?react'

import './scss/index.scss'
import { cn } from '@/lib/utils'

export const Screen = () => {
  const ScreenAsideBg = () => {
    return (
      <div className="flex h-full">
        <AsideDottedSvg className="w-[22px] h-full" />
      </div>
    )
  }

  // 总数

  return (
    <ScreenContextProvider>
      <div className="big-screen bg-[#050506] text-white flex flex-col w-screen min-w-[1440px] h-full overflow-hidden">
        <ScreenHeader />
        <div
          className={cn(
            'flex flex-1 w-full overflow-hidden',
            'py-1 px-0 custom-2439:pt-[23px] custom-2439:pb-[25px] custom-2439:px-[10px]',
            ' space-x-0 custom-2439:space-x-7',
          )}
        >
          <ScreenAsideBg />
          <div className="flex-1 flex flex-col overflow-hidden space-y-2">
            <div className="flex-1 flex overflow-hidden">
              <ProxyOverview />
              <LineMap />
              <NodeAnalysis />
            </div>
            {/* 实时网络状况统计 */}
            <div className="h-[184px] custom-1920:h-[289px] custom-2439:h-[295px] w-full overflow-hidden">
              <RealTimeTraffic />
            </div>
          </div>
          <ScreenAsideBg />
        </div>
      </div>
    </ScreenContextProvider>
  )
}
