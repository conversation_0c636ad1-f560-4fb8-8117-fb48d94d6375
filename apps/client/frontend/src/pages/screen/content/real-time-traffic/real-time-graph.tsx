import React, { useEffect, useRef } from 'react'
import * as echarts from 'echarts'
// import 'echarts-gl';
import type { EChartsType } from 'echarts'

import { useTrafficRealTimeContext } from '@/provider/TrafficRealTimeContextProvider'

import RealTimeMaskPng from '@/assets/image/screen/real-time-mask.png'
import RealTimeMaskImg from '@/assets/svga/real-time-mask'

interface GraphicOption {
  elements: Array<{
    type: string
    style: {
      image: string
      width: number
      height: number
    }
    x: number
    y: number
    z: number
  }>
}

const DATA_ZOOM_NUM = 99

const RealTimeGraph = React.memo(() => {
  const lineDomRef = React.useRef<HTMLDivElement | null>(null)
  const lineChart = React.useRef<EChartsType | null>(null)
  const { realTimeTrafficData } = useTrafficRealTimeContext()

  const getLineOption = () => {
    const imageDom = document.createElement('img')
    // 设置 img 元素的属性
    imageDom.src = RealTimeMaskPng // 图片的 URL
    imageDom.alt = '' // 图片的替代文本
    // img.width = 300; // 设置宽度
    // img.height = 200; // 设置高度

    const option = {
      animation: true,
      animationDuration: 1000,
      // animationEasing: 'exponentialOut',
      animationDurationUpdate: 900,
      grid: {
        left: 60,
        right: 0,
        top: 14,
        bottom: 22,
        containLabel: false,
      },
      legend: {
        show: false,
      },
      tooltip: {
        trigger: 'axis',
        position: 'top',
        padding: 8,
        borderWidth: 0,
        transitionDuration: 0,
      },
      dataZoom: [
        {
          show: false,
          startValue: 0,
          endValue: DATA_ZOOM_NUM,
        },
      ],
      xAxis: [
        {
          type: 'category',
          boundaryGap: [0.2, 0.2],
          data: [],
          axisLabel: {
            color: '#999',
            align: 'center',
            padding: 0,
            height: 16,
            lineHeight: 16,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          z: 1,
        },
      ],
      yAxis: [
        {
          type: 'value',
          scale: true,
          min: 0,
          boundaryGap: [0.2, 0.2],
          splitLine: {
            show: false, // 显示分割线
          },
          axisLabel: {
            align: 'right',
          },
        },
      ],
      graphic: {
        elements: [
          {
            type: 'image',
            style: {
              image: RealTimeMaskImg,
            },
            x: 60,
            y: 14,
            z: -10,
          },
        ],
      },
      series: [
        {
          name: '上传',
          type: 'line',
          data: [],
          // smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          // hoverAnimation: true, // 启用悬停动画
          lineStyle: {
            type: [5, 1.5],
            dashOffset: 5,
            color: '#018CFF',
            width: 1.5,
          },
          itemStyle: {
            opacity: 0, // 默认不显示符号
            color: '#018CFF',
            borderColor: 'white',
            borderWidth: 2,
            borderType: 'solid',
            shadowColor: 'rgba(0, 0, 0, 0.10)',
            shadowBlur: 4,
            shadowOffsetX: 0,
            shadowOffsetY: 4,
          },
          emphasis: {
            itemStyle: {
              opacity: 1, // 鼠标悬停时显示符号
            },
          },
          animation: true,
          animationDuration: 2000,
          animationDelay: 0,
          universalTransition: {
            enabled: true,
          },
        },
        {
          name: '下载',
          type: 'line',
          data: [],
          symbol: 'circle',
          symbolSize: 8,
          // hoverAnimation: true, // 启用悬停动画
          lineStyle: {
            type: [5, 1.5],
            dashOffset: 5,
            color: '#00F700',
            width: 1.5,
          },
          itemStyle: {
            opacity: 0, // 默认不显示符号
            color: '#00F700',
            borderColor: 'white',
            borderWidth: 2,
            borderType: 'solid',
            shadowColor: 'rgba(0, 0, 0, 0.10)',
            shadowBlur: 4,
            shadowOffsetX: 0,
            shadowOffsetY: 4,
          },
          emphasis: {
            itemStyle: {
              opacity: 1, // 鼠标悬停时显示符号
            },
          },
          animation: true,
          animationDuration: 2000,
          animationDelay: 0,
          universalTransition: {
            enabled: true,
          },
        },
      ],
      barCategoryGap: '0%',
    }
    return option
  }

  const updateGraphic = () => {
    if (!lineDomRef.current || !lineChart.current) return
    const option = lineChart.current.getOption()

    const elements =
      (option['graphic'] as Array<GraphicOption>)[0]?.elements ?? []
    const graphicElement = elements[0]
    const grid = (
      option['grid'] as Array<{
        top: number
        bottom: number
        left: number
        right: number
      }>
    )[0]
    if (!grid) return
    // 获取图表的实际宽高
    const height = lineDomRef.current?.offsetHeight - grid.top - 14
    const width = lineDomRef.current.offsetWidth - grid.left - grid?.right
    // 更新背景图的大小和位置
    if (graphicElement) {
      graphicElement.style.width = width
      graphicElement.style.height = height
      graphicElement.x = grid.left
      graphicElement.y = grid.top - 4
    }

    // 更新图表
    lineChart.current.setOption({
      graphic: {
        elements: [graphicElement],
      },
    })
  }

  const handleResize = () => {
    if (!lineChart.current) return
    lineChart.current.resize()
    updateGraphic()
  }
  const timer = useRef<NodeJS.Timeout | null>(null)
  useEffect(() => {
    const upload = realTimeTrafficData.upload.slice(-DATA_ZOOM_NUM)
    const download = realTimeTrafficData.download.slice(-DATA_ZOOM_NUM)
    const allData = [...upload, ...download].filter(
      (index) => index !== '',
    ) as Array<number>
    const MaxData = allData.length > 0 ? Math.max(...allData) : 0

    let factor = 1
    if (MaxData >= 1024 ** 2) {
      // 大于等于 1 GB
      factor = 3
    } else if (MaxData >= 1024) {
      // 大于等于 1 MB
      factor = 2
    }

    function getUnit(factor: number) {
      return factor === 3 ? 'GB' : factor === 2 ? 'MB' : 'KB'
    }
    const conversion = 1024 ** (factor - 1) || 1 // 换算值，转为GB/MB/KB
    const uploadData = upload.map((value) =>
      value === '' ? 0 : ((value as number) / conversion).toFixed(factor * 4),
    )
    const downloadData = download.map((value) =>
      value === '' ? 0 : ((value as number) / conversion).toFixed(factor * 4),
    )

    timer.current && clearTimeout(timer.current)
    timer.current = setTimeout(() => {
      lineChart.current?.dispatchAction({
        type: 'downplay',
      })
      lineChart.current?.dispatchAction({
        type: 'hideTip',
      })
      timer.current && clearTimeout(timer.current)
    }, 300)
    lineChart.current?.setOption({
      tooltip: {
        valueFormatter: (value: number) => {
          const number_ = Number(value)
          function formatter(number_: number, factor: number): string {
            if (number_ < 1) {
              if (factor === 3) {
                // 当前单位是 GB
                return number_ * 1024 < 1
                  ? formatter(number_ * 1024, 2)
                  : `${(number_ * 1024).toFixed(2)} MB`
              } else if (factor === 2) {
                // 当前单位是 MB
                return `${(number_ * 1024).toFixed(2)} KB`
              }
            }
            return `${Math.ceil(number_)} ${getUnit(factor)}`
          }
          return `${formatter(number_, factor)}`
        },
      },
      xAxis: [{ data: realTimeTrafficData.categories.slice(-DATA_ZOOM_NUM) }],
      yAxis: [
        {
          unit: getUnit(factor),
          axisLabel: {
            formatter: (value: number) => `${value}${getUnit(factor)}`,
          },
        },
      ],
      series: [{ data: uploadData }, { data: downloadData }],
    })
  }, [realTimeTrafficData])

  useEffect(() => {
    // const dom = document.getElementById();
    lineChart.current = echarts.init(lineDomRef.current)
    const option = getLineOption()
    option && lineChart.current.setOption(option)
    updateGraphic()
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
      lineChart.current?.dispose()
      lineChart.current = null
    }
  }, [])

  return (
    <div className="h-full flex flex-col w-full">
      <div ref={lineDomRef} className="h-full w-full"></div>
    </div>
  )
})

export default RealTimeGraph
