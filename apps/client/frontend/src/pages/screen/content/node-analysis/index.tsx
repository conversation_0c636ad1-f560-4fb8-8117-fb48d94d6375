import UsageMonitor from './usage-monitor'
import NodeDisplay from './node-display'
import { BigTitle } from '@/pages/screen/component/small-title'
import { cn } from '@/lib/utils'

const NodeAnalysis = () => {
  return (
    <div
      className={cn(
        'aside-custom node-analysisw flex flex-col space-y-2 h-full overflow-hidden',
        'w-[352px] custom-1920:w-[398px] custom-2439:w-[489px]',
      )}
    >
      <BigTitle title="节点分析" />
      <UsageMonitor />
      <NodeDisplay />
    </div>
  )
}

export default NodeAnalysis
