import { useMemo } from 'react'
import { SmallTitle } from '@/pages/screen/component/small-title'

import UsageMonitorLeft from './usage-monitor-left'
import UsageMonitorRight from './usage-monitor-right'
import { useScreenContext } from '@/pages/screen/provider/ScreenContextProvider'

export interface ProxyNumberType {
  active_proxy_num: number
  idle_proxy_num: number
  total_proxy_num: number
}
const UsageMonitor = () => {
  const { screenData } = useScreenContext()

  const proxyNumber = useMemo(() => {
    const { active_proxy_num, idle_proxy_num, total_proxy_num } =
      screenData ?? {
        active_proxy_num: 0,
        idle_proxy_num: 0,
        total_proxy_num: 0,
      }
    return {
      active_proxy_num,
      idle_proxy_num,
      total_proxy_num,
    }
  }, [screenData])

  const usingPercentage = useMemo(() => {
    const { active_proxy_num, total_proxy_num } = proxyNumber
    return Number(((active_proxy_num / total_proxy_num) * 100).toFixed(0))
  }, [proxyNumber])

  return (
    <div className="w-full flex flex-col space-y-2">
      <SmallTitle title="节点使用情况监控" />
      <div className="px-1.5 custom-2439:px-4 flex custom-2439:space-x-[46px] bg-[#0E0E11]">
        <UsageMonitorLeft
          proxyNum={proxyNumber}
          usingPercentage={usingPercentage}
        />

        <UsageMonitorRight
          proxyNum={proxyNumber}
          usingPercentage={usingPercentage}
        />
      </div>
    </div>
  )
}

export default UsageMonitor
