import { useEffect, useRef, useState } from 'react'

import { ProxyRegionDelayType } from './index'
import { PROXY_QUALITY_TYPE, PROXY_STATUS_KEY } from '@/data/index'
import { getDelayStatus } from '@/hooks/useDelayStatus'
import { cn, getUrl } from '@/lib/utils'

import { EllipsisTooltip } from '@/components/encapsulation/EllipsisTooltip'

import GenerallyDelaySvg from '@/assets/svg/screen/node-analysis/generally-delay.svg'
import GoodDelaySvg from '@/assets/svg/screen/node-analysis/good-delay.svg'
import ExcellenDelaytSvg from '@/assets/svg/screen/node-analysis/excellent-delay.svg'
import TimeoutDelaySvg from '@/assets/svg/screen/node-analysis/timeout-delay.svg'

export const PROXY_STATUS_SVG: { [key: string]: string } = {
  [PROXY_STATUS_KEY.GENERALLY]: GenerallyDelaySvg,
  [PROXY_STATUS_KEY.GOOD]: GoodDelaySvg,
  [PROXY_STATUS_KEY.EXCELLENT]: ExcellenDelaytSvg,
  [PROXY_STATUS_KEY.TIMEOUT]: TimeoutDelaySvg,
}

interface DelayTableProps {
  list: ProxyRegionDelayType[]
}
const DelayTable = ({ list }: DelayTableProps) => {
  const url = getUrl()

  const outRef = useRef<HTMLDivElement>(null)
  const innerRef = useRef<HTMLDivElement>(null)
  const timer = useRef<NodeJS.Timeout | null>(null)

  const currentIndex = useRef(0)
  const [newList, setNewList] = useState(list)

  const getProxyQuality = (node: ProxyRegionDelayType) => {
    const delayType = getDelayStatus(node.delay)
    return {
      name: PROXY_QUALITY_TYPE[delayType],
      svg: PROXY_STATUS_SVG[delayType],
    }
  }

  const timeoutTimer = useRef<NodeJS.Timeout | null>(null)
  const scrollList = () => {
    timer.current && clearInterval(timer.current)
    const scrollToNextItem = () => {
      currentIndex.current += 1
      const outHeight = outRef.current?.offsetHeight ?? 0
      const innerHeight = innerRef.current?.offsetHeight ?? 0
      if (!outRef.current || !innerRef.current || outHeight >= innerHeight)
        return

      const childs = Array.from(innerRef.current.children) as HTMLElement[]
      const height = (childs[currentIndex.current]?.offsetHeight ?? 0) + 4
      const style = window.getComputedStyle(innerRef.current)
      const oldTranslateY = new DOMMatrix(style.transform).m42
      if (-(oldTranslateY - height) >= height * list.length) {
        timeoutTimer.current && clearInterval(timeoutTimer.current)
        timeoutTimer.current = setTimeout(() => {
          timeoutTimer.current && clearInterval(timeoutTimer.current)
          if (!innerRef.current) return
          currentIndex.current = 0
          innerRef.current.style.transition = 'none'
          innerRef.current.style.transform = `translateY(0px)`
        }, 800)
      }

      const scrollY = oldTranslateY - height
      innerRef.current.style.transition = 'transform 0.8s ease-out'
      innerRef.current.style.transform = `translateY(${scrollY}px)`
    }
    timer.current = setInterval(scrollToNextItem, 2000)
  }

  const isOverHeight = () => {
    if (!outRef.current || !innerRef.current) return false
    const outHeight = outRef.current?.offsetHeight ?? 0
    const itemHeight =
      ((innerRef.current?.children[0] as HTMLElement)?.offsetHeight ?? 0) + 4
    return outHeight < itemHeight * list.length
  }

  const changeList = () => {
    if (isOverHeight()) {
      scrollList()
      return setNewList([...list, ...list])
    }
    if (innerRef.current) {
      innerRef.current.style.transition = 'none'
      innerRef.current.style.transform = `translateY(0px)`
      timer.current && clearInterval(timer.current)
    }
    setNewList(list)
  }

  useEffect(() => {
    changeList()
  }, list)

  useEffect(() => {
    scrollList()
    changeList()

    window.addEventListener('resize', changeList)

    return () => {
      timer.current && clearTimeout(timer.current)
    }
  }, [])

  const DelayItem = ({
    item,
    index,
  }: { item: ProxyRegionDelayType; index: number }) => {
    const index_ = index > list.length - 1 ? index - list.length : index
    return (
      <div key={index} className="w-full flex items-center space-x-1.5">
        <div
          className={cn(
            'flex-1 flex justify-between items-center py-1.5 pl-4 pr-6 space-x-1 overflow-hidden',
            index_ % 2 === 0 ? 'bg-[rgba(48,48,48,0.40)]' : 'bg-[transparent]',
          )}
        >
          <div className="flex-1 flex items-center space-x-1 overflow-hidden">
            <img
              className="w-4 h-4"
              src={`${url}/res/flag3/${item.country_code.toLowerCase()}.svg`}
              alt=""
            />
            <EllipsisTooltip type="text" text={item.name} lineNum={1} />
          </div>
          <div className="flex text-center pl-1">{item.delay + ' ms'}</div>
        </div>
        <div
          className={cn(
            'w-[120px] text-center py-1.5 pl-2.5 pr-1 flex items-center justify-between self-stretch',
            index_ % 2 === 0 ? 'bg-[rgba(48,48,48,0.40)]' : 'bg-[transparent]',
          )}
        >
          <div>{getProxyQuality(item).name}</div>
          <img src={getProxyQuality(item).svg} alt="" className="w-[34px]" />
        </div>
      </div>
    )
  }

  return (
    <div
      className="w-full flex-1 mt-2 flex flex-col space-y-1 overflow-hidden transition-all"
      ref={outRef}
    >
      <div ref={innerRef} className="delay-table flex flex-col space-y-1">
        {newList.map((item, index) => (
          <DelayItem key={index} item={item} index={index} />
        ))}
      </div>
    </div>
  )
}

export default DelayTable
