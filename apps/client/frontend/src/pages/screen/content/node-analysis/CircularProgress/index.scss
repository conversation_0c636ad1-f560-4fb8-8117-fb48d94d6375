.circular-progress {
    // width: 334px;
    // height: 334px;
    background: conic-gradient(#ADFA1D 0deg var(--circular-progress), #E5E7EB var(--last) 360deg);
    border-radius: 50%;
    position: relative;
    // position: absolute;
    // inset: 50%;
    // transform: translate(-50%, -50%);
    // padding: 6px;
    // z-index: -1;

    .inner {
        width: 18px;
        height: 18px;
        background-color: #fff;
        border-radius: 50%;
        position: relative;

        &::before {
            content: attr(data-progress);
            // content: '';
            color: #ADFA1D;
            position: absolute;
            // inset: 11px;
            background-color: var(--mask-bg);
            width: 18px;
            height: 18px;
            text-align: center;
            // line-height: 300px;
            border-radius: 50%;
        }
    }
}
