import { useMemo, useRef, useEffect } from 'react'
import * as echarts from 'echarts'
import type { EChartsType, EChartsCoreOption } from 'echarts'

import DeclineSvg from '@/assets/svg/screen/usage-monitor/decline.svg?react'
import RiseSvg from '@/assets/svg/screen/usage-monitor/rise.svg?react'
import { cn } from '@/lib/utils'

import { ProxyNumberType } from './index'

const UsageMonitorRight = ({
  proxyNum,
  usingPercentage,
}: { proxyNum: ProxyNumberType; usingPercentage: number }) => {
  const pieDomRef = useRef<HTMLDivElement | null>(null)
  const resizeObserver = useRef<ResizeObserver | null>(null)
  const chartRef = useRef<EChartsType | null>(null)

  const handleResize = () => {
    chartRef.current?.resize()

    // 获取容器的宽高
    const containerWidth = pieDomRef.current?.clientWidth ?? 0
    const containerHeight = pieDomRef.current?.clientHeight ?? 0
    const imageSize = Math.min(containerWidth, containerHeight) * 0.55
    chartRef.current?.setOption({
      graphic: {
        elements: [
          {
            style: {
              width: imageSize,
              height: imageSize,
            },
          },
        ],
      },
    })
  }

  const progressData = useMemo(() => {
    return {
      using: [
        {
          name: '空闲中',
          value: proxyNum.idle_proxy_num,
          itemStyle: {
            color: '#eef8f4',
            borderRadius: '10px',
            shadowBlur: 5,
            shadowColor: '#eef8f4',
          },
        },
        {
          name: '使用中',
          value: proxyNum.active_proxy_num,
          itemStyle: {
            color: usingPercentage <= 80 ? '#018cff' : '#dc2625',
            borderRadius: '10px',
            shadowBlur: 5,
            shadowColor: usingPercentage <= 80 ? '#018cff' : '#dc2625',
          },
        },
      ],
      unused: [
        {
          name: '空闲中',
          value: proxyNum.idle_proxy_num,
          itemStyle: {
            color: '#fff',
          },
        },
        {
          name: '使用中',
          value: proxyNum.active_proxy_num,
          itemStyle: {
            color: '#677681',
          },
        },
      ],
    }
  }, [proxyNum, usingPercentage])

  useEffect(() => {
    chartRef.current?.setOption(getOption())
  }, [progressData])

  const getOption = () => {
    const option: EChartsCoreOption = {
      tooltip: {
        show: false,
      },
      series: [
        {
          type: 'pie',
          radius: ['73%', '75%'], // 外圆环
          data: progressData.unused,
          label: {
            show: false, // 不显示标签
          },
          animation: true, // 开启动画
          animationDuration: 1000, // 动画持续时间
          animationEasing: 'linear', // 动画缓动效果
          emphasis: {
            scale: false,
          },
        },
        {
          colorBy: 'data',
          type: 'pie',
          startAngle: 30,
          radius: ['60%', '67%'], // 内圆环
          data: progressData.using,
          padAngle: 5,
          label: {
            show: true,
            fontSize: 8,
            lineHeight: 7,
            position: 'center',
            color: '#fff',
            padding: [4, 0],
            formatter: (
              // params: { dataIndex: number, value: number }
            ) => {
              let total = 0
              progressData.using?.forEach(
                (item) => (total += (item as { value: number }).value),
              )
              const value =
                total === 0
                  ? '0'
                  : (
                      ((progressData.using[1]?.value ?? 0) / total) *
                      100
                    ).toFixed(0)
              const tip = usingPercentage <= 80 ? 'HIGH' : 'LOW'
              return `{value|${value}} {unit|%}\n{line1|1}\n{separation|1}\n{line2|1}\n{name1|节点使用率}\n{name2|${tip}}\n{line3|}\n{image|}`
            },
            rich: {
              value: {
                color: '#fff',
                fontSize: 22,
                fontWeight: 'bold',
                fontFamily: 'Furore',
                align: 'center',
                verticalAlign: 'top',
                lineHeight: 25,
                padding: [8, 0, 0, 2],
              },
              unit: {
                color: '#fff',
                fontSize: 7,
                fontFamily: 'Alibaba',
                align: 'center',
                verticalAlign: 'top',
                padding: [10, 0, 0, 2],
              },
              line1: {
                color: 'transparent',
                align: 'center',
                verticalAlign: 'top',
                lineHeight: 9,
              },
              separation: {
                width: 50,
                height: 1,
                color: 'transparent',
                align: 'center',
                verticalAlign: 'middle',
                backgroundColor: 'rgba(255,255,255,0.40)',
                lineHeight: 1,
              },
              line2: {
                color: 'transparent',
                align: 'center',
                verticalAlign: 'top',
                lineHeight: 5,
              },
              name1: {
                color: '#fff',
                fontSize: 8,
                fontFamily: 'Alibaba',
                verticalAlign: 'bottom',
                align: 'center',
                lineHeight: 10,
                // padding: [14, 0, 0, 0]
              },
              name2: {
                color: usingPercentage <= 80 ? '#018CFF' : '#dc2626',
                fontSize: 8,
                fontFamily: 'Alibaba',
                verticalAlign: 'bottom',
                align: 'center',
                lineHeight: 10,
                // padding: [14, 0, 0, 0],
              },
              line3: {
                color: 'transparent',
                align: 'center',
                verticalAlign: 'top',
                lineHeight: 2,
              },
              image: {
                width: 6,
                height: 6,
                verticalAlign: 'bottom',
                align: 'center',
                // lineHeight: 15,
                backgroundColor: {
                  image: `data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjYiIHZpZXdCb3g9IjAgMCA1IDYiIGZpbGw9Im5vbmUiPgo8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTMuMDA1NzQgMi43MzQ1NFYwLjgyODYxM0wyLjI0MzM1IDAuODI4NjEzTDIuMjQzMzUgMi43MzQ1NEgwLjMzNzQwMlYzLjQ5NjkySDIuMjQzMzVMMi4yNDMzNSA1LjQwMjk0SDMuMDA1NzRWMy40OTY5Mkg0LjkxMTczVjIuNzM0NTRIMy4wMDU3NFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPg==`, // 动态生成国旗图标
                },
              },
            },
          },
          emphasis: {
            scale: false,
          },
        },
      ],
      graphic: {
        elements: [
          {
            type: 'image',
            style: {
              image:
                'data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMTQiIGhlaWdodD0iMTE0IiB2aWV3Qm94PSIwIDAgMTE0IDExNCIgZmlsbD0ibm9uZSI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTcuMTExIDE2Ljg0MTdDMzkuMTkwMyAtNS4yMzc2MSA3NC45ODggLTUuMjM3NjEgOTcuMDY3NCAxNi44NDE3QzExOS4xNDcgMzguOTIxMSAxMTkuMTQ3IDc0LjcxODcgOTcuMDY3NCA5Ni43OTgxQzc0Ljk4OCAxMTguODc3IDM5LjE5MDMgMTE4Ljg3NyAxNy4xMTEgOTYuNzk4MUMtNC45NjgzMiA3NC43MTg3IC00Ljk2ODMyIDM4LjkyMTEgMTcuMTExIDE2Ljg0MTdaTTEwMC4wNjUgODguMjAwMUMxMTUuMzAyIDY3LjM4NjQgMTEzLjUxOCAzOC4wMDQxIDk0LjcxMTQgMTkuMTk3N0M3NC44Mzc1IC0wLjY3NjI0NSA0My4xNTI5IC0xLjU0MTE3IDIyLjI1MSAxNi42MDI5TDI0LjYwMDQgMjMuOTA1TDE2LjY4NDggMjIuMTk4N0MtMS4yNjU2OCA0My4xMDI0IC0wLjMzODMxIDc0LjYzNjkgMTkuNDY2OSA5NC40NDIxQzQwLjI0NTEgMTE1LjIyIDczLjkzMzIgMTE1LjIyIDk0LjcxMTQgOTQuNDQyMUM5NC43NDgyIDk0LjQwNTMgOTQuNzg1IDk0LjM2ODUgOTQuODIxNyA5NC4zMzE2TDkyLjEzOTEgODYuNzE4TDEwMC4wNjUgODguMjAwMVoiIGZpbGw9IndoaXRlIiBmaWxsLW9wYWNpdHk9IjAuNCIvPgo8L3N2Zz4=',
              // width: 58 * 2,
              // height: 113,
            },
            left: 'center',
            top: 'center',
          },
        ],
      },
    }
    return option
  }

  useEffect(() => {
    chartRef.current = echarts.init(pieDomRef.current)
    const option = getOption()
    option && chartRef.current.setOption(option)

    // window.addEventListener('resize', handleResize)

    resizeObserver.current = new ResizeObserver(handleResize)

    if (pieDomRef.current) {
      resizeObserver.current.observe(pieDomRef.current)
    }
    return () => {
      chartRef.current?.dispose()
      chartRef.current = null
    }
  }, [])

  return (
    <div
      className={cn(
        'relative ',
        'pr-[9px] custom-1920:pr-[18px] custom-2439:pr-[15px]',
        'w-[155px]  custom-1920:w-[175px] custom-2439:w-[203px]',
      )}
    >
      <div
        className={cn(
          ' absolute top-1 -left-1 z-10',
          'custom-2439:w-[60px] custom-2439:h-[60px] custom-1920:w-[50px] custom-1920:h-[50px] w-[46px] h-[46px]',
        )}
      >
        {usingPercentage <= 80 ? (
          <RiseSvg className="w-full h-full" />
        ) : (
          <DeclineSvg className="w-full h-full" />
        )}
      </div>
      <div ref={pieDomRef} className="w-full h-full"></div>
    </div>
  )
}

export default UsageMonitorRight
