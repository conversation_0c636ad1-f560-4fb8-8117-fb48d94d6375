import { useEffect, useRef } from 'react'
import * as echarts from 'echarts'
import type { EChartsType } from 'echarts'

import { cn } from '@/lib/utils'

const CircularProgress = ({
  data,
  className,
}: { data: echarts.PieSeriesOption['data']; className?: string }) => {
  const circularDomRef = useRef<HTMLDivElement>(null)
  const pieChart = useRef<EChartsType | null>(null)
  const resizeObserver = useRef<ResizeObserver | null>(null)

  const getOption = () => {
    const option = {
      tooltip: {
        show: false,
      },
      series: [
        {
          type: 'pie',
          radius: ['70%', '90%'],
          data: [
            {
              value: 30,
              name: '填充部分',
              itemStyle: {
                color: (data?.[1] as { itemStyle: { color: string } }).itemStyle
                  ?.color,
              },
            },
          ],
          label: {
            show: false,
          },
          emphasis: {
            scale: false,
            label: {
              show: false,
            },
            itemStyle: {
              shadowBlur: 0,
              shadowOffsetX: 0,
              shadowColor: 'transparent',
            },
          },
          animation: false,
        },
        {
          colorBy: 'data',
          name: 'Access From',
          type: 'pie',
          radius: ['70%', '90%'],
          avoidLabelOverlap: false,
          itemStyle: {
            opacity: 1,
          },
          label: {
            show: true,
            fontSize: 8,
            lineHeight: 7,
            position: 'center',
            color: '#fff',
            formatter: (
              // params: { dataIndex: number, value: number }
            ) => {
              let total = 0
              const allData = data as { value: number }[]
              allData?.forEach((item) => (total += item.value))
              return (
                (((allData?.[0]?.value ?? 0) / total) * 100).toFixed(0) + '%'
              )
              // return '';
            },
          },
          emphasis: {
            scale: false,
            itemStyle: {
              shadowBlur: 0,
              shadowOffsetX: 0,
              shadowColor: 'transparent',
            },
          },
          labelLine: {
            show: false,
          },
          data: data,
        },
      ],
    }
    return option
  }

  const handleResize = () => {
    pieChart.current?.resize()
  }

  useEffect(() => {
    pieChart.current = echarts.init(circularDomRef.current)
    const option = getOption()
    option && pieChart.current.setOption(option)

    // window.addEventListener('resize', handleResize)

    resizeObserver.current = new ResizeObserver(handleResize)

    if (circularDomRef.current) {
      resizeObserver.current.observe(circularDomRef.current)
    }

    return () => {
      pieChart.current?.dispose()
      pieChart.current = null
      resizeObserver.current?.disconnect()
    }
  }, [])
  return (
    <div ref={circularDomRef} className={cn('w-full h-full', className)}></div>
  )
}

export default CircularProgress
