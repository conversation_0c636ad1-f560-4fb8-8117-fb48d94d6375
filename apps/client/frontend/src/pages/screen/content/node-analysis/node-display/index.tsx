import { useMemo } from 'react'
import { SmallTitle } from '@/pages/screen/component/small-title'

import DelayTable from './delay-table'

import GenerallySvg from '@/assets/svg/screen/node-analysis/generally.svg'
import GoodSvg from '@/assets/svg/screen/node-analysis/good.svg'
import ExcellentSvg from '@/assets/svg/screen/node-analysis/excellent.svg'
import TimeoutSvg from '@/assets/svg/screen/node-analysis/timeout.svg'
import { cn } from '@/lib/utils'

import { useScreenContext } from '@/pages/screen/provider/ScreenContextProvider'

import { PROXY_QUALITY_TYPE, PROXY_STATUS_KEY } from '@/data/index'
import { countryCodeMap } from '@/pages/dashboard/data'

interface QualityProxyNumberType {
  [key: string]: number
}

export interface ProxyRegionDelayType {
  name: string
  country_name: string
  country_code: string
  delay: number
  // city_name: string,
  // protocol: string,
  // server: string,
  // port: number,
}

const NodeDisplay = () => {
  const { screenData } = useScreenContext()

  const qualityProxyNumber = useMemo(() => {
    return {
      [PROXY_STATUS_KEY.GENERALLY]: screenData?.high_quality_proxy_num ?? 0,
      [PROXY_STATUS_KEY.GOOD]: screenData?.mid_quality_proxy_num ?? 0,
      [PROXY_STATUS_KEY.EXCELLENT]: screenData?.low_quality_proxy_num ?? 0,
      [PROXY_STATUS_KEY.TIMEOUT]: screenData?.time_out_proxy_num ?? 0,
    }
  }, [screenData])

  const proxyRegionDelayList = useMemo(() => {
    const proxiesData = screenData?.proxies_data ?? []

    return proxiesData.map((item) => {
      return {
        name: item.Name,
        country_code: item.CountryCode.toUpperCase(),
        country_name: countryCodeMap[item.CountryCode.toUpperCase()] ?? '',
        delay: item.Delay,
      }
    })
  }, [])

  return (
    <div className="flex-1 w-full flex flex-col space-y-2 overflow-hidden">
      <SmallTitle title="节点数展示" />
      <div className="flex-1 overflow-hidden flex flex-col">
        <CategoryStatistics qualityProxyNum={qualityProxyNumber} />
        <DelayTable list={proxyRegionDelayList} />
      </div>
    </div>
  )
}

export default NodeDisplay

const CategoryStatistics = ({
  qualityProxyNum,
}: { qualityProxyNum: QualityProxyNumberType }) => {
  return (
    <div
      className={cn(
        'border-y border-y-[rgba(193,227,255,0.60)] flex justify-between',
        'py-2.5 px-0 custom-2439:py-3 custom-2439:px-2 ',
      )}
    >
      {Object.keys(qualityProxyNum).map((key, index) => {
        return (
          <div
            key={index}
            className={cn(
              'flex items-center space-x-[5px] py-[3px] border-r-[1px] border-r-[rgba(103,122,137,0.60)] pr-2.5',
              index === Object.keys(qualityProxyNum).length - 1 && 'border-r-0',
            )}
          >
            <img
              src={
                key === 'generally'
                  ? GenerallySvg
                  : key === 'good'
                    ? GoodSvg
                    : key === 'excellent'
                      ? ExcellentSvg
                      : TimeoutSvg
              }
              alt=""
              className={cn(
                'w-[33px] h-[33px] custom-1920:w-9 custom-1920:h-9 custom-2439:w-9 custom-2439:h-9 ',
              )}
            />
            <div className={cn('h-full flex flex-col justify-between')}>
              <div
                className={cn(
                  'flex items-center text-[#BBBEC1] text-[10px] space-x-[5px]',
                )}
              >
                <div>{PROXY_QUALITY_TYPE[key]}</div>
                {/*  key === 'generally' ? '优秀' : key === 'good' ? '良好' : key === 'excellent' ? '一般' : '超时' */}
                <div
                  className={cn(
                    'w-0 h-0 border-x-[6px] border-x-[transparent]',
                    key !== 'timeout' && 'border-b-8 ',
                    key === 'timeout' && 'border-t-8 ',
                    key === 'generally' && 'border-b-[rgba(0,247,0,0.70)]',
                    key === 'good' && 'border-b-[rgba(3,140,255,0.70)]',
                    key === 'excellent' && 'border-b-[rgba(254,134,24,0.70)]',
                    key === 'timeout' && 'border-t-[rgba(254,52,24,0.70)]',
                  )}
                ></div>
              </div>
              <div className="font-[Furore] text-lg leading-[18px]">
                {qualityProxyNum[key as keyof QualityProxyNumberType]}
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}
