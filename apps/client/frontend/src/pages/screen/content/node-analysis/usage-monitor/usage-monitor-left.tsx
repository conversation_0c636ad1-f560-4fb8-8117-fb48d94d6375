import { useMemo } from 'react'
import CircularProgress from '@/pages/screen/content/node-analysis/CircularProgress'

import { ProxyNumberType } from './index'

import { cn } from '@/lib/utils'

const UsageMonitorLeft = ({
  proxyNum,
  usingPercentage,
}: { proxyNum: ProxyNumberType; usingPercentage: number }) => {
  const progressData = useMemo(() => {
    return {
      using: [
        {
          name: '使用中',
          value: proxyNum.active_proxy_num,
          itemStyle: {
            color: '#038CFF',
            borderRadius: '5px',
          },
        },
        {
          name: '空闲中',
          value: proxyNum.idle_proxy_num,
          itemStyle: {
            color: '#414143',
          },
        },
      ],
      unused: [
        {
          name: '空闲中',
          value: proxyNum.idle_proxy_num,
          itemStyle: {
            color: '#fff',
          },
        },
        {
          name: '使用中',
          value: proxyNum.active_proxy_num,
          itemStyle: {
            color: '#414143',
            borderRadius: '5px',
          },
        },
      ],
    }
  }, [proxyNum])

  return (
    <div
      className={cn(
        'flex flex-col flex-1 space-y-[7px]',
        'py-[13px] custom-1920:py-4 custom-2439:py-[22px]',
      )}
    >
      <div
        className={cn(
          'border-y border-y-[rgba(193,227,255,0.60)] flex items-center',
          'py-[7px] custom-1920:py-2 custom-2439:py-2.5',
        )}
      >
        <div className="flex flex-col space-y-[7px] flex-1">
          <div className="text-[19px] custom-1920:text-[21px] custom-2439:text-[25px] font-[Furore] leading-[18px]">
            {proxyNum.active_proxy_num}
          </div>
          <div className="flex space-x-1.5 items-center">
            <div
              className={cn(
                `flex items-end w-1.5 h-6 border ${usingPercentage > 80 ? 'border-[#DC2626]' : 'border-[#038CFF]'}`,
                'w-[5px] h-6 custom-1920:w-[5px] custom-1920:h-6 custom-2439:w-1.5 custom-2439:h-6',
              )}
            >
              <div
                className={`w-full ${usingPercentage > 80 ? 'bg-[#DC2626]' : 'bg-[#038CFF]'}`}
                style={{ height: `${usingPercentage}%` }}
              ></div>
            </div>
            <div className="text-[12px]">
              <div>使用中</div>
              <div className="text-[8px]">节点数量</div>
            </div>
          </div>
        </div>
        <div className="w-[1px] h-[38px] bg-[rgba(255,255,255,0.40)] mx-2.5"></div>
        <div className="flex flex-col space-y-[7px] flex-1">
          <div className="text-[19px] custom-1920:text-[21px] custom-2439:text-[25px] font-[Furore] leading-[18px]">
            {proxyNum.idle_proxy_num}
          </div>
          <div className="flex space-x-1.5 items-center">
            <div
              className={cn(
                `flex items-end border border-[#fff]`,
                'w-[5px] h-6 custom-1920:w-[5px] custom-1920:h-6 custom-2439:w-1.5 custom-2439:h-6',
              )}
            >
              <div
                className={`w-full bg-[#fff]`}
                style={{ height: `${usingPercentage}%` }}
              ></div>
            </div>
            <div className="text-[12px]">
              <div>富余</div>
              <div className="text-[8px]">节点数量</div>
            </div>
          </div>
        </div>
      </div>
      <div className="text-[11px] flex flex-col space-y-[5px]">
        <div className="flex space-x-1 items-center">
          <div className=" bg-[rgba(255,255,255,0.04)]">
            <CircularProgress
              className="w-[27px] h-[27px] custom-1920:w-[32px] custom-1920:h-[32px] custom-2439:w-[36px] custom-2439:h-[36px]"
              data={progressData.using}
            />
          </div>
          <div
            className={cn(
              'flex flex-1 items-center bg-[rgba(255,255,255,0.04)] leading-5',
              'h-[27px] custom-1920:h-[32px] custom-2439:h-[36px]',
              'p-[5px] custom-2439:py-2 custom-2439:px-3',
            )}
          >
            <span>使用节点占比</span>
            <div className="flex-1 flex justify-end">
              {proxyNum.active_proxy_num}/
              <span className="text-[#038CFF]">
                {proxyNum.total_proxy_num}
              </span>{' '}
            </div>
          </div>
        </div>
        <div className="flex space-x-1 items-center">
          <CircularProgress
            className="w-[27px] h-[27px] custom-1920:w-[32px] custom-1920:h-[32px] custom-2439:w-[36px] custom-2439:h-[36px] bg-[rgba(255,255,255,0.04)]"
            data={progressData.unused}
          />
          <div
            className={cn(
              'flex flex-1 items-center bg-[rgba(255,255,255,0.04)] leading-5',
              'h-[27px] custom-1920:h-[32px] custom-2439:h-[36px]',
              'p-[5px] custom-2439:py-2 custom-2439:px-3',
            )}
          >
            <span className="pr-[5px]">富余节点占比</span>
            <div className="flex-1 flex justify-end">
              {proxyNum.idle_proxy_num}/
              <span className="text-[#038CFF]">{proxyNum.total_proxy_num}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UsageMonitorLeft
