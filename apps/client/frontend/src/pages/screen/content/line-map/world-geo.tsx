import { useEffect, useMemo, useRef } from "react";

import * as echarts from "echarts";
// import 'echarts-gl';
// import { useQueryClient } from "@tanstack/react-query";
import type { EChartsType } from "echarts";
import { useUserOutProxy } from "@/api/dashboard";

import { useScreenContext } from "@/pages/screen/provider/ScreenContextProvider";
import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";
import worldGeoJson from "@/assets/echarts-map/json/world.json";
import {
	geoCoordMap,
	countryNameMap,
	countryCodeMap,
} from "@/pages/dashboard/data";
import { getUrl } from "@/lib/utils";
import { startCountry } from "@/pages/data";

const planePathImg =
	"image://data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI2NiIgaGVpZ2h0PSIxMDMiIHZpZXdCb3g9IjAgMCA2NiAxMDMiIGZpbGw9Im5vbmUiPgogIDxnIGZpbHRlcj0idXJsKCNmaWx0ZXIwX2ZfMjY2NF8zMTg2MikiPgogICAgPHBhdGggZD0iTTMzLjY0MDYgOTEuMzMyMUw1My4wODc1IDQxLjEwMTVDNTQuMzY3NyAzNy43OTQ4IDU1LjIyOCAzNC4zMDI4IDU0Ljc4NiAzMC43ODQ2QzUzLjg2NTQgMjMuNDU2MyA0OS44OTggMTEuMTY0NCAzMy42NDA5IDExLjE2NDRDMTguNjIgMTEuMTY0NCAxMy4zNDA3IDIxLjY1ODIgMTEuNDg1MSAyOS4wMzQ3QzEwLjMzMDcgMzMuNjI0MiAxMS40MjIzIDM4LjM2MjIgMTMuMjUyOCA0Mi43MjYzTDMzLjY0MDYgOTEuMzMyMVoiIGZpbGw9InVybCgjcGFpbnQwX2xpbmVhcl8yNjY0XzMxODYyKSIvPgogIDwvZz4KICA8ZGVmcz4KICAgIDxmaWx0ZXIgaWQ9ImZpbHRlcjBfZl8yNjY0XzMxODYyIiB4PSIwLjMyMTY3NSIgeT0iMC40NzU3MjgiIHdpZHRoPSI2NS4yNzA1IiBoZWlnaHQ9IjEwMS41NDUiIGZpbHRlclVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgY29sb3ItaW50ZXJwb2xhdGlvbi1maWx0ZXJzPSJzUkdCIj4KICAgICAgPGZlRmxvb2QgZmxvb2Qtb3BhY2l0eT0iMCIgcmVzdWx0PSJCYWNrZ3JvdW5kSW1hZ2VGaXgiLz4KICAgICAgPGZlQmxlbmQgbW9kZT0ibm9ybWFsIiBpbj0iU291cmNlR3JhcGhpYyIgaW4yPSJCYWNrZ3JvdW5kSW1hZ2VGaXgiIHJlc3VsdD0ic2hhcGUiLz4KICAgICAgPGZlR2F1c3NpYW5CbHVyIHN0ZERldmlhdGlvbj0iNS4zNDQ0MSIgcmVzdWx0PSJlZmZlY3QxX2ZvcmVncm91bmRCbHVyXzI2NjRfMzE4NjIiLz4KICAgIDwvZmlsdGVyPgogICAgPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzI2NjRfMzE4NjIiIHgxPSIzNC43MTIzIiB5MT0iMTEiIHgyPSIzNC43MTIzIiB5Mj0iODciIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj4KICAgICAgPHN0b3Agc3RvcC1jb2xvcj0iIzA5RTYwQiIvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiNGRkM3MDAiLz4KICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgPC9kZWZzPgo8L3N2Zz4=";

interface LinesItemType {
	name: string;
	country_code: string;
	value: number[];
}

type LinesDataType = [LinesItemType, LinesItemType];

type LinesType = [string, LinesDataType[]];

export const WorldGeo = () => {
	const { globalInfo } = useGlobalInfoContext();
	// const queryClient = useQueryClient()
	const { screenData } = useScreenContext();
	const { data: ipData, refetch: refetchIp } = useUserOutProxy();

	const proxyGeoRef = useRef<EChartsType | null>(null);
	const preMainToData = useRef<{ country_code: string }[]>([]);

	const isWg = useMemo(() => false, [globalInfo]); // WG functionality removed

	const mainToData = useMemo(() => {
		console.log(screenData,'screenData')
		const proxiesList = screenData?.proxy_info?.proxies ?? [];
		const data: (typeof startCountry)[] = [];
		proxiesList.forEach((item) => {
			data.push({ country_code: item.country_code });
		});
		return data;
	}, [screenData]);

	const getLineItem = (
		preCode: string,
		nextCode: string
	): [LinesItemType, LinesItemType] => {
		return [
			{
				name: countryCodeMap[preCode] ?? "",
				value: geoCoordMap[preCode] ?? [],
				country_code: preCode,
			},
			{
				name: countryCodeMap[nextCode] ?? "",
				value: geoCoordMap[nextCode] ?? [],
				country_code: nextCode,
			},
		];
	};

	// 适配连线数据格式
	const getLine = () => {
		// 实现数据处理
		const solidData: LinesType[] = [[startCountry.country_code, []]];
		mainToData.forEach((item, index) => {
			if (index === mainToData.length - 1) return;
			const countryCode = item.country_code.toUpperCase();
			const nextCountryCode =
				mainToData[index + 1]?.country_code.toUpperCase() ?? "";
			solidData[0]?.[1].push(getLineItem(countryCode, nextCountryCode));
		});

		// 虚线数据处理
		const pathList =
			screenData?.path_list?.filter(
				(v) => v.name !== screenData?.proxy_info?.name
			) ?? [];
		const otherLineList = pathList.map((v) => {
			const datas: LinesType[] = [["", []]];
			datas[0] && (datas[0][0] = startCountry.country_code);
			const lines = [
				startCountry.country_code,
				...v.proxies_code.map((v) => v.toUpperCase()),
			];
			lines.forEach((countryCode, index) => {
				if (index === lines.length - 1) return;
				const nextLine = lines[index + 1] ?? "";
				const value: [LinesItemType, LinesItemType] = [
					{
						name: countryCodeMap[countryCode] ?? "",
						value: geoCoordMap[countryCode] ?? [],
						country_code: countryCode,
					},
					{
						name: countryCodeMap[nextLine] ?? "",
						value: geoCoordMap[nextLine] ?? [],
						country_code: nextLine,
					},
				];
				datas[0] && datas[0][1].push(value);
			});

			return datas;
		});

		return {
			solidData,
			otherLineList,
		};
	};

	// 获取连线经纬度数据
	const convertData = (data: LinesDataType[]) => {
		const res = [];
		for (let index = 0; index < data.length; index++) {
			const dataIndex = data[index];
			const fromCoord = geoCoordMap[dataIndex?.[0]?.country_code ?? ""];
			const toCoord = geoCoordMap[dataIndex?.[1]?.country_code ?? ""];
			if (fromCoord && toCoord) {
				res.push([fromCoord, toCoord]);
			}
		}
		return res;
	};

	const getExitNode = (lastExit: LinesItemType) => {
		return {
			type: "effectScatter",
			coordinateSystem: "geo",
			zlevel: 3,
			// color: '#D7E979',
			color: "rgba(255, 254, 250, 0.6)",
			symbol: "circle",
			symbolSize: 5,
			rippleEffect: {
				period: 8, //动画时间，值越小速度越快
				brushType: "fill", //波纹绘制方式 stroke, fill
				scale: 8, //波纹圆环最大限制，值越大波纹越大
			},
			label: {
				show: false,
			},
			data: lastExit
				? [lastExit].map((v) => {
						return {
							name: v.name,
							value: v.value,
							datas: {
								country_code: v.country_code,
							},
						};
					})
				: [],
		} as echarts.SeriesOption;
	};

	// 连线 series
	const getLianData = (series: echarts.SeriesOption[]) => {
		if (isWg) {
			const lastExit = {
				name: ipData?.country ?? "",
				country_code: ipData?.country_code ?? "",
				value: geoCoordMap[ipData?.country_code ?? ""] ?? [],
			};
			return series.push(getExitNode(lastExit));
		}
		const { solidData, otherLineList } = getLine();
		solidData.forEach((item) => {
			const lastExit = item[1]?.[item[1].length - 1]?.[1] ?? null;
			const exitNode = getExitNode(lastExit);
			series.push(
				{
					name: item[0],
					type: "lines",
					zlevel: 1,
					label: {
						show: false,
					},
					// 飞行线特效
					effect: {
						show: true, // 是否显示
						period: 4, // 特效动画时间
						trailLength: 0.6, // 特效尾迹长度。取从 0 到 1 的值，数值越大尾迹越长
						// symbol: planePath, // 特效图形标记
						// symbolSize: 30
						symbol: planePathImg, // 特效图形标记
						symbolSize: [10, 20],
					},
					// 线条样式
					lineStyle: {
						curveness: -0.4, // 飞线弧度
						type: "solid", // 飞线类型
						color: "#F0FFA2", // 飞线颜色
						width: 1.5, // 飞线宽度
						opacity: 0.1,
					},
					data: convertData(item[1]) as echarts.LinesSeriesOption["data"],
				},
				{
					type: "effectScatter",
					coordinateSystem: "geo",
					zlevel: 3,
					color: "rgba(255, 254, 250, 0.6)",
					symbol: "circle",
					symbolSize: 4,
					rippleEffect: {
						period: 8, //动画时间，值越小速度越快
						brushType: "fill", //波纹绘制方式 stroke, fill
						scale: 10, //波纹圆环最大限制，值越大波纹越大
					},
					label: {
						show: false,
					},
					// 这里用来组装自定义数据，以便在tooltip中取得。
					data: item[1].map((dataItem: LinesDataType) => {
						return {
							name: dataItem[0].name,
							value: geoCoordMap[dataItem[0].country_code],
							datas: {
								country_code: dataItem[0].country_code,
							},
						};
					}),
				},
				// 出口节点
				exitNode
			);
		});
		otherLineList.forEach((line) => {
			line.forEach((item) => {
				const lastExit = item[1]?.[item[1].length - 1]?.[1] ?? null;
				series.push(
					{
						name: item[0],
						type: "lines",
						zlevel: 1,
						label: {
							show: false,
						},
						// 线条样式
						lineStyle: {
							curveness: -0.4, // 飞线弧度
							type: [5, 5], // 飞线类型
							color: "#F0FFA2", // 飞线颜色
							width: 0.5, // 飞线宽度
							opacity: 0.6,
						},
						data: convertData(item[1]) as echarts.LinesSeriesOption["data"],
					},
					{
						type: "effectScatter",
						coordinateSystem: "geo",
						zlevel: 3,
						color: "rgba(215, 233, 121, 0.3)",
						symbol: "circle",
						symbolSize: 4,
						rippleEffect: {
							period: 4, //动画时间，值越小速度越快
							brushType: "fill", //波纹绘制方式 stroke, fill
							scale: 4, //波纹圆环最大限制，值越大波纹越大
						},
						label: {
							show: false,
						},
						// 这里用来组装自定义数据，以便在tooltip中取得。
						data: item[1].map((dataItem: LinesDataType) => {
							return {
								name: dataItem[0].name,
								value: geoCoordMap[dataItem[0].country_code],
								datas: {
									country_code: dataItem[0].country_code,
								},
							};
						}),
					},
					// 被攻击点1
					{
						type: "effectScatter",
						coordinateSystem: "geo",
						zlevel: 3,
						color: "rgba(215, 233, 121, 0.3)",
						symbol: "circle",
						symbolSize: 4,
						rippleEffect: {
							period: 4, //动画时间，值越小速度越快
							brushType: "fill", //波纹绘制方式 stroke, fill
							scale: 4, //波纹圆环最大限制，值越大波纹越大
						},
						label: {
							show: false,
						},
						data: lastExit
							? [lastExit].map((v) => {
									return {
										name: v.name,
										value: v.value,
										datas: {
											country_code: v.country_code,
										},
									};
								})
							: [],
					}
				);
			});
		});

		return true;
	};

	// 主线每个节点tip竖线的经纬度
	const mianLineData = (data: typeof mainToData) => {
		return (
			data
				.map((item) => {
					const countryCode = item.country_code.toUpperCase();
					const coords = geoCoordMap[countryCode] as
						| [number, number]
						| undefined;
					if (!coords) return null;
					return {
						name: countryCodeMap[countryCode],
						coords: [coords, [coords[0], coords[1] + 4]],
						value: countryCode,
					};
				})
				.filter((v) => !!v) ?? []
		);
	};

	// 主线tip series
	const getMianLineTipData = (series: echarts.SeriesOption[] = []) => {
		const url = getUrl();
		const rich = Object.keys(countryCodeMap).reduce(
			(object, code) => {
				object[code] = {
					color: "transparent",
					height: 20,
					width: 20,
					align: "left",
					backgroundColor: {
						image: `${url}/res/flag3/${code.toLowerCase()}.svg`, // 动态生成国旗图标
					},
				};
				return object;
			},
			{} as { [key: string]: { [key: string]: number | string | unknown } }
		);

		series.push(
			// 柱状体的主干
			{
				name: "solidTip",
				type: "lines",
				zlevel: 5,
				effect: {
					show: false,
					// period: 4, //箭头指向速度，值越小速度越快
					// trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
					// symbol: 'arrow', //箭头图标
					// symbol: imgDatUrl,
					symbolSize: 5, // 图标大小
				},
				lineStyle: {
					width: 2, // 尾迹线条宽度
					color: "#F0FFA2",
					opacity: 1, // 尾迹线条透明度
					curveness: 0, // 尾迹线条曲直度
				},
				label: {
					show: true,
					position: "end",
					color: "#fff",
					formatter: (parameters) => {
						return `{left|} {gap1|}{${parameters.value}|}{gap2|}{name|${parameters.name}}{gap3|}{right|}`;
					},
					rich: {
						left: {
							color: "transparent",
							height: 35,
							width: 8,
							align: "center",
							backgroundColor: {
								image: `data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMSIgaGVpZ2h0PSI1OCIgdmlld0JveD0iMCAwIDExIDU4IiBmaWxsPSJub25lIj4KPHBhdGggZD0iTTExIDU2LjA4ODRIMVY0Ni4wODg0IiBzdHJva2U9IiNGRkZERDMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLW1pdGVybGltaXQ9IjEwIi8+CjxwYXRoIGQ9Ik0xIDExVjFIMTEiIHN0cm9rZT0iI0ZGRkREMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbWl0ZXJsaW1pdD0iMTAiLz4KPHBhdGggZD0iTTguNzI5NDkgMTlWMzkiIHN0cm9rZT0iI0ZGRkREMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbWl0ZXJsaW1pdD0iMTAiLz4KPC9zdmc+`, // 动态生成国旗图标
							},
						},
						gap1: {
							height: 35,
							width: 8,
						},
						...rich,
						gap2: {
							height: 35,
							width: 6,
						},
						name: {
							color: "#fff",
							align: "center",
							lineHeight: 35,
							fontSize: 14,
							padding: [2, 0, 0, 0],
						},
						gap3: {
							height: 35,
							width: 8,
						},
						right: {
							color: "transparent",
							height: 35,
							width: 8,
							align: "center",
							backgroundColor: {
								image: `data:image/svg+xml;charset=utf-8;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxNCIgaGVpZ2h0PSI1OCIgdmlld0JveD0iMCAwIDE0IDU4IiBmaWxsPSJub25lIj4KPHBhdGggZD0iTTEyLjczMDIgNDYuMDQzOVY1Ni4wNDM5SDIuNzMwMjIiIHN0cm9rZT0iI0ZGRkREMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbWl0ZXJsaW1pdD0iMTAiLz4KPHBhdGggZD0iTTIuNzMwMjIgMS4wNDM5NUgxMi43MzAyVjExLjA0MzkiIHN0cm9rZT0iI0ZGRkREMyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbWl0ZXJsaW1pdD0iMTAiLz4KPHBhdGggZD0iTTEgMTkuMDQzOVYzOS4wNDM5IiBzdHJva2U9IiNGRkZERDMiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLW1pdGVybGltaXQ9IjEwIi8+Cjwvc3ZnPg==`, // 动态生成国旗图标
							},
						},
					},
					backgroundColor: "#080A00",
				},
				silent: true,
				data: mianLineData(
					isWg ? [{ country_code: ipData?.country_code ?? "" }] : mainToData
				),
			}
		);
	};

	const isCN = (code: string) => {
		return ["HK", "MO", "TW", "CN"].includes(code.toUpperCase());
	};

	const getRegions = () => {
		const codeList: string[] = [];
		const regionsData = isWg
			? [{ country_code: ipData?.country_code ?? "" }]
			: mainToData;
		regionsData.forEach((item) =>
			codeList.push(
				isCN(item.country_code) ? "CN" : item.country_code.toUpperCase()
			)
		);
		const regions = codeList.map((item) => {
			return {
				name: countryCodeMap[item], // 中国
				itemStyle: {
					color: "#0b6f00",
					areaColor: "#0b6f00",
					borderColor: "#00F700", // 边框颜色
					borderWidth: 1.2, // 边框宽度
				},
			};
		});
		return regions;
	};

	const getOption = () => {
		const series: echarts.SeriesOption[] = [];
		getLianData(series);
		getMianLineTipData(series);

		const regions = getRegions();

		const option = {
			backgroundColor: "transparent",
			// 底图样式
			geo: {
				map: "world", // 地图类型
				roam: true, // 是否开启缩放
				zoom: 1.2, // 初始缩放大小
				// center: [11.3316626, 19.5845024], // 地图中心点
				layoutCenter: ["50%", "50%"], //地图位置
				scaleLimit: {
					// 缩放等级
					min: 1,
					max: 3,
				},
				label: {
					show: false,
				},
				nameMap: countryNameMap, // 自定义地区的名称映射
				// 三维地理坐标系样式
				itemStyle: {
					areaColor: "#171E25",
					borderColor: "#038CFF", // 边框颜色
					borderWidth: 1.2, // 边框宽度
				},
				emphasis: {
					itemStyle: {
						areaColor: "#0b6f00",
						borderColor: "#00F700", // 边框颜色
						borderWidth: 1.2, // 边框宽度
					},
					label: false,
				},
				tooltip: {
					show: true,
					trigger: "item",
					triggerOn: "click", // 提示框触发的条件
					enterable: true, // 鼠标是否可进入提示框浮层中，默认为false，如需详情内交互，如添加链接，按钮，可设置为 true
					backgroundColor: "rgba(0,0,0,0.8)",
					borderColor: "rgba(0,0,0,0.2)",
					textStyle: {
						color: "#fff",
					},
					formatter: (parameters: {
						name: string;
						data:
							| { name: string; datas: { tradingCountry: string } }
							| undefined;
					}) => {
						if (parameters.data?.name) return parameters.data.name;
						return parameters.name;
					},
				},
				regions,
			},

			series: series,
			tooltip: {
				show: true,
				enterable: true, // 鼠标是否可进入提示框浮层中，默认为false，如需详情内交互，如添加链接，按钮，可设置为 true
			},
		};

		return option;
	};

	const handleResize = () => {
		proxyGeoRef.current?.resize();
	};

	useEffect(() => {
		preMainToData.current?.some(
			(item, index) => item.country_code !== mainToData[index]?.country_code
		) && proxyGeoRef.current?.clear();
		preMainToData.current = mainToData;
		const option = getOption();
		proxyGeoRef.current?.setOption(option);
	}, [screenData, mainToData, isWg, ipData]);

	useEffect(() => {
		refetchIp();
		const chartDom = document.getElementById("screenGeo");
		proxyGeoRef.current = echarts.init(chartDom);
		echarts.registerMap(
			"world",
			worldGeoJson as unknown as Parameters<typeof echarts.registerMap>[1]
		);

		const option = getOption();
		option && proxyGeoRef.current?.setOption(option);

		// 页面resize时触发
		window.addEventListener("resize", handleResize);

		return () => {
			window.removeEventListener("resize", handleResize);
			proxyGeoRef.current?.dispose();
			proxyGeoRef.current = null;
		};
	}, []);

	return (
		<div className="flex-1 h-full flex flex-col">
			<div id="screenGeo" className="flex-1"></div>
		</div>
	);
};
