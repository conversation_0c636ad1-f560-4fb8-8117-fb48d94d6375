import { useMemo } from 'react'

import { WorldGeo } from './world-geo'
import { dateFormat, cn } from '@/lib/utils'
import MapRulerSvg from '@/assets/svg/screen/line-map/map-ruler.svg?react'

import { useWsTrafficContext } from '@/provider/WsTrafficContextProvider'

import packageJson from 'package.json'

interface DeviceType {
  cpu: string
  memory: string
}

const LineMap = () => {
  const { wsTrafficData } = useWsTrafficContext()

  const deviceData = useMemo(() => {
    const newData = wsTrafficData[wsTrafficData.length - 1] ?? null
    return {
      cpu: newData?.cpu?.toFixed(2) ?? '0',
      memory: newData?.memory?.toFixed(2) ?? '0',
    }
  }, [wsTrafficData])

  return (
    <div
      className={cn(
        'line-map flex-1 flex ml-[9px] mr-[18px] py-4 overflow-hidden relative',
        'pr-[11px] custom-1920:pr-[22px] custom-2439:pr-[22px] ',
      )}
    >
      <LineMapLeft />
      <WorldGeo />
      <LineMapRight device={deviceData} />
    </div>
  )
}

const LineMapLeft = () => {
  return (
    <div className="flex h-full overflow-hidden">
      <MapRulerSvg className="w-[29px] h-full" />
    </div>
  )
}

const LineMapRight = ({ device }: { device: DeviceType }) => {
  const unit = 100 / 8

  // Math.ceil(parseInt(device.cpu) / unit)

  return (
    <div
      className={cn(
        'h-full absolute right-[22px] top-0 flex flex-col justify-between pt-[22px] pb-[26px] text-right leading-[14px]',
        ' right-[11px] custom-1920:right-[22px] custom-2439:right-[22px]',
        ' text-[8px] custom-1920:text-[9px] custom-2439:text-[10px]',
        ' pt-[11px] pb-[15px] custom-1920:pt-[13px] custom-1920:pb-[20px] custom-2439:pt-[22px] custom-2439:pb-[26px]',
      )}
    >
      <div className="flex flex-col justify-end items-end space-y-2.5">
        <div className="w-4 h-1 bg-[#018cff]"></div>
        <div>
          <div>Time</div>
          <div>{dateFormat(new Date(), 'YYYY/MM/DD')}</div>
        </div>
        <div>Basic Map Database</div>
        <div>V{packageJson.version.split('-')[0]}</div>
      </div>
      <div className="flex flex-col justify-end items-end space-y-2.5 bg-[transparent]">
        <div className="w-4 h-1 bg-[#018cff]"></div>
        {/* <div className='flex flex-col space-y-[2.5px] bg-[transparent]'>
                <div>分辨率</div>
                <div>1920*1080</div>
            </div> */}
        <div className="flex flex-col space-y-[2.5px]">
          <div>CPU</div>
          <div className="w-[59px] h-5 border border-[#808082] flex flex-row-reverse items-center px-[2px]">
            {Array.from(
              { length: Math.max(Math.ceil(parseInt(device.cpu) / unit), 1) },
              (_, index) => index + 1,
            ).map((item) => {
              return (
                <div
                  key={item}
                  className={`bg-[#FE8618] w-1.5 h-[15px] ${item !== 1 ? 'mr-[1px]' : ''}`}
                  style={{ opacity: (0.7 - item * 0.07).toFixed(2) }}
                ></div>
              )
            })}
          </div>
        </div>
        <div className="flex flex-col space-y-[2.5px]">
          <div>内存</div>
          <div className="w-[59px] h-5 border border-[#808082] flex flex-row-reverse items-center px-[2px]">
            {Array.from(
              {
                length: Math.max(Math.ceil(parseInt(device.memory) / unit), 1),
              },
              (_, index) => index + 1,
            ).map((item) => {
              return (
                <div
                  key={item}
                  className={`bg-[#FFF] w-1.5 h-[15px] ${item !== 1 ? 'mr-[1px]' : ''}`}
                  style={{ opacity: (0.7 - item * 0.07).toFixed(2) }}
                ></div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

export default LineMap
