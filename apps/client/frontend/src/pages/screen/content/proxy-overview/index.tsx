import OnlineDevices from './online-devices'
import NodesDistribution from './nodes-distribution'

import { BigTitle } from '@/pages/screen/component/small-title'
import { cn } from '@/lib/utils'

const ProxyOverview = () => {
  return (
    <div
      className={cn(
        'proxy-overview aside-custom node-analysisw flex flex-col space-y-2 h-full overflow-hidden',
        'w-[352px] custom-1920:w-[398px] custom-2439:w-[489px]',
      )}
    >
      <BigTitle title="数据总览" />
      <OnlineDevices />
      <NodesDistribution />
    </div>
  )
}

export default ProxyOverview
