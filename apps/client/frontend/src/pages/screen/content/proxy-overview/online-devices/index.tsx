import { SmallTitle } from '@/pages/screen/component/small-title'
import DeviceTotalImg from '@/assets/image/screen/device-total.png'

import { useScreenContext } from '@/pages/screen/provider/ScreenContextProvider'

// import DeviceProgressSvg from '@/assets/svg/screen/proxy-overview/device-progress.svg'
import { cn } from '@/lib/utils'

const OnlineDevices = () => {
  const { screenData } = useScreenContext()

  const { active_proxy_num, idle_proxy_num, total_proxy_num } = screenData || {
    active_proxy_num: 0,
    idle_proxy_num: 0,
    total_proxy_num: 0,
  }
  const activeRatio = total_proxy_num ? active_proxy_num / total_proxy_num : 0
  const idleRatio = total_proxy_num ? idle_proxy_num / total_proxy_num : 0

  return (
    <div className="online-devices w-full">
      <SmallTitle title="代理" />
      <div
        className={cn(
          ' bg-[rgba(0,0,0,0.40)] py-4 px-3 flex items-center ',
          'py-3 px-2 custom-1920:py-[9px] custom-1920:px-[13px] custom-2439:py-4 custom-2439:px-3',
        )}
      >
        <img
          src={DeviceTotalImg}
          alt=""
          className={cn(
            'w-[147px] h-[147px] custom-1920:w-[166px] custom-1920:h-[166px] custom-2439:w-[204px] custom-2439:h-[204px]',
          )}
        />
        <div className="ml-[15px] max-w-[209px] flex-1">
          <div className="text-2xl custom-1920:text-[20px] custom-2439:text-[17px] font-bold">
            代理总数
          </div>
          <div className="text-[11px] custom-2439:text-sm flex items-baseline mb-[36px] custom-1920:mb-[40px] custom-2439:mb-[50px]">
            <div
              className={cn(
                ' font-[Furore] text-[60px] leading-[42px]',
                ' text-[43px] leading-[30px] custom-1920:text-[48px] custom-1920:leading-[34px] custom-2439:text-[60px] custom-2439:leading-[42px] ',
              )}
            >
              {total_proxy_num}
            </div>
            <span>个</span>
          </div>
          <div className="flex space-x-2 items-center mb-1 custom-2439:mb-2 text-[11px] custom-2439:text-[12px]">
            <div>活跃</div>
            <div className="flex-1 flex space-x-1 items-center">
              <div className="flex-1 bg-[#151516] h-[6px]">
                <div
                  className="device-progress h-full"
                  style={{ width: `${activeRatio * 100}%` }}
                ></div>
              </div>
              <div className="font-[Furore] w-[30px]">{active_proxy_num}</div>
            </div>
          </div>
          <div className="flex space-x-2 items-center  mb-1 custom-2439:mb-2 text-[11px] custom-2439:text-[12px]">
            <div>闲置</div>
            <div className="flex-1 flex space-x-1 items-center">
              <div className="flex-1 bg-[#151516] h-[6px]">
                <div
                  className="device-progress h-full"
                  style={{ width: `${idleRatio * 100}%` }}
                ></div>
              </div>
              <div className="font-[Furore] w-[30px]">{idle_proxy_num}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OnlineDevices
