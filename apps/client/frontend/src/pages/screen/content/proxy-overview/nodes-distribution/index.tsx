import { useEffect, useRef, useState } from 'react'
import * as echarts from 'echarts'
import { SmallTitle } from '@/pages/screen/component/small-title'

import { countryCodeMap } from '@/pages/dashboard/data'
import { useScreenContext } from '@/pages/screen/provider/ScreenContextProvider'

import type { EChartsType, EChartsCoreOption } from 'echarts'

import { getUrl } from '@/lib/utils'

type DataZoomType = Array<{
  start: number
  startValue: number
  end: number
  endValue: number
}>
type YAxisType = Array<{ data: Array<string> }>

const NodesDistribution = () => {
  const url = getUrl()
  const { screenData } = useScreenContext()

  const outDomRef = useRef<HTMLDivElement | null>(null)
  const innerRef = useRef<HTMLDivElement | null>(null)
  // const barChart = useRef<EChartsType>();
  const [barChart, setBarChart] = useState<EChartsType | null>(null)
  const scrollTimer = useRef<NodeJS.Timeout | null>(null)

  const dataZoomNumber = useRef(10)

  const getBarOption = () => {
    const { proxy_region_map } = screenData ?? {}
    const categories = Object.keys(proxy_region_map ?? {}) || []
    const data = proxy_region_map
      ? categories.map((key) => proxy_region_map[key])
      : []
    // const categories = ['BV', 'BW', 'BY', 'BZ', 'GS', 'CA', 'CD', 'US', 'AD', 'AS', 'BA', 'BM', 'BN', 'BO', 'BS', 'BT', 'BF', 'CK',]
    // const data = [100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200, 210, 220, 230, 240, 250, 260, 260]

    const rich = Object.keys(countryCodeMap).reduce(
      (object, code) => {
        object[code] = {
          color: 'transparent',
          height: 15,
          width: 20,
          align: 'left',
          backgroundColor: {
            image: `${url}/res/flag3/${code.toLowerCase()}.svg`, // 动态生成国旗图标
          },
        }
        return object
      },
      {} as { [key: string]: { [key: string]: number | string | unknown } },
    )

    const option = {
      grid: {
        top: 22,
        left: 14,
        right: 22,
        bottom: 22,
        containLabel: false,
      },
      animation: true,
      animationDurationUpdate: 800,
      dataZoom: [
        {
          type: 'inside', // 内部滚动
          startValue: 0,
          endValue: dataZoomNumber.current - 1,
          yAxisIndex: 0,
          throttle: 500, // 事件触发频率
          zoomOnMouseWheel: false, // 禁用鼠标滚轮缩放
          moveOnMouseMove: false,
          moveOnMouseWheel: false,
          preventDefaultMouseMove: true, // 防止默认的鼠标移动事件
        },
      ],
      backgroundColor: '#000', // 背景颜色
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}', // 提示框格式
      },
      xAxis: {
        // show:false,
        type: 'value',
        axisLine: { show: false }, // 不显示 x 轴线
        axisTick: { show: false }, // 不显示 x 轴刻度
        splitLine: {
          show: true,
          lineStyle: { color: 'rgba(232, 232, 232, 0.1)' },
        }, // 分隔线样式
        axisLabel: {
          show: false,
          color: '#fff', // x 轴标签颜色
        },
        // boundaryGap: true
        boundaryGap: false,
      },
      yAxis: {
        type: 'category',
        data: categories.map((item) => item.toUpperCase()),
        inverse: true,
        axisLine: { show: false }, // 不显示 y 轴线
        axisTick: { show: false }, // 不显示 y 轴刻度
        // axisLine: {onZero: false},
        left: 0,
        boundaryGap: false,
        axisLabel: {
          lineHeight: 20,
          // width: 73,
          overflow: 'breakAll',
          color: '#fff', // y 轴标签颜色
          align: 'left', // 左对齐
          formatter: (value: string) => {
            const countryName = countryCodeMap[value]
            return `{${value}|} {name|${formatLabel(countryName ?? '')}}`
          },
          rich: {
            ...rich,
            name: {
              color: '#fff',
              align: 'left',
              // padding: [0, 0, 0, 5]
            },
          },
        },
        nameLocation: 'middle', // Y轴名称位置
        nameGap: 30, // Y轴名称与轴线的距离
        splitLine: {
          show: false,
          lineStyle: { color: 'rgba(232, 232, 232, 0.1)' },
        }, // 分隔线样式
      },
      series: [
        {
          name: '数量',
          type: 'bar',
          data,
          itemStyle: {
            color: '#1E1E1E', // 柱状图颜色
            enabled: true,
            decal: {
              show: true,
              symbol: [
                'image://data:image/svg+xml;charset=utf-8;base64,PHN2ZyB3aWR0aD0iNCIgaGVpZ2h0PSI2IiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgogICAgPHJlY3Qgd2lkdGg9IjEuNSIgaGVpZ2h0PSI2IiBmaWxsPSIjMDM4Q0ZGIiAvPgogICAgPHJlY3QgeD0iMS41IiB3aWR0aD0iMi41IiBoZWlnaHQ9IjYiIGZpbGw9IiMxMjJkNDUiIC8+Cjwvc3ZnPg==',
              ],
              dashArrayX: [5, 0],
              dashArrayY: [1, 0],
              rotation: 0,
            },
          },
          label: {
            show: true,
            position: 'right',
            formatter: '{c}', // 显示数值
            color: '#fff', // 标签颜色
            offset: [-2, 1],
          },
          barWidth: 12, // 柱宽

          showBackground: true,
          backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)',
          },
        },
      ],
      aria: {
        enabled: true,
        decal: {
          show: true,
        },
      },
    }

    return option
  }

  // 处理换行
  const formatLabel = (value: string) => {
    let maxLabelWidth = 4
    // 如果标签长度超过最大宽度，则进行换行
    if (value?.length > maxLabelWidth) {
      const parts = []
      let currentLine = ''

      // 遍历每个字符，判断是否需要换行
      for (const char of value) {
        if ((currentLine + char).length > maxLabelWidth) {
          parts.push(currentLine)
          currentLine = char // 新的一行
          maxLabelWidth = 100
        } else {
          currentLine += char // 添加字符
        }
      }

      // 添加最后一行
      if (currentLine) {
        parts.push(currentLine)
      }

      return parts.join('\n') // 用换行符连接
    }
    return value // 如果没有超出最大宽度，直接返回
  }

  // 获取当前显示区域的Y轴数据, 并计算label宽度
  const getVisibleYAxisData = () => {
    if (!barChart?.getOption()) return
    const yAxisData =
      (barChart.getOption()['yAxis'] as YAxisType)[0]?.data ?? [] // 获取所有Y轴数据
    const dataZoom = barChart.getOption()['dataZoom'] as DataZoomType // 获取dataZoom信息
    const visibleData = []

    // 处理dataZoom的范围
    const startIndex = dataZoom[0]?.startValue ?? 0 // dataZoom的起始百分比
    const endIndex = (dataZoom[0]?.endValue ?? 0) + 1 // dataZoom的结束百分比

    // 获取当前可视区域的Y轴数据
    for (let index = startIndex; index < endIndex; index++) {
      visibleData.push(countryCodeMap[yAxisData[index] ?? ''])
    }

    // 创建一个临时的DOM元素来测量标签宽度
    const temporaryElement = document.createElement('div')
    temporaryElement.style.position = 'absolute'
    temporaryElement.style.visibility = 'hidden'
    temporaryElement.style.whiteSpace = 'nowrap'
    temporaryElement.style.fontSize = '12px'
    document.body.appendChild(temporaryElement)

    // 计算最长标签的宽度
    const longestLabelWidth = Math.max(
      ...visibleData.map((label) => {
        temporaryElement.innerHTML = `<img style="height: 13px; width: 20px; vertical-align: middle;" /> ${label?.slice(0, 4)}`
        return temporaryElement.offsetWidth // 获取宽度
      }),
    )

    // 清理临时元素
    document.body.removeChild(temporaryElement)

    const width = Math.min(longestLabelWidth, 73)
    barChart.setOption({
      grid: {
        left: width + 45,
      },
      yAxis: {
        offset: width + 23,
      },
    })
  }

  /**
   * 数据改变或者窗口大小变化时的处理
   * 计算展示的dataZoom个数, 并更新dataZoom
   * 判断是否超出容器高度, 超出开启定时滚动
   */
  const changeList = () => {
    if (!outDomRef.current || !innerRef.current) return
    const ITEM_HEIGHT = 40
    const CANVAS_PADDING_Y = 44

    const { proxy_region_map } = screenData ?? {}
    const categories = Object.keys(proxy_region_map ?? {}) || []
    const outHeight = outDomRef.current?.offsetHeight ?? 0

    const height = categories.length * ITEM_HEIGHT + CANVAS_PADDING_Y
    const isOverHeight = height > outHeight

    innerRef.current.style.height = isOverHeight
      ? `${outHeight}px`
      : `${height}px`

    // 计算dataZoom的个数
    const NUM = Math.floor((outHeight - CANVAS_PADDING_Y) / ITEM_HEIGHT)
    const COUNT = NUM > 0 ? NUM : 1
    dataZoomNumber.current = COUNT

    // 如果canvas 超出容器高度，则滚动，否则不滚动
    isOverHeight && categories.length > 1
      ? scrollDataZoom()
      : clearScrollTimer()
    updateSeriesData(isOverHeight && categories.length > 1)

    changeDataZoom(true)
    barChart?.resize()
  }

  // 更新series, 超出容器高度, 则复制一份数据到最后，否则正常展示
  const updateSeriesData = (isOver: boolean) => {
    if (!screenData || !barChart) return
    const { proxy_region_map } = screenData
    let categories = Object.keys(proxy_region_map)
    let data = categories.map((key) => proxy_region_map[key])

    if (isOver) {
      categories = [...categories, ...categories]
      data = [...data, ...data]
    }

    barChart?.setOption({
      yAxis: {
        data: categories.map((item) => item.toUpperCase()),
      },
      series: {
        data,
      },
    })
  }

  const scrollDataZoom = () => {
    clearScrollTimer()
    scrollTimer.current = setInterval(() => {
      changeDataZoom()
    }, 2000)
  }

  // 更新dataZoom
  const timeoutTimer = useRef<NodeJS.Timeout | null>(null)
  const changeDataZoom = (isResize?: boolean) => {
    const option = barChart?.getOption() as EChartsCoreOption
    if (!option) return
    const listKeys = Object.keys(screenData?.proxy_region_map ?? {})
    const dataZoom = option['dataZoom'] as DataZoomType
    if (!dataZoom[0]) return
    const { startValue, endValue } = dataZoom[0]
    const isEnd =
      endValue + 1 >= listKeys.length ||
      dataZoomNumber.current >= listKeys.length

    // 若是resize， 则重新设置dataZoom
    let start = 0,
      end = dataZoomNumber.current
    if (isResize) {
      start = isEnd ? 0 : endValue - dataZoomNumber.current + 1
      end = isEnd ? dataZoomNumber.current - 1 : endValue
    } else {
      start = isEnd ? 0 : startValue + 1
      end = isEnd ? dataZoomNumber.current - 1 : endValue + 1
    }

    if (startValue >= listKeys.length - 1) {
      timeoutTimer.current && clearInterval(timeoutTimer.current)
      timeoutTimer.current = setTimeout(() => {
        timeoutTimer.current && clearInterval(timeoutTimer.current)
        barChart?.setOption({
          animation: false,
          dataZoom: {
            startValue: 0,
            endValue: dataZoomNumber.current - 1,
          },
        })
      }, 800)
    }

    barChart?.setOption({ animation: true })
    barChart?.dispatchAction({
      type: 'dataZoom',
      startValue: start,
      endValue: end,
    })
  }

  const clearScrollTimer = () => {
    scrollTimer.current && clearInterval(scrollTimer.current)
  }

  useEffect(() => {
    if (!barChart) return
    getVisibleYAxisData()
    scrollDataZoom()
    changeList()

    barChart?.on('dataZoom', getVisibleYAxisData)
    window.addEventListener('resize', changeList)

    return () => {
      clearScrollTimer()
      barChart?.off('dataZoom', getVisibleYAxisData)
      window.removeEventListener('resize', changeList)
    }
  }, [screenData, barChart])

  useEffect(() => {
    const chartDom = document.getElementById('proxyBarChart')
    const barChart = echarts.init(chartDom)
    setBarChart(barChart)
    const option = getBarOption()

    option && barChart.setOption(option)

    return () => {
      clearScrollTimer()
      barChart?.dispose()
      setBarChart(null)
    }
  }, [])

  return (
    <div className="w-full flex-1 flex flex-col overflow-hidden">
      <SmallTitle title="节点分布情况" />
      <div
        ref={outDomRef}
        className=" flex-1 flex bg-[rgba(0,0,0,0.40)] overflow-hidden"
      >
        <div ref={innerRef} id="proxyBarChart" className="w-full h-full"></div>
      </div>
    </div>
  )
}

export default NodesDistribution
