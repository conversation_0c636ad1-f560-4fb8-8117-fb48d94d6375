import { useEffect, useState, useRef } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { getDownloadProxies, useImportProxiesMutation } from '@/api/proxies'
import { errorToast, successToast } from '@/components/GlobalToast'
import { toast } from '@/components/ui/use-toast'
import { Loader } from 'lucide-react'
import { fileDownload } from '@/lib/utils'

interface AddProxysProps {
  open: boolean
  openChange: (value: boolean) => void
}
const AddProxys = ({ open, openChange }: AddProxysProps) => {
  const importProxiesMutation = useImportProxiesMutation()

  const [content, setContent] = useState('')
  const [loading, setLoading] = useState(false)
  const [submitLoading, setSubmitLoading] = useState(false)

  const dialogContentRef = useRef<HTMLDivElement>(null!)
  const textareaRef = useRef<HTMLTextAreaElement>(null!)

  const downloadPackage = async () => {
    setLoading(true)
    const blob = await getDownloadProxies()
      .then((res) => res)
      .catch(() => {
        errorToast('安装包下载失败，请重新下载！', toast)
        return undefined
      })
    if (blob) {
      await fileDownload(
        { data: blob, name: 'xproxy.tar.gz' },
        { transcode: '' },
      )
      successToast('文件包下载成功！', toast)
    }

    setLoading(false)
  }

  const handleSure = () => {
    setSubmitLoading(true)
    importProxiesMutation.mutate(
      { data: content },
      {
        onSuccess: () => {
          successToast('节点导入成功', toast)
          openChange(false)
          setSubmitLoading(false)
        },
        onError: () => {
          setSubmitLoading(false)
        },
      },
    )
  }

  const adjustTextareaHeight = () => {
    if (!textareaRef.current) return
    textareaRef.current.style.height = 'auto'
    textareaRef.current.style.height = `${textareaRef.current.scrollHeight + 2}px`
  }

  useEffect(() => {
    adjustTextareaHeight()
  }, [content])

  useEffect(() => {
    if (!open) setContent('')
  }, [open])

  return (
    <Dialog open={open} onOpenChange={() => openChange(false)}>
      <DialogContent
        ref={dialogContentRef}
        className="w-[800px] overflow-hidden"
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle className="text-[#18181B]">导入节点</DialogTitle>
          <DialogDescription className="text-[#71717A] font-normal">
            导入节点仅可做为出口节点
          </DialogDescription>
        </DialogHeader>
        <div className="font-medium text-[#18181b] leading-6 flex flex-col space-y-3">
          <div className="flex flex-col space-y-1.5">
            <div>Setp1. 请下载安装包，在公网节点服务器解压后参照Readme使用</div>
            <div className="w-full h-[72px] flex items-center px-3 rounded-md border border-1-[#D1D5DB]">
              <div className="flex-1 flex items-center space-x-2">
                <span className="text-[26px] custom-font">&#x1F4E6;</span>
                <span className="">安装包</span>
              </div>
              {loading ? (
                <div className="px-2 py-4 flex items-center">
                  <Loader className="w-4 h-4 mr-2 animate-spin-slow" />{' '}
                  下载中...
                </div>
              ) : (
                <div
                  className="px-2 py-4 cursor-pointer"
                  onClick={downloadPackage}
                >
                  下载
                </div>
              )}
            </div>
          </div>
          <div className="flex flex-col space-y-1.5">
            <div>Setp2. 请输入运行结果，同时导入多个运行结果请换行</div>
            <Textarea
              value={content}
              ref={textareaRef}
              className="result_input max-h-[calc(100vh-405px)] font-normal"
              placeholder="同时导入多个结果请换行"
              onChange={(e) => setContent(e.target.value)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button
            type="submit"
            className="bg-[#1E3A8A] hover:bg-[#1E3A8A] font-medium text-sm "
            disabled={submitLoading || !content.trim()}
            onClick={handleSure}
          >
            {submitLoading && (
              <Loader className="w-4 h-4 mr-2 animate-spin-slow" />
            )}
            {submitLoading ? '导入中...' : '确认'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

export default AddProxys
