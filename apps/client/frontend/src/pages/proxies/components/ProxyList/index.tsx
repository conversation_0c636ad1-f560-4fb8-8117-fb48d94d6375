import { useEffect, useMemo, useRef } from 'react'
import { useSearch } from '@tanstack/react-router'
import { SOURECE_TYPE, type IProxyItem } from '../../types/proxy-bridge-type'
import { EllipsisTooltip } from '@/components/encapsulation'
import { cn, getUrl } from '@/lib/utils'
import { countryCodeMap } from '@/pages/dashboard/data'
import { PROXY_TYPE, PROXY_USE_STATUS } from '@/data'
import ProxiesEmpty from '@/assets/svg/proxies/ProxiesEmpty.svg?react'
import { tabProxiesList } from '@/pages/proxies/index'
import { useGlobalInfoContext } from '@/provider/GlobalInfoContextProvider'

import './index.scss'

export const PROXY_TYPE_DISPLAY = {
  [PROXY_TYPE.GUARD]: {
    label: '守卫',
    icon: '',
    bg: '#CCFBF1',
    color: '#0F766E',
  },
  [PROXY_TYPE.EXIT]: {
    label: '出口',
    icon: '',
    bg: '#DBEAFE',
    color: '#1D4ED8',
  },
  [PROXY_TYPE.IMPORTED]: {
    label: '自有',
    icon: '',
    bg: '#FFEDD5',
    color: '#B45309',
  },
}

interface IProxyItemProps {
  countryCode: string
  country: string
  proxy: IProxyItem
  openDiscard: () => void
}

export const ProxyItem: React.FC<IProxyItemProps> = (props) => {
  const { globalInfo } = useGlobalInfoContext()
  const { countryCode, country, proxy } = props
  // const { countryCode, country, proxy, openDiscard } = props
  const url = getUrl()

  // const discardEnable = useMemo(() => {
  //   return globalInfo?.data.flags.includes('discard_enable')
  // }, [globalInfo])

  const isDiscard = useMemo(() => {
    return proxy.use_status === PROXY_USE_STATUS.DISCARD
  }, [proxy.use_status])

  const hitProxies = useMemo(() => {
    const hitEnable = globalInfo?.data.flags.includes('hit_enable')
    return hitEnable && proxy.source === SOURECE_TYPE.HIT
  }, [proxy, globalInfo])

  return (
    <div
      className={cn(
        'w-[minmax(160px,464px)] flex p-3 hover:bg-[#EFF6FF] rounded-lg group text-[#111322]',
        isDiscard ? 'text-[#A1A1AA]' : '',
        hitProxies && 'rounded-lg border border-red-800 hover:bg-red-600/20 ',
      )}
    >
      <div className="flex-1 flex items-center justify-end w-full h-7">
        <div className="flex-1 flex space-x-3 items-center">
          <div className="w-[27px] h-[20px] proxy-item-flag  rounded-sm overflow-hidden">
            <img
              className={cn(
                'w-full h-full object-cover rounded-sm',
                isDiscard && 'opacity-50',
              )}
              src={`${url}/res/flag3/${countryCode.toLowerCase()}.svg`}
            />
          </div>
          <EllipsisTooltip
            className="text-lg flex-1 font-semibold"
            text={country}
          />
        </div>
        {/* {discardEnable && !isDiscard && proxy.type !== PROXY_TYPE.GUARD && (
          <div
            className="flex-nowrap hidden group-hover:flex px-4 py-2 leading-5 text-xs font-medium bg-[#DC2626] rounded-md text-[#fff] cursor-pointer"
            onClick={openDiscard}
          >
            弃用
          </div>
        )} */}
      </div>
    </div>
  )
}

interface IProps {
  tab: (typeof tabProxiesList)[number] | undefined
  data: IProxyItem[]
  openDiscard: (proxy: IProxyItem) => void
}

export const ProxyList: React.FC<IProps> = (props) => {
  const search = useSearch({ strict: false })
  const { tab, data, openDiscard } = props

  const refs = useRef<{ [key: string]: HTMLDivElement | null }>({})

  const countryCategory = useMemo(() => {
    const category: { [key: string]: IProxyItem[] } = {}
    data.forEach((item) => {
      if (!category[item.country_code]) {
        category[item.country_code] = []
      }
      category[item.country_code].push(item)
    })
    return category
  }, [data])

  const searchCountryCode = useMemo(() => {
    const parameters = new URLSearchParams(search)
    const country_code = parameters.get('country_code')
    return country_code || ''
  }, [search])

  useEffect(() => {
    const element = refs.current[searchCountryCode.toLowerCase()]
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
    }
  }, [searchCountryCode, countryCategory])

  return (
    <div className="flex-1 flex flex-col space-y-4">
      {Object.keys(countryCategory).length ? (
        Object.keys(countryCategory).map((key) => {
          return (
            <div
              ref={(element) => (refs.current[key.toLowerCase()] = element)}
              key={key}
              className="flex flex-col space-y-4"
            >
              <div className="text-[#111827] text-lg font-semibold leading-6.5 ">
                {countryCodeMap[key.toUpperCase()]}
              </div>
              <div
                className={cn(
                  'p-4 bg-white rounded-lg border border-[#dcdfea] grid grid-cols-[repeat(6,_minmax(160px,_464px))] gap-4 justify-between',
                )}
              >
                {countryCategory[key].map((item) => (
                  <ProxyItem
                    key={item.name}
                    countryCode={key}
                    country={item.name}
                    proxy={item}
                    openDiscard={() => openDiscard(item)}
                  />
                ))}
              </div>
            </div>
          )
        })
      ) : (
        <div className="flex-1 w-full h-full flex flex-col space-y-4 items-center justify-center">
          <ProxiesEmpty />
          <div>{tab?.value}为空</div>
        </div>
      )}
    </div>
  )
}
