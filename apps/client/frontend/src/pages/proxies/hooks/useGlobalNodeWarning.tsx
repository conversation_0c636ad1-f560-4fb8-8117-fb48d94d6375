import { useCallback, useState } from 'react'
import _ from 'lodash'

import { toast } from '@/components/ui/use-toast'
import { errorToast } from '@/components/GlobalToast'

import { api } from '@/auth'

import {
  ProxiesSchema,
  IProxyItem,
} from '@/pages/proxies/types/proxy-bridge-type'

function Title(text: string): React.ReactNode {
  return (
    <div className="text-white text-lg font-medium font-['PingFang SC'] leading-relaxed">
      {text}
    </div>
  )
}
function Desc(text: string): React.ReactNode {
  return (
    <div className="w-[238px] text-justify text-neutral-50 text-sm font-normal font-['PingFang SC'] leading-tight">
      {' '}
      {text}
    </div>
  )
}
export const useGlobalNodeWarning = () => {
  const [count, setCount] = useState<number>(0)
  const nodeIsToLow = _.debounce((data: IProxyItem[]) => {
    const length = data.length || 0
    if (length < 20) {
      errorToast(
        {
          title: Title('节点数过低!') as unknown as string,
          desc: Desc('当前节点数低于20个，请及时扩充！'),
        },
        toast,
      )
    }
  }, 300)

  const countriesIsToLow = _.debounce(
    (data: { [key: string]: IProxyItem[] }) => {
      const length = Object.keys(data)?.length || 0
      if (length < 5) {
        errorToast(
          {
            title: Title('节点国家数过低！') as unknown as string,
            desc: Desc('当前节点所属国家数低于5个，请及时扩充！'),
          },
          toast,
        )
      }
    },
    300,
  )

  const globalNodeWarning = _.debounce(
    useCallback(
      (data: IProxyItem[]) => {
        if (count === 0) {
          const category: { [key: string]: IProxyItem[] } = {}
          data.forEach((item) => {
            if (!category[item.country_code]) {
              category[item.country_code] = []
            }
            category[item.country_code].push(item)
          })
          nodeIsToLow(data)
          countriesIsToLow(category)
          setCount(count + 1)
        }
      },
      [count],
    ),
    300,
  )

  const initGlobalNodeWarning = async () => {
    try {
      const { data } = await api.get('/proxies').json(ProxiesSchema.parse)
      globalNodeWarning(data)
    } catch (error) {
      /* empty */
    }
  }

  return {
    initGlobalNodeWarning,
    globalNodeWarning,
    nodeIsToLow,
    countriesIsToLow,
  }
}
