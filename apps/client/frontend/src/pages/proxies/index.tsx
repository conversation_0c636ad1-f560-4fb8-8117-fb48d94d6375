import { useEffect, useState, useMemo } from "react";

import { BaseContent } from "@/components/BaseContent";
import { ProxyList } from "./components/ProxyList";
import DeleteDialog from "@/components/DeleteDialog";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
// import { Button } from '@/components/ui/button'
import AddProxys from "./components/AddProxys";
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";

import ProxiesAll from "@/assets/svg/proxies/ProxiesAll.svg";
import ProxiesGuard from "@/assets/svg/proxies/ProxiesGuard.svg";
import ProxiesExit from "@/assets/svg/proxies/ProxiesExit.svg";
import ProxiesSelf from "@/assets/svg/proxies/ProxiesSelf.svg";
// import ProxiesHit from "@/assets/svg/proxies/ProxiesHit.svg";
// import ProxiesRemind from "@/assets/svg/proxies/remind.svg";
// import { useProxiesExit, useProxiesGuard } from "@/api/link";
import { getProxies, useDeleteProxiesMutation } from "@/api/proxies";
import { useGlobalInfoContext } from "@/provider/GlobalInfoContextProvider";

// import { useGlobalNodeWarning } from './hooks/useGlobalNodeWarning'

import { IProxyItem, SOURECE_TYPE } from "./types/proxy-bridge-type";
import { TABPROXIESTYPE } from "./types/config";
import { PROXY_TYPE, PROXY_USE_STATUS } from "@/data";
import { cn } from "@/lib/utils";
// Plus,
// import { X } from "lucide-react";
import "./index.scss";
import { successToast } from "@/components/GlobalToast";
import { toast } from "@/components/ui/use-toast";

export const tabProxiesList = [
	{
		key: TABPROXIESTYPE.ALL,
		value: "全部节点",
		icon: ProxiesAll,
	},
	{
		key: TABPROXIESTYPE.USE,
		value: "在用节点",
		icon: ProxiesGuard,
	},
	{
		key: TABPROXIESTYPE.SPARE,
		value: "备用节点",
		icon: ProxiesExit,
	},
	// {
	//   key: TABPROXIESTYPE.DISCARD,
	//   value: '弃用节点',
	//   icon: ProxiesExit,
	// },
];

export const Proxies: React.FC = () => {
	// const { globalNodeWarning } = useGlobalNodeWarning();
	const { globalInfo } = useGlobalInfoContext();
	const deleteProxiesMutation = useDeleteProxiesMutation();

	const [proxies, setProxies] = useState<IProxyItem[]>([]);
	const [tabChoose, setTabChoose] = useState(TABPROXIESTYPE.ALL);
	const [searchType, setSearchType] = useState(PROXY_TYPE.ALL); // 下拉筛选

	const [showAdd, setShowAdd] = useState(false);

	const [showDiscard, setShowDiscard] = useState(false);
	const [discard, setDiscard] = useState<IProxyItem>({} as IProxyItem);
	// const [showTip, setShowTip] = useState(true);

	const { data: allProxies } = getProxies();
	// const { data: guardProxies } = useProxiesGuard()
	// const { data: exitProxies } = useProxiesExit()

	const deleteHandle = () => {
		setShowDiscard(false);
		setDiscard({} as IProxyItem);
		console.log("discard", discard.name);
		deleteProxiesMutation.mutate(discard.name, {
			onSuccess: () => {
				successToast("弃用成功", toast);
			},
		});
	};

	const getProxiesCategory = (tab: string) => {
		let filterStatus: (typeof PROXY_USE_STATUS)[keyof typeof PROXY_USE_STATUS][] =
			[];
		switch (tab) {
			case TABPROXIESTYPE.ALL:
				filterStatus = Object.values(PROXY_USE_STATUS);
				break;
			case TABPROXIESTYPE.USE:
				filterStatus = [PROXY_USE_STATUS.USE, PROXY_USE_STATUS.EXCLUSIVE];
				break;
			case TABPROXIESTYPE.SPARE:
				filterStatus = [PROXY_USE_STATUS.NONE];
				break;
			case TABPROXIESTYPE.DISCARD:
				filterStatus = [PROXY_USE_STATUS.DISCARD];
				break;
		}
		return allProxies
			? allProxies.data.filter((item) =>
					filterStatus.includes(item?.use_status)
				)
			: [];
	};

	useEffect(() => {
		// const current = tabChoose === TABPROXIESTYPE.ALL ? allProxies : tabChoose === TABPROXIESTYPE.USE ? guardProxies : exitProxies
		const current = getProxiesCategory(tabChoose);
		current && setProxies(current);
	}, [allProxies, tabChoose, searchType]);

	const proxiesTypeList = useMemo(() => {
		return proxies.filter((item) => {
			if (searchType === PROXY_TYPE.ALL) return true;
			// if (searchType === PROXY_TYPE.SELF) return item.user_imported
			if (item.source === SOURECE_TYPE.PLATFORM)
				return item.type === searchType;
			return item.source === searchType;
		});
	}, [proxies, searchType]);

	const discardEnable = useMemo(() => {
		return globalInfo?.data.flags.includes("discard_enable");
	}, [globalInfo]);

	const proxiesSelectType = useMemo(() => {
		const proxiesSelectType = [
			{
				label: "全部",
				value: PROXY_TYPE.ALL,
				statistics: proxies.length,
				icon: ProxiesAll,
			},
			{
				label: "入口节点",
				value: PROXY_TYPE.PASS_GFW,
				statistics: proxies.filter((item) => item.type === PROXY_TYPE.PASS_GFW)
					.length,
				icon: ProxiesSelf,
			},
			{
				label: "中继节点",
				value: PROXY_TYPE.GUARD,
				statistics: proxies.filter((item) => item.type === PROXY_TYPE.GUARD)
					.length,
				icon: ProxiesGuard,
			},
			{
				label: "出口节点",
				value: PROXY_TYPE.EXIT,
				statistics: proxies.filter(
					(item) =>
						item.type === PROXY_TYPE.EXIT &&
						item.source !== SOURECE_TYPE.IMPORTED
				).length,
				icon: ProxiesExit,
			},

			// {
			// 	label: "导入节点",
			// 	value: PROXY_TYPE.IMPORTED,
			// 	statistics: proxies.filter(
			// 		(item) => item.source === SOURECE_TYPE.IMPORTED
			// 	).length,
			// 	icon: ProxiesSelf,
			// },
		];
		// if (discardEnable) {
		// 	proxiesSelectType.push({
		// 		label: "打击节点",
		// 		value: PROXY_TYPE.HIT,
		// 		statistics: proxies.filter((item) => item.source === SOURECE_TYPE.HIT)
		// 			.length,
		// 		icon: ProxiesHit,
		// 	});
		// }
		return proxiesSelectType;
	}, [proxies, discardEnable]);

	const filterTabProxiesList = useMemo(() => {
		return tabProxiesList.filter((item) => {
			if (item.key === TABPROXIESTYPE.DISCARD) {
				return discardEnable;
			}
			return true;
		});
	}, [discardEnable]);

	// useEffect(() => {
	//   if (allProxies.data) {
	//     globalNodeWarning(allProxies.data)
	//   }
	// }, [allProxies])

	return (
		<BaseContent
			title="节点池"
			className="proxies w-full h-full flex flex-col overflow-y-hidden"
			// toolbar={
			//   <Button
			//     id="proxies-import-btn"
			//     variant="link"
			//     onClick={() => setShowAdd(true)}
			//   >
			//     <Plus className="mr-2 w-4 h-4" />
			//     <span>导入节点</span>
			//   </Button>
			// }
			titleContain={
				<div className="flex space-x-5 items-center">
					<Tabs
						defaultValue={tabChoose}
						className="w-full rounded-lg border border-1 border-[#F4F4F5]"
					>
						<TabsList className="w-full flex justify-start bg-[#FAFAFA]">
							{filterTabProxiesList.map((item) => (
								<TabsTrigger
									className="flex space-x-2 h-full rounded-md data-[state=active]:shadow-[0px_1px_3px_0px_rgba(16,24,40,0.10),0px_1px_2px_0px_rgba(16,24,40,0.06)]"
									key={item.key}
									value={item.key}
									onClick={() => {
										setTabChoose(item.key);
									}}
								>
									{/* <item.icon className={cn("mr-2", item.key === tabChoose ? 'fill-[#18181B]' : 'fill-[#71717A]')} /> */}

									<span>{item.value}</span>
									<div className="px-2 py-[2px] border border-1 border-[#E4E4E7] rounded-[48px] leading-5.5">
										{getProxiesCategory(item.key).length}
									</div>
								</TabsTrigger>
							))}
						</TabsList>
					</Tabs>
					<SelectProxyType
						value={searchType}
						placeholder="类型"
						options={proxiesSelectType}
						onValueChange={(e) => {
							setSearchType(e);
						}}
					></SelectProxyType>
				</div>
			}
		>
			<div className="flex-1 h-0 flex flex-col space-y-5">
				{/* {tabChoose !== TABPROXIESTYPE.DISCARD && showTip && discardEnable && (
					<div className=" relative p-5 flex space-x-4 rounded-md border border-[#BFDBFE] bg-[#EFF6FF]  shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]">
						<img src={ProxiesRemind} alt="" className="w-8 h-8" />
						<div className="text-base text-[#18181B] leading-6 flex flex-col space-y-1">
							<p>仅出口节点可弃用</p>
							<p className="text-sm text-[#3F3F46] leading-5.5">
								右上角提供出口节点的下拉筛选
							</p>
						</div>
						<X
							className=" absolute right-3 top-3 w-4 h-4 text-[#18181B] cursor-pointer"
							onClick={() => setShowTip(false)}
						/>
					</div>
				)} */}
				<div className="proxies-container flex-1 overflow-y-auto flex">
					<ProxyList
						tab={tabProxiesList.find((v) => v.key === tabChoose)}
						data={proxiesTypeList}
						openDiscard={(proxy) => {
							setDiscard(proxy);
							setShowDiscard(true);
						}}
					/>
				</div>
			</div>

			<DeleteDialog
				open={showDiscard}
				title={
					discard.use_status === PROXY_USE_STATUS.NONE
						? `确定弃用【${discard.name}】这个节点？`
						: `节点【${discard.name}】正在使用，无法弃用`
				}
				desc={
					discard.use_status === PROXY_USE_STATUS.NONE
						? "弃用该节点后，不能再次恢复，请谨慎操作"
						: "不能弃用正在使用的节点"
				}
				// loading={deleteLoading}
				openChange={setShowDiscard}
				deleteHandle={deleteHandle}
				className="w-[600px]"
				okText={discard.use_status === PROXY_USE_STATUS.NONE ? "弃用" : ""}
				cancelText={
					discard.use_status === PROXY_USE_STATUS.NONE ? "取消" : "关闭"
				}
				cancelClassName={
					discard.use_status !== PROXY_USE_STATUS.NONE
						? "bg-[#1E3A8A] hover:bg-[#1E3A8A] text-[#fff]"
						: ""
				}
			/>

			<AddProxys open={showAdd} openChange={setShowAdd} />
		</BaseContent>
	);
};

interface SelectProxyTyperProps {
	value: string;
	options: {
		label: string;
		value: string;
		statistics?: number | undefined;
		icon?: string;
	}[];
	placeholder?: string;
	onValueChange: (value: string) => void;
}

const SelectProxyType = ({
	value,
	options,
	placeholder,
	onValueChange,
}: SelectProxyTyperProps) => {
	return (
		<Select onValueChange={onValueChange} value={value}>
			<SelectTrigger
				asChild={false}
				className={cn(
					"w-[280px] h-[44px] py-3 bg-[#FAFAFA] focus:ring-0 focus:ring-ring focus:ring-offset-0"
				)}
			>
				<SelectValue
					placeholder={placeholder}
					className={cn("bg-[#fff] text-[#a1a1a1] placeholder:text-[#a1a1a1]")}
				/>
			</SelectTrigger>
			<SelectContent>
				<SelectGroup>
					{options?.map((item) => (
						<SelectItem
							value={item.value}
							key={item.value}
							prefix={
								(
									<img src={item.icon} className="w-4 h-4 mr-2" />
								) as unknown as string
							}
							slot={
								(
									<span className="text-[#71717A]">{item.statistics}</span>
								) as unknown as string
							}
						>
							{item.label}
						</SelectItem>
					))}
				</SelectGroup>
			</SelectContent>
		</Select>
	);
};
