import { ApiResponseSchema } from '@/api/commonProxy'
import { z } from 'zod'

export enum SOURECE_TYPE {
  PLATFORM = 'platform',
  HIT = 'hit',
  IMPORTED = 'imported',
}

export const ProxySchema = z.object({
  name: z.string(),
  server: z.string(),
  port: z.number(),
  protocol: z.string(),
  country_name: z.string(),
  country_code: z.string(),
  city_name: z.string(),
  delay: z.number(),
  use_status: z.string(),
  type: z.string(),
  // user_imported: z.boolean(),
  source: z.string(),
})
export const ProxiesSchema = ApiResponseSchema(z.array(ProxySchema))
export type IProxyItem = z.infer<typeof ProxySchema>
