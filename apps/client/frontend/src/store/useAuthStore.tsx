import { create } from 'zustand'
import { persist, createJSONStorage, devtools } from 'zustand/middleware'
import { z } from 'zod'
import { jwtDecode } from 'jwt-decode'
import { createSelectorFunctions } from 'auto-zustand-selectors-hook'
import type { UserSchema } from '@/pages/link/types/proxy-bridge-type'

// TODO Cheery-pick the necessary code from https://doichevkostia.dev/blog/authentication-store-with-zustand/

const TokenDataSchema = z.object({
  exp: z.number(),
  iat: z.number(),
  iss: z.string(),
  user_id: z.string(),
})

type TokenData = z.infer<typeof TokenDataSchema>

type AuthStore = {
  accessToken: string | undefined
  accessTokenData: TokenData | undefined
  accessUser: User | undefined
  accessUserInfo: z.infer<typeof UserSchema> | undefined
  setAccessToken: (token: string | undefined) => void
  clearTokens: () => void
  setAccessUser: (uer: User | undefined) => void
  setAccessUserInfo: (info: z.infer<typeof UserSchema>) => void
}

type User = {
  username: string
  password: string
}
export const decodeAccessToken = (accessToken: string) =>
  TokenDataSchema.parse(jwtDecode<TokenData>(accessToken))

const useAuthStoreBase = create<AuthStore>()(
  devtools(
    persist(
      (set) => ({
        accessToken: undefined,
        accessTokenData: undefined,
        accessUser: undefined,
        accessUserInfo: undefined,
        setAccessToken: (accessToken: string | undefined) => {
          const accessTokenData = (() => {
            try {
              return accessToken ? decodeAccessToken(accessToken) : undefined
            } catch (error) {
              console.error(error)
              return
            }
          })()
          set({
            accessToken,
            accessTokenData,
          })
        },
        setAccessUser: (accessUser: User | undefined) => {
          set({
            accessUser,
          })
        },
        setAccessUserInfo: (info: z.infer<typeof UserSchema>) => {
          set({
            accessUserInfo: info,
          })
        },
        clearTokens: () => {
          set({
            accessToken: undefined,
            accessTokenData: undefined,
            accessUserInfo: undefined,
          })
        },
      }),
      {
        name: 'routerd.auth-store', // name of the item in the storage (must be unique)
        storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
      },
    ),
    {
      name: 'routerd.auth-store',
      enabled: !import.meta.env.PROD,
    },
  ),
)

export const useAuthStore = createSelectorFunctions(useAuthStoreBase)
