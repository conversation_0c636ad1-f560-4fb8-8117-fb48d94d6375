export const Lan1 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="369"
    height="301"
    viewBox="0 0 369 301"
    fill="none"
  >
    <path
      d="M368.995 30.687C85.9949 30.687 229.508 270.39 0.507903 270.39"
      stroke="url(#paint0_linear_32_69071)"
      strokeOpacity="0.7"
      strokeWidth="60"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_69071"
        x1="303.685"
        y1="129.576"
        x2="202.619"
        y2="348.041"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan2 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="372"
    height="187"
    viewBox="0 0 372 187"
    fill="none"
  >
    <path
      d="M371.508 20.6551C169.508 20.6551 274.925 166.39 0.507867 166.39"
      stroke="url(#paint0_linear_32_69070)"
      strokeOpacity="0.7"
      strokeWidth="40"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_69070"
        x1="303.685"
        y1="90.3649"
        x2="270.02"
        y2="225.15"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan3 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="366"
    height="83"
    viewBox="0 0 366 83"
    fill="none"
  >
    <path
      d="M365.411 15.5913C163.411 15.5913 274.925 67.3899 0.507832 67.3899"
      stroke="url(#paint0_linear_32_69069)"
      strokeOpacity="0.7"
      strokeWidth="30"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_69069"
        x1="298.702"
        y1="40.3683"
        x2="294.146"
        y2="90.8524"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan4 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="365"
    height="89"
    viewBox="0 0 365 89"
    fill="none"
  >
    <path
      d="M364.677 73.5327C162.677 73.5327 274.925 15.8859 0.507834 15.8859"
      stroke="url(#paint0_linear_38_75898)"
      strokeOpacity="0.7"
      strokeWidth="30"
    />
    <defs>
      <linearGradient
        id="paint0_linear_38_75898"
        x1="298.102"
        y1="45.9583"
        x2="292.458"
        y2="-10.1153"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan5 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="372"
    height="195"
    viewBox="0 0 372 195"
    fill="none"
  >
    <path
      d="M371.508 174.277C169.508 174.277 274.925 20.8859 0.50787 20.8859"
      stroke="url(#paint0_linear_32_69067)"
      strokeOpacity="0.7"
      strokeWidth="40"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_69067"
        x1="303.685"
        y1="100.905"
        x2="266.624"
        y2="-40.0684"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan6 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="372"
    height="320"
    viewBox="0 0 372 320"
    fill="none"
  >
    <path
      d="M371.508 289.341C88.5078 289.341 229.508 30.8855 0.50791 30.8855"
      stroke="url(#paint0_linear_32_69068)"
      strokeOpacity="0.7"
      strokeWidth="60"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_69068"
        x1="303.685"
        y1="165.713"
        x2="209.647"
        y2="-46.5808"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan7 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="369"
    height="301"
    viewBox="0 0 369 301"
    fill="none"
  >
    <path
      d="M0.020813 30.687C283.021 30.687 139.508 270.39 368.508 270.39"
      stroke="url(#paint0_linear_32_68936)"
      strokeOpacity="0.7"
      strokeWidth="60"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_68936"
        x1="67.3845"
        y1="145.345"
        x2="150.57"
        y2="346.46"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan8 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="372"
    height="187"
    viewBox="0 0 372 187"
    fill="none"
  >
    <path
      d="M0.50769 20.6551C202.508 20.6551 97.0902 166.39 371.508 166.39"
      stroke="url(#paint0_linear_32_68935)"
      strokeOpacity="0.7"
      strokeWidth="40"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_68935"
        x1="68.3309"
        y1="90.3649"
        x2="101.996"
        y2="225.15"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan9 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="366"
    height="83"
    viewBox="0 0 366 83"
    fill="none"
  >
    <path
      d="M0.604675 15.5913C202.605 15.5913 91.0902 67.3899 365.508 67.3899"
      stroke="url(#paint0_linear_32_68934)"
      strokeOpacity="0.7"
      strokeWidth="30"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_68934"
        x1="67.3133"
        y1="40.3683"
        x2="71.8699"
        y2="90.8524"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan10 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="365"
    height="89"
    viewBox="0 0 365 89"
    fill="none"
  >
    <path
      d="M0.338806 73.5327C202.339 73.5327 90.0902 15.8859 364.508 15.8859"
      stroke="url(#paint0_linear_32_68931)"
      strokeOpacity="0.7"
      strokeWidth="30"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_68931"
        x1="66.9132"
        y1="45.9583"
        x2="72.5571"
        y2="-10.1153"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan11 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="372"
    height="195"
    viewBox="0 0 372 195"
    fill="none"
  >
    <path
      d="M0.50769 174.277C202.508 174.277 97.0902 20.8859 371.508 20.8859"
      stroke="url(#paint0_linear_32_68932)"
      strokeOpacity="0.7"
      strokeWidth="40"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_68932"
        x1="68.3309"
        y1="100.905"
        x2="105.392"
        y2="-40.0684"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)

export const Lan12 = ({ color }: { color: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="372"
    height="320"
    viewBox="0 0 372 320"
    fill="none"
  >
    <path
      d="M0.507874 289.341C283.508 289.341 142.508 30.8855 371.508 30.8855"
      stroke="url(#paint0_linear_32_68933)"
      strokeOpacity="0.7"
      strokeWidth="60"
    />
    <defs>
      <linearGradient
        id="paint0_linear_32_68933"
        x1="68.331"
        y1="165.713"
        x2="162.369"
        y2="-46.5808"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor={color} />
        <stop offset="1" stopColor={color} />
      </linearGradient>
    </defs>
  </svg>
)
