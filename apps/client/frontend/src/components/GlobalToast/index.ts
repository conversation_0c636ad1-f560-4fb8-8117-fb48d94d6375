import type { ToastProps } from '@/components/ui/toast'
import { useToast } from '@/components/ui/use-toast'

type ToasterToast = ToastProps & {
  title?: React.ReactNode | string
  desc?: React.ReactNode
}

// <TData>
export const errorToast = (
  info: ToasterToast | string,
  toastFunction: ReturnType<typeof useToast>['toast'],
) => {
  let parameters: ToasterToast = { title: '', desc: '', duration: 3000 }
  if (typeof info === 'string') {
    parameters.title = info
  } else {
    parameters = { ...parameters, ...info }
  }

  return toastFunction({
    ...parameters,
    description: parameters.desc,
    variant: 'destructive',
  })
}

export const successToast = (
  info: ToasterToast | string,
  toastFunction: ReturnType<typeof useToast>['toast'],
) => {
  let parameters: ToasterToast = { title: '', desc: '', duration: 3000 }
  if (typeof info === 'string') {
    parameters.title = info
  } else {
    parameters = { ...parameters, ...info }
  }
  return toastFunction({
    ...parameters,
    description: parameters.desc,
    variant: 'succsess',
  })
}

export const infoToast = (
  info: ToasterToast | string,
  toastFunction: ReturnType<typeof useToast>['toast'],
) => {
  let parameters: ToasterToast = { title: '', desc: '', duration: 2000 }
  if (typeof info === 'string') {
    parameters.title = info
  } else {
    parameters = { ...parameters, ...info }
  }
  return toastFunction({
    ...parameters,
    // variant: "info",
    className: 'w-auto',
  })
}

// 全局注册errorToast 和 successToast
