import React from 'react'
import { cn } from '@/lib/utils'

export const CardTitle = ({
  title,
  children,
  toolbar,
  className,
  showBorderBottom = true,
}: {
  title: string
  children?: React.ReactNode
  toolbar?: React.ReactNode
  className?: string
  showBorderBottom?: boolean
}) => {
  return (
    <div className={cn('grid gap-3.5 mb-5 custom-1920:gap-5', className)}>
      <div className="flex justify-between items-center">
        <p className="text-[18px] font-semibold leading-10">{title}</p>
        {toolbar}
      </div>
      {children}
      {showBorderBottom && (
        <div className="border-t border-[#E4E4E7] h-[1px]"></div>
      )}
    </div>
  )
}
