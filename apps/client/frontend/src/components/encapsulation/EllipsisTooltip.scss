.ellipsis-tooltip-outer_box {
  width: 100%;
  white-space: pre-wrap;
  text-align: left;

  .ellipsis_box {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: var(--row, 1);
    -webkit-box-orient: vertical;
    width: 100%;
  }

  :deep(.ellipsis_box .ellipsis) {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: var(--row, 1);
    -webkit-box-orient: vertical;
  }

  .content_box {
    word-break: break-all;
    white-space: pre-wrap;
  }
}

.ant-popover:has(.ellipsis-tooltip-content) {
  .ant-popover-arrow {
    &::after {
      background-color: #18181b;
    }
  }

  .ant-popover-content {

    .ant-popover-inner {
      background-color: #18181b;

      .ant-popover-inner-content {
        color: #D4D4D8;
        font-size: 12px;

        .ellipsis-tooltip-content {
          line-height: 18px;

        }
      }
    }
  }
}
