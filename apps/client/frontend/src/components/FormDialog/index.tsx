import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'

import { cn } from '@/lib/utils'

export const FormDialog = ({
  open,
  openChange,
  title = '',
  describe = '',
  successHandle,
  children,
  form,
  cancelText = '取消',
  cancelStyle,
  showSuccessBtn: showSuccessButton = true,
  successText = '确定',
  submitLoading = false,
  loadingIcon = <Loader2 className="w-4 h-4 mr-2 animate-spin" />,
  successStyle,
  contentClass,
  showFooter = true,
}: {
  open: boolean
  openChange: (open: boolean) => void
  title?: string | React.ReactNode
  describe?: string
  children?: React.ReactNode
  successHandle?: () => void
  form: any
  cancelText?: string
  cancelStyle?: string
  showSuccessBtn?: boolean
  successText?: string
  successStyle?: string
  contentClass?: string
  submitLoading?: boolean
  loadingIcon?: React.ReactNode
  showFooter?: boolean
}) => {
  const clear = () => {
    form.resetFields()
    openChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={clear}>
      <DialogContent
        className={cn('w-[600px] px-0', contentClass)}
        onOpenAutoFocus={(e) => e.preventDefault()}
      >
        <DialogHeader className="px-6">
          <DialogTitle className="text-[#18181B]">{title}</DialogTitle>
          <DialogDescription className="text-[#71717A]">
            {describe}
          </DialogDescription>
        </DialogHeader>
        <div className="flex-1 overflow-y-auto px-6">{children}</div>
        {showFooter && (
          <DialogFooter className="px-6">
            <Button
              variant="destructive"
              className={cn(
                'bg-[#F4F4F5] hover:bg-[#F4F4F5] text-[#18181B]',
                cancelStyle,
              )}
              onClick={clear}
            >
              {cancelText}
            </Button>
            {showSuccessButton && (
              <Button
                type="submit"
                loading={submitLoading}
                loaderIcon={loadingIcon}
                className={cn(
                  'bg-[#18181B] hover:bg-[#18181B] font-medium text-sm ml-4',
                  successStyle,
                )}
                onClick={successHandle}
              >
                {successText}
              </Button>
            )}
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  )
}
