import React from 'react'
import { CardTitle } from '../CardTitle'
import { cn } from '@/lib/utils'

export const BaseContent = ({
  children,
  toolbar,
  title,
  titleContain,
  className,
  titleClassName,
  showBorderBottom = true,
}: {
  children: React.ReactNode
  toolbar?: React.ReactNode
  titleContain?: React.ReactNode
  title: string
  titleClassName?: string
  className?: string
  showBorderBottom?: boolean
}) => {
  return (
    <div
      className={cn(
        'p-6 h-full flex flex-col overflow-y-auto w-full',
        className,
      )}
    >
      <CardTitle
        title={title}
        toolbar={toolbar}
        showBorderBottom={showBorderBottom}
        className={titleClassName}
      >
        {titleContain}
      </CardTitle>
      {children}
    </div>
  )
}
