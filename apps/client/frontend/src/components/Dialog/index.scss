@keyframes dialogOverlayShow {
	from {
		opacity: 0;
	}
	to {
		opacity: 0.4;
	}
}
@keyframes dialogContentShow {
	from {
		opacity: 0;
		transform: translate(-50%, -48%) scale(0.96);
	}
	to {
		opacity: 1;
		transform: translate(-50%, -50%) scale(1);
	}
}

.dialogOverlay {
	@apply fixed top-0 left-0 w-full h-full;
	background: linear-gradient(
			0deg,
			rgba(255, 255, 255, 0.2) 0%,
			rgba(255, 255, 255, 0.2) 100%
		),
		rgba(255, 255, 255, 0.88);
	animation: dialogOverlayShow 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
	z-index: 9998;
}

.dialogContent {
	@apply bg-white overflow-hidden rounded shadow fixed top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2  w-[600px] p-6 pb-4;
	box-shadow:
		0px 2px 8px -2px rgba(79, 79, 79, 0.3),
		0px 2px 4px -2px rgba(16, 24, 40, 0.06);
	z-index: 9999;
	animation: dialogContentShow 0.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
}

.dialogTitle {
	@apply text-lg mb-2 text-zinc-900 relative;
	&-type {
		@apply relative flex justify-center items-center mb-4 rounded-full w-12 h-12 bg-red-100;
		div {
			@apply border absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 border-[#EAECF0] rounded-full;
			@for $i from 0 through 5 {
				&:nth-child(#{$i}) {
					width: ($i + 1) * 100%;
					height: ($i + 1) * 100%;
					opacity: (6 - $i) * 0.2;
				}
			}
		}
	}
	&-text {
		@apply relative z-10;
	}
	&-close {
		@apply absolute right-0 top-0 cursor-pointer;
	}
}

.dialogDesc {
	@apply text-zinc-500 text-sm z-10 relative;
}

.dialogFooter {
	@apply flex justify-end pt-4 mt-6 gap-4;
}
