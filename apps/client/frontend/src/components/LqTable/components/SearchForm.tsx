import { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z, ZodSchema } from 'zod'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form'
import { cn, debounce } from '@/lib/utils'

const formItemMap = {
  input: Input,
}

export type IFormValues = Record<string, any>

export interface IFormSchemaItem<T = Record<string, any>> {
  key: string
  defaultValue: any
  rules?: ZodSchema<z.AnyZodObject>
  type?: keyof typeof formItemMap
  formItemRender?: React.FC<T>
  formItemProps?: T
  className?: string
}

interface IProps {
  formSchema: IFormSchemaItem[]
  onChange?: (values: IFormValues) => void
  onSearch?: () => void
  onReset?: () => void
  showReset?: boolean
  toolbar?: React.ReactNode
}

const getDefaultValues = (formSchema: IFormSchemaItem[]) => {
  const defaultValues: IFormValues = {}
  formSchema.forEach((item) => {
    defaultValues[item.key] = item.defaultValue
  })
  return defaultValues
}

const getFormValidator = (formSchema: IFormSchemaItem[]) => {
  const formValidator: Record<string, ZodSchema<any>> = {}
  formSchema.forEach((item) => {
    if (!item.rules) return
    formValidator[item.key] = item.rules
  })
  return formValidator
}

export function SearchForm(props: IProps) {
  // const { formSchema, onChange, onSearch, onReset, toolbar, showReset } = props;
  const { formSchema, onChange, toolbar } = props

  const formValidator = z.object(getFormValidator(formSchema))

  const [formData, setFormData] = useState<IFormValues>()

  // const hiddenResetButton = useMemo<boolean>(() => {
  //     return !Object.values(formData || {}).some((value) => {
  //         if (value instanceof Array) {
  //             return !!value.length;
  //         } else {
  //             return !!value;
  //         }
  //     });
  // }, [formData]);

  const form = useForm<z.infer<typeof formValidator>>({
    resolver: zodResolver(formValidator),
    defaultValues: getDefaultValues(formSchema),
  })

  const handleChange = debounce((values: IFormValues) => {
    console.log('formData', formData)

    setFormData(values)
    onChange?.(values)
  }, 300)

  // const handleReset = () => {
  //     form.reset();
  //     setFormData({});
  //     onReset?.();
  // };

  useEffect(() => {
    const subscription = form.watch(handleChange)
    return () => subscription.unsubscribe()
  }, [form])

  return (
    <>
      <div className={'flex items-center justify-between space-x-3'}>
        <div className="flex flex-1 items-center space-x-2">
          <Form {...form}>
            <div className="flex flex-1  space-x-2">
              {formSchema.map((item) => {
                const FormItemRender =
                  item.formItemRender || formItemMap[item.type || 'input']
                return (
                  <FormField
                    key={item.key}
                    control={form.control}
                    name={item.key}
                    render={({ field }) => (
                      <FormItem
                        className={cn(
                          'min-w-[100px] max-w-[304px] h-8 mt-0',
                          item.className,
                        )}
                      >
                        <FormControl>
                          <FormItemRender {...field} {...item.formItemProps} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )
              })}
            </div>
          </Form>
        </div>
        <div className="min-w-[80px] flex justify-end">{toolbar}</div>
      </div>
    </>
  )
}
