import { Switch } from '@/components/ui/switch'

export const SwitchExclusive = ({
  value,
  onChange,
  canSwitch = true,
}: {
  canSwitch?: boolean
  value?: boolean
  onChange?: () => void
}) => {
  // const [checked, setChecked] = useState(value)

  const onCheckedChange = () => {
    if (canSwitch) {
      onChange?.()
    }
  }
  return (
    <Switch
      className="data-[state=checked]:bg-[#1E3A8A] "
      checked={value}
      disabled={!canSwitch}
      onCheckedChange={onCheckedChange}
    />
  )
}
