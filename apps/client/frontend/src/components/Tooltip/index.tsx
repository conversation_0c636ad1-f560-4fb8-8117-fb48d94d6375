import {
  Tooltip as BaseTooltip,
  <PERSON><PERSON><PERSON>Content,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'

export const Tooltip = (props: {
  children: React.ReactNode
  content: React.ReactNode
}) => {
  return (
    <TooltipProvider>
      <BaseTooltip>
        <TooltipTrigger>{props.children}</TooltipTrigger>
        <TooltipContent>{props.content}</TooltipContent>
      </BaseTooltip>
    </TooltipProvider>
  )
}
