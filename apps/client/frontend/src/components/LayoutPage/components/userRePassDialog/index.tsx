import { currentRePassMutation } from '@/api/user'
import { FormDialog } from '@/components/FormDialog'
import { successToast } from '@/components/GlobalToast'
import { PasswordInput } from '@/components/PasswordInput'
import { toast } from '@/components/ui/use-toast'
import { passCrypto } from '@/lib/utils'
import {
  PasVerification,
  validatePassword,
} from '@/pages/login/components/passVerification'
import { Form } from 'antd'
import { useEffect, useState } from 'react'

export const UserRePassDialog = ({
  setOpen,
  open,
}: {
  open: boolean
  setOpen: (value: boolean) => void
}) => {
  const [pass, setPass] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(true)
  const [dialogLoading, setDialogLoading] = useState(false)
  const [form] = Form.useForm()

  const rePassValue = Form.useWatch('rePassword', form)
  const passwordValue = Form.useWatch('password', form)

  const editPassMutation = currentRePassMutation()

  const handleSubmit = () => {
    if (!isSubmitting) return
    setDialogLoading(true)
    form.validateFields()
    try {
      const addvalue = form.getFieldsValue()
      editPassMutation.mutate(passCrypto(addvalue.password))
      successToast('密码修改成功', toast)
      setOpen(false)
    } finally {
      setDialogLoading(false)
    }
  }
  useEffect(() => {
    setIsSubmitting(
      validatePassword(passwordValue as string, rePassValue as string, pass),
    )
  }, [rePassValue, passwordValue])

  return (
    <FormDialog
      open={open}
      openChange={setOpen}
      title="修改密码"
      describe="重设密码后，请您牢记密码！"
      submitLoading={dialogLoading}
      successHandle={handleSubmit}
      form={form}
      contentClass="w-[568px]"
      successStyle={
        isSubmitting
          ? 'bg-[#1E3A8A] hover:bg-[#1E3A8A]'
          : 'bg-[#1E3A8A] hover:bg-[#1E3A8A] opacity-50'
      }
    >
      <Form
        className="projectform"
        form={form}
        name="dynamic_form_nest_item"
        initialValues={{
          password: '',
          rePassword: '',
        }}
        autoComplete="off"
        layout="vertical"
      >
        <PasVerification
          className="change_password_input_dialog"
          setSatisfiedConditions={setPass}
          des="新密码"
        />
        <Form.Item
          name="rePassword"
          label="确认密码"
          rules={[
            { required: true, message: '请输入确认密码' },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('password') === value) {
                  return Promise.resolve()
                }
                return Promise.reject(new Error('两次输入的密码不一致'))
              },
            }),
          ]}
          className="confirm_password_input_dialog"
        >
          <PasswordInput
            className="focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 focus-within:border-[#D1D5DB] confirm_password_input_dialog"
            placeholder="请输入密码"
          />
        </Form.Item>
      </Form>
    </FormDialog>
  )
}
