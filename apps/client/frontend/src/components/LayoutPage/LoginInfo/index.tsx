import { useState } from 'react'
import { currentUser, isAdmin, logout } from '@/auth'
import Down from '@/assets/svg/(2)chevron-down.svg?react'
import { LogOut } from 'lucide-react'

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { cn } from '@/lib/utils'
import { Route } from '@/routes'
import { useLocation } from '@tanstack/react-router'

export const LoginInfo = ({
  setOpen,
}: {
  setOpen: (value: boolean) => void
}) => {
  const [openDropdown, setOpenDropdown] = useState(false)
  const user = currentUser()
  const navigate = Route.useNavigate()
  const location = useLocation()
  const handleChangeOpenDropdown = (value: boolean) => {
    setOpenDropdown(value)
  }
  return (
    <DropdownMenu open={openDropdown} onOpenChange={handleChangeOpenDropdown}>
      <DropdownMenuTrigger
        asChild
        id="user-menu"
        className="[&_svg]:data-[state=open]:rotate-180"
        onClick={() => setOpenDropdown(true)}
      >
        <div className="curosr-pointer">
          <div className="border-t border-[#fff] opacity-20 mb-2"></div>
          <div
            className={cn(
              'p-2 text-[#fff] flex items-center justify-between rounded-sm',
              location.href === '/userManage' && 'bg-[#213266]',
            )}
          >
            <div className=" flex items-center">
              <div className="w-8 h-8 text-center bg-white rounded-full border border-[#18181B] text-[#52525B] leading-[30px]">
                {' '}
                {user?.username[0]}
              </div>
              <div className="font-normal text-xs mx-3">
                <p>{user?.username}</p>
                <span className="text-[#fff] text-xs font-normal">
                  {isAdmin() ? '管理员' : '用户'}
                </span>
              </div>
            </div>
            <Down />
          </div>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[256px] rounded-md p-[5px]">
        <DropdownMenuLabel className="p-2">
          <p>{user?.username}</p>
          <span className="text-[#52525B] text-xs font-normal">
            {isAdmin() ? '管理员' : '用户'}
          </span>
        </DropdownMenuLabel>
        {isAdmin() ? (
          <DropdownMenuItem
            className="hover:bg-[#F4F4F5] cursor-pointer"
            onClick={async () => {
              await navigate({ to: '/userManage' })
            }}
          >
            <span className="font-normal">用户管理</span>
          </DropdownMenuItem>
        ) : (
          <DropdownMenuItem
            className="hover:bg-[#F4F4F5] cursor-pointer"
            onClick={async () => {
              setOpen(true)
            }}
          >
            <span className="font-normal">修改密码</span>
          </DropdownMenuItem>
        )}
        <div className="border-t border-[#F4F4F5] my-1.5"></div>
        <DropdownMenuItem
          className="flex items-center justify-between hover:bg-[#F4F4F5] cursor-pointer"
          onClick={async () => {
            logout()
            // await navigate({ to: '/login' })
          }}
        >
          <span className="font-normal">退出登录</span>
          <LogOut className="h-4 w-4" />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
