'use client'

import {
  <PERSON>ertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { X } from 'lucide-react'
import { useCallback } from 'react'

interface DeleteDialogProps {
  open: boolean
  openChange: (open: boolean) => void
  deleteHandle: (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
  ) => Promise<void> | void
  title?: string
  desc?: string
  okText?: string
  loading?: boolean
  cancelText?: string
  className?: string
  okClassName?: string
  cancelClassName?: string
  width?: number
}
// 操作栏
export default function DeleteDialog({
  open = false,
  title,
  desc,
  okText = '删除',
  loading = false,
  cancelText = '取消',
  className,
  okClassName = 'bg-[#DC2626] text-[#fff] hover:bg-[#DC2626] hover:text-[#fff] ml-2',
  cancelClassName = '',
  openChange,
  deleteHandle,
}: DeleteDialogProps) {
  const handleSubmit = useCallback(
    (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
      const promise = deleteHandle(e)
      if (promise?.then) {
        promise.then(() => {
          openChange(false)
        })
      } else {
        openChange(false)
      }
    },
    [deleteHandle, openChange],
  )

  return (
    <AlertDialog open={open} onOpenChange={openChange}>
      <AlertDialogContent className={cn('w-[500px]', className)}>
        <AlertDialogHeader>
          <AlertDialogTitle>{title}</AlertDialogTitle>
          <AlertDialogDescription>{desc}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="mt-4">
          <AlertDialogCancel className={cancelClassName}>
            {cancelText}
          </AlertDialogCancel>
          {okText && (
            <Button
              id="user-delete-dialog-btn"
              className={cn(okClassName)}
              disabled={loading}
              onClick={handleSubmit}
            >
              {okText}
            </Button>
          )}
        </AlertDialogFooter>
        <X
          width={20}
          height={20}
          className="absolute top-[22px] right-[22px] cursor-pointer"
          onClick={() => openChange(false)}
        />
      </AlertDialogContent>
    </AlertDialog>
  )
}
