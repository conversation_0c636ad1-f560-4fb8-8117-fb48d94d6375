import React, { useState, createContext, useEffect, useMemo } from 'react'
import { WebSocketService } from '@/lib/socket'
import { useAuthStore } from '@/store/useAuthStore'

export interface WsTrafficMessageType {
  up: number
  down: number
  memory: number
  cpu: number
}

type WsTrafficStateStorage = {
  wsTrafficData: WsTrafficMessageType[]
  setWsTrafficData: React.Dispatch<React.SetStateAction<WsTrafficMessageType[]>>
}

const WsTrafficContext = createContext<WsTrafficStateStorage>(null!)

export const useWsTrafficContext = () => React.useContext(WsTrafficContext)

export function WsTrafficContextProvider({
  children,
}: { children: React.ReactNode }) {
  const token = useAuthStore((state) => state.accessToken)

  const [wsTrafficData, setWsTrafficData] = useState<WsTrafficMessageType[]>([])

  useEffect(() => {
    // if (!token) return
    try {
      const wsService = new WebSocketService(`/traffic?token=${token}`)
      wsService.connect()
      wsService.setOnMessageCallback((message: string) => {
        const newMessage = JSON.parse(message) as WsTrafficMessageType
        setWsTrafficData((previous) => {
          return [...previous.slice(-300), newMessage]
        })
      })
    } catch (error) {
      console.log('wsService', error)
    }
  }, [])

  const TrafficRealTimeMemo = useMemo(() => {
    return {
      wsTrafficData,
      setWsTrafficData,
    }
  }, [wsTrafficData, setWsTrafficData])

  return (
    <WsTrafficContext.Provider value={TrafficRealTimeMemo}>
      {children}
    </WsTrafficContext.Provider>
  )
}
