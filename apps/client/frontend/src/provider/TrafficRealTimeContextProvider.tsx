import React, {
  useState,
  createContext,
  useEffect,
  useMemo,
  useRef,
} from 'react'

import { useWsTrafficContext } from '@/provider/WsTrafficContextProvider'

export interface RealTimeTrafficType {
  categories: string[]
  upload: (number | string)[]
  download: (number | string)[]
}

type RealTimeTrafficStateStorage = {
  realTimeTrafficData: RealTimeTrafficType
  setRealTimeTrafficData: React.Dispatch<
    React.SetStateAction<RealTimeTrafficType>
  >
}

const TrafficRealTimeContext = createContext<RealTimeTrafficStateStorage>(null!)

export const useTrafficRealTimeContext = () =>
  React.useContext(TrafficRealTimeContext)

export function TrafficRealTimeContextProvider({
  children,
}: { children: React.ReactNode }) {
  const { wsTrafficData } = useWsTrafficContext()

  const wsTrafficDataRef = useRef(wsTrafficData)

  const [realTimeTrafficData, setRealTimeTrafficData] =
    useState<RealTimeTrafficType>({
      categories: [],
      upload: [],
      download: [],
    })

  const initTrafficData = (length_: number) => {
    let now = new Date()
    const categories = []
    const upload = []
    const download = []

    while (length_--) {
      categories.unshift('')
      now = new Date(+now - 1000)
      upload.push('')
      download.push('')
    }
    setRealTimeTrafficData({
      categories,
      upload,
      download,
    })
  }

  useEffect(() => {
    wsTrafficDataRef.current = wsTrafficData
  }, [wsTrafficData])

  const timer = useRef<NodeJS.Timeout | null>(null)
  const updateRealTimeTraffic = () => {
    timer.current && clearInterval(timer.current)
    timer.current = setInterval(() => {
      if (!wsTrafficDataRef.current.length) return
      const lastData = wsTrafficDataRef.current[
        wsTrafficDataRef.current.length - 1
      ] ?? { up: 0, down: 0 }
      setRealTimeTrafficData((data) => {
        const newData = JSON.parse(JSON.stringify(data)) as RealTimeTrafficType
        const axisData = new Date().toLocaleTimeString().replace(/^\D*/, '')
        newData.upload.shift()
        newData.upload.push((lastData.up / 1024).toFixed(4))
        newData.download.shift()
        newData.download.push((lastData.down / 1024).toFixed(4))
        newData.categories.shift()
        newData.categories.push(axisData)
        return newData
      })
    }, 1500)
  }

  useEffect(() => {
    initTrafficData(200)
    updateRealTimeTraffic()
    return () => {
      timer.current && clearInterval(timer.current)
    }
  }, [])

  const TrafficRealTimeMemo = useMemo(() => {
    return {
      realTimeTrafficData,
      setRealTimeTrafficData,
    }
  }, [realTimeTrafficData, setRealTimeTrafficData])

  return (
    <TrafficRealTimeContext.Provider value={TrafficRealTimeMemo}>
      {children}
    </TrafficRealTimeContext.Provider>
  )
}
