import React, {
  createContext,
  useEffect,
  useMemo,
  useState,
  useRef,
} from 'react'
import { getDashInfo } from '@/api/dashboard'
import { IDashInfo } from '@/pages/dashboard/types/proxy'
import { useAuthStore } from '@/store/useAuthStore'

import { KV_KEYS } from '@/data'
import { useStorageQuery, useStorageMutation, SpecialLinksType } from '@/api/kv'

import { UseQueryResult } from '@tanstack/react-query'

export interface GlobalInfoMessageType {}

type GlobalInfoStateStorage = {
  globalInfo: IDashInfo | null
  // refetchGlobalInfo: (options?: RefetchOptions) => Promise<QueryObserverResult<IDashInfo | null, Error>>
  refetchGlobalInfo: (hideError?: boolean) => Promise<IDashInfo | null>
}

const GlobalInfoContext = createContext<GlobalInfoStateStorage>(null!)

export const useGlobalInfoContext = () => React.useContext(GlobalInfoContext)

export function GlobalInfoContextProvider({
  children,
}: { children: React.ReactNode }) {
  const token = useAuthStore((state) => state.accessToken)
  const [globalInfo, setGlobalInfo] = useState<IDashInfo | null>(null)

  const { refetch: refetchSpecialLinks } = useStorageQuery(
    KV_KEYS.SPECIAL_LINKS,
  ) as UseQueryResult<{ data: SpecialLinksType } | null>
  const storageMutation = useStorageMutation()

  const timer = useRef<NodeJS.Timeout | null>(null)
  // const { data: globalInfo, refetch: refetchGlobalInfo } = useDashInfo()

  const refetchGlobalInfo = async (hideError = false) => {
    let globalData = null
    try {
      const data = await getDashInfo(hideError)
      globalData = data
      
      // 调试历史流量数据
    } catch (error) {
      // 检查是否是因为系统正在加载导致的错误
      if (error && typeof error === 'object' && 'json' in error) {
        try {
          const errorData: any = await (error as any).json();
          if (errorData?.data?.status === 'load') {
            // 系统正在加载中，设置一个延迟重试
            setTimeout(() => {
              refetchGlobalInfo(true);
            }, 2000);
          }
        } catch (jsonError) {
          console.error("GlobalInfoContextProvider - Error parsing error response:", jsonError);
        }
      }
      
      globalData = null
    }
    setGlobalInfo(globalData)
    return globalData
  }

  const clearSpecialLinks = async () => {
    try {
      const { data } = await refetchSpecialLinks()
      const ips = Object.keys(data?.data || {})
      const you_ip = globalInfo?.data?.device_information.you_ip ?? ''

      if (ips.includes(you_ip)) {
        const linkValue = { ...(data?.data ?? {}) }
        Reflect.deleteProperty(linkValue, you_ip)

        storageMutation.mutate({ key: KV_KEYS.SPECIAL_LINKS, value: linkValue })
      }
    } catch {
      // console.log(error)
    }
  }

  useEffect(() => {
    if (!token) return
    refetchGlobalInfo()
  }, [token])

  const GlobalInfoMemo = useMemo(() => {
    return {
      globalInfo,
      refetchGlobalInfo,
    }
  }, [globalInfo, refetchGlobalInfo])

  useEffect(() => {
    timer.current && clearTimeout(timer.current)
    const proxyInfo = globalInfo?.data?.proxy_info
    const { change_at, change_time } = proxyInfo ?? {}
    if (proxyInfo) {
      clearSpecialLinks()
    }

    if (!change_time || !change_at) return

    // 动态跳变倒计时刷新info数据
    const getRaf = () => {
      return change_at + change_time - Date.now() / 1000
    }
    let second = getRaf()
    second = second < 0 ? 5 : second
    timer.current = setTimeout(() => {
      refetchGlobalInfo()
      timer.current && clearTimeout(timer.current)
    }, second * 1000)

    return () => {
      timer.current && clearTimeout(timer.current)
    }
  }, [globalInfo])

  return (
    <GlobalInfoContext.Provider value={GlobalInfoMemo}>
      {children}
    </GlobalInfoContext.Provider>
  )
}
