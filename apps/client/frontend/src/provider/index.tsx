import { WsTrafficContextProvider } from './WsTrafficContextProvider'
import { TrafficRealTimeContextProvider } from './TrafficRealTimeContextProvider'
import { GlobalInfoContextProvider } from './GlobalInfoContextProvider'

export const Provider = ({ children }: { children: React.ReactNode }) => {
  return (
    <WsTrafficContextProvider>
      <TrafficRealTimeContextProvider>
        <GlobalInfoContextProvider>{children}</GlobalInfoContextProvider>
      </TrafficRealTimeContextProvider>
    </WsTrafficContextProvider>
  )
}
